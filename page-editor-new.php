<?php
/**
 * Template Name: Editor (New)
 *
 * Dedicated page for the Gutenberg editor interface using standalone Gutenberg
 *
 * @package BaumPress
 * @since 1.0.0
 */

get_header(); ?>

<div style="width: 100%; max-width: 1200px; margin: 0 auto; padding: 20px;">
  <div style="width: 100%;">
    
    <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 30px; color: var(--color-body-text);">Article Editor</h1>
    
    <?php
    // Check if user has permission to create posts
    if (!current_user_can('edit_posts')) {
      echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; color: #856404;">
              <h3 style="margin: 0 0 10px 0; color: #856404;">Permission Required</h3>
              <p style="margin: 0;">You need to be logged in with editor or contributor permissions to use this feature.</p>
            </div>';
    } else {
    ?>

    <div id="editor-container" style="background: white; border: 1px solid #e0e0e0; border-radius: 12px; padding: 30px; margin: 20px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">

      <!-- Article Metadata -->
      <div class="article-metadata" style="margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #f0f0f0;">
        
        <!-- Status and Featured Image Row -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
          
          <!-- Status -->
          <div>
            <label for="article-status" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Post Status</label>
            <select id="article-status" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius);">
              <option value="draft">Draft</option>
              <option value="pending">Pending Review</option>
              <?php if (current_user_can('publish_posts')): ?>
              <option value="publish">Publish</option>
              <?php endif; ?>
            </select>
          </div>

          <!-- Featured Image -->
          <div>
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Featured Image</label>
            <div id="featured-image-container" style="position: relative;">
              <div id="featured-image-preview" style="width: 100%; height: 80px; border: 2px dashed #ddd; border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; cursor: pointer; background: #f8f9fa; transition: all 0.2s ease;">
                <div style="text-align: center; color: #666;">
                  <i class="fas fa-image" style="font-size: 20px; margin-bottom: 4px; display: block;"></i>
                  <span style="font-size: 12px;">Click to upload</span>
                </div>
              </div>
              <input type="file" id="featured-image-input" accept="image/*" style="display: none;">
              <button type="button" id="remove-featured-image" style="position: absolute; top: 4px; right: 4px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer; display: none; color: #666;">
                <i class="fas fa-times" style="font-size: 12px;"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Categories and Tags Row -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          
          <!-- Categories Multi-Select -->
          <div>
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Categories</label>
            <div class="multi-select-dropdown" id="categories-dropdown" style="position: relative; width: 100%;">
              <div class="multi-select-display" style="min-height: 40px; padding: 8px 35px 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; background: white; display: flex; flex-wrap: wrap; gap: 4px; align-items: center;">
                <span class="multi-select-placeholder" style="color: #999; font-size: 14px;">Select categories...</span>
              </div>
              <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666; pointer-events: none;"></i>
              <div class="multi-select-options" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                <div class="multi-select-search-container" style="padding: 8px; border-bottom: 1px solid #eee;">
                  <input type="text" class="multi-select-search-input" placeholder="Search categories..." style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                </div>
                <?php
                $categories = get_categories(['hide_empty' => false]);
                foreach ($categories as $category) {
                  echo '<label class="multi-select-option" style="display: flex; align-items: center; padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">
                          <input type="checkbox" value="' . $category->term_id . '" style="margin-right: 8px;">
                          <span>📁 ' . $category->name . '</span>
                        </label>';
                }
                ?>
              </div>
            </div>
          </div>

          <!-- Tags Multi-Select -->
          <div>
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Tags</label>
            <div class="multi-select-dropdown" id="tags-dropdown" style="position: relative; width: 100%;">
              <div class="multi-select-display" style="min-height: 40px; padding: 8px 35px 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; background: white; display: flex; flex-wrap: wrap; gap: 4px; align-items: center;">
                <span class="multi-select-placeholder" style="color: #999; font-size: 14px;">Select tags...</span>
              </div>
              <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666; pointer-events: none;"></i>
              <div class="multi-select-options" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                <div class="multi-select-search-container" style="padding: 8px; border-bottom: 1px solid #eee;">
                  <input type="text" class="multi-select-search-input" placeholder="Search tags..." style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                </div>
                <?php
                $tags = get_tags(['hide_empty' => false]);
                foreach ($tags as $tag) {
                  echo '<label class="multi-select-option" style="display: flex; align-items: center; padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">
                          <input type="checkbox" value="' . $tag->term_id . '" style="margin-right: 8px;">
                          <span>🏷️ ' . $tag->name . '</span>
                        </label>';
                }
                ?>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Article Title (Growing Textarea) -->
      <div style="margin-bottom: 20px;">
        <label for="article-title" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Article Title</label>
        <div style="position: relative; width: 100%;">
          <textarea id="article-title" class="auto-grow-textarea" placeholder="Enter your article title..." style="width: 100%; min-height: 50px; max-height: 150px; padding: 12px; border: 1px solid #ddd; border-radius: var(--border-radius); resize: none; overflow-y: hidden; font-family: inherit; line-height: 1.4; font-size: 24px; font-weight: 700; color: var(--color-body-text); box-sizing: border-box;"></textarea>
        </div>
      </div>

      <!-- Gutenberg Editor Container -->
      <div class="gutenberg-editor-wrapper" style="margin-bottom: 30px;">
        <label style="display: block; margin-bottom: 12px; color: var(--color-body-text); font-size: 14px; font-weight: 600;">Article Content</label>
        <div style="border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; background: white;">
          <iframe 
            id="gutenberg-editor" 
            src="http://gutenberg-standalone.surge.sh/" 
            style="width: 100%; height: 600px; border: none; display: block;"
            title="Gutenberg Editor">
          </iframe>
        </div>
        <div style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
          <strong>📝 Real Gutenberg Editor:</strong> This is the actual WordPress Gutenberg editor running standalone. 
          All blocks and features work exactly like in WordPress admin. Content will be extracted when you save.
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="editor-actions" style="display: flex; justify-content: space-between; align-items: center; padding-top: 20px; border-top: 1px solid #f0f0f0;">
        <div style="display: flex; gap: 12px;">
          <button id="save-draft" class="button-secondary" style="padding: 12px 24px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; color: var(--color-body-text); font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;">
            💾 Save Draft
          </button>
          <button id="preview-article" class="button-secondary" style="padding: 12px 24px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; color: var(--color-body-text); font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;">
            👁️ Preview
          </button>
          <button id="get-content" class="button-secondary" style="padding: 12px 24px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; color: var(--color-body-text); font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;">
            📄 Get Content
          </button>
        </div>

        <div style="display: flex; gap: 12px;">
          <?php if (current_user_can('publish_posts')): ?>
          <button id="publish-article" class="button-primary" style="padding: 12px 24px; border: none; border-radius: 8px; background: var(--color-secondary); color: white; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease;">
            🚀 Publish Article
          </button>
          <?php else: ?>
          <button id="submit-review" class="button-primary" style="padding: 12px 24px; border: none; border-radius: 8px; background: var(--color-secondary); color: white; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease;">
            📝 Submit for Review
          </button>
          <?php endif; ?>
        </div>
      </div>
    </div>

    <?php } ?>
  </div>
</div>

<script>
// Multi-select dropdown functionality
function initializeMultiSelectDropdowns() {
  const dropdowns = document.querySelectorAll('.multi-select-dropdown');

  dropdowns.forEach(dropdown => {
    const display = dropdown.querySelector('.multi-select-display');
    const options = dropdown.querySelector('.multi-select-options');
    const placeholder = dropdown.querySelector('.multi-select-placeholder');
    const searchInput = dropdown.querySelector('.multi-select-search-input');
    const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');

    // Toggle dropdown
    display.addEventListener('click', () => {
      const isVisible = options.style.display === 'block';

      // Close all other dropdowns
      document.querySelectorAll('.multi-select-options').forEach(opt => {
        opt.style.display = 'none';
      });

      options.style.display = isVisible ? 'none' : 'block';
      if (!isVisible && searchInput) {
        searchInput.focus();
      }
    });

    // Search functionality
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const optionLabels = dropdown.querySelectorAll('.multi-select-option');

        optionLabels.forEach(label => {
          const text = label.textContent.toLowerCase();
          label.style.display = text.includes(searchTerm) ? 'flex' : 'none';
        });
      });
    }

    // Handle checkbox changes
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        updateMultiSelectDisplay(dropdown);
      });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!dropdown.contains(e.target)) {
        options.style.display = 'none';
      }
    });
  });
}

// Update multi-select display with selected items
function updateMultiSelectDisplay(dropdown) {
  const display = dropdown.querySelector('.multi-select-display');
  const placeholder = dropdown.querySelector('.multi-select-placeholder');
  const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');

  // Remove existing tags
  const existingTags = display.querySelectorAll('.selected-tag');
  existingTags.forEach(tag => tag.remove());

  if (checkboxes.length === 0) {
    placeholder.style.display = 'block';
  } else {
    placeholder.style.display = 'none';

    checkboxes.forEach(checkbox => {
      const label = checkbox.closest('.multi-select-option').querySelector('span').textContent;
      const tag = document.createElement('span');
      tag.className = 'selected-tag';
      tag.style.cssText = `
        background: var(--color-secondary);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
      `;
      tag.innerHTML = `
        ${label}
        <i class="fas fa-times" style="cursor: pointer; font-size: 10px;" onclick="removeMultiSelectTag(this, '${checkbox.value}')"></i>
      `;
      display.insertBefore(tag, placeholder);
    });
  }
}

// Remove tag from multi-select
function removeMultiSelectTag(element, value) {
  const dropdown = element.closest('.multi-select-dropdown');
  const checkbox = dropdown.querySelector(`input[value="${value}"]`);
  if (checkbox) {
    checkbox.checked = false;
    updateMultiSelectDisplay(dropdown);
  }
}

// Featured image upload functionality
function initializeFeaturedImageUpload() {
  const container = document.getElementById('featured-image-container');
  const preview = document.getElementById('featured-image-preview');
  const input = document.getElementById('featured-image-input');
  const removeBtn = document.getElementById('remove-featured-image');

  if (!container || !preview || !input) return;

  // Click to upload
  preview.addEventListener('click', () => {
    input.click();
  });

  // Handle file selection
  input.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        preview.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: var(--border-radius);">`;
        removeBtn.style.display = 'block';
      };
      reader.readAsDataURL(file);
    }
  });

  // Remove image
  if (removeBtn) {
    removeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      input.value = '';
      preview.innerHTML = `
        <div style="text-align: center; color: #666;">
          <i class="fas fa-image" style="font-size: 20px; margin-bottom: 4px; display: block;"></i>
          <span style="font-size: 12px;">Click to upload</span>
        </div>
      `;
      removeBtn.style.display = 'none';
    });
  }
}

// Auto-growing textarea functionality
function initializeAutoGrowTextarea() {
  const textarea = document.getElementById('article-title');
  if (!textarea) return;

  function adjustHeight() {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 150) + 'px';
  }

  textarea.addEventListener('input', adjustHeight);
  textarea.addEventListener('paste', () => {
    setTimeout(adjustHeight, 0);
  });

  // Initial adjustment
  adjustHeight();
}

// Get content from Gutenberg iframe
function getGutenbergContent() {
  const iframe = document.getElementById('gutenberg-editor');
  try {
    // Try to access iframe content (may be blocked by CORS)
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    const editorContent = iframeDoc.querySelector('.editor-writing-flow');
    if (editorContent) {
      return editorContent.innerHTML;
    }
  } catch (e) {
    console.log('Cannot access iframe content due to CORS policy');
  }
  
  // Fallback: show instructions to user
  alert('To get content from the Gutenberg editor:\n\n1. Select all content in the editor (Ctrl+A or Cmd+A)\n2. Copy it (Ctrl+C or Cmd+C)\n3. Click "Get Content" button again\n4. Paste when prompted');
  
  const userContent = prompt('Paste your content from the Gutenberg editor here:');
  return userContent || '';
}

// Save article functionality
function saveArticle(status = 'draft') {
  const title = document.getElementById('article-title').value;
  const content = getGutenbergContent();
  const articleStatus = document.getElementById('article-status').value;

  // Get selected categories and tags
  const categories = getMultiSelectValues('categories-dropdown');
  const tags = getMultiSelectValues('tags-dropdown');

  const articleData = {
    title: title,
    content: content,
    status: status || articleStatus,
    categories: categories,
    tags: tags,
    featured_image: document.getElementById('featured-image-input').files[0] || null
  };

  console.log('Saving article:', articleData);

  // Simulate save (replace with actual API call)
  setTimeout(() => {
    alert(`Article ${status === 'draft' ? 'saved as draft' : status === 'publish' ? 'published' : 'submitted for review'}!`);
  }, 1000);
}

// Helper function to get selected values from multi-select dropdown
function getMultiSelectValues(dropdownId) {
  const dropdown = document.getElementById(dropdownId);
  if (!dropdown) return [];

  const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');
  return Array.from(checkboxes).map(cb => cb.value);
}

// Initialize everything when DOM loads
document.addEventListener('DOMContentLoaded', function() {
  if (document.getElementById('editor-container')) {
    initializeMultiSelectDropdowns();
    initializeFeaturedImageUpload();
    initializeAutoGrowTextarea();

    // Add event listeners to action buttons
    const saveBtn = document.getElementById('save-draft');
    const previewBtn = document.getElementById('preview-article');
    const publishBtn = document.getElementById('publish-article') || document.getElementById('submit-review');
    const getContentBtn = document.getElementById('get-content');

    if (saveBtn) {
      saveBtn.addEventListener('click', () => saveArticle('draft'));
    }

    if (previewBtn) {
      previewBtn.addEventListener('click', () => {
        const title = document.getElementById('article-title').value;
        const content = getGutenbergContent();

        const previewWindow = window.open('', '_blank');
        previewWindow.document.write(`
          <html>
            <head>
              <title>Preview: ${title}</title>
              <style>
                body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 40px 20px; line-height: 1.6; }
                h1 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
                img { max-width: 100%; height: auto; }
                blockquote { border-left: 4px solid #ddd; margin: 20px 0; padding: 10px 20px; background: #f9f9f9; }
              </style>
            </head>
            <body>
              <h1>${title || 'Untitled Article'}</h1>
              <div>${content}</div>
            </body>
          </html>
        `);
      });
    }

    if (publishBtn) {
      publishBtn.addEventListener('click', () => {
        const status = publishBtn.id === 'publish-article' ? 'publish' : 'pending';
        saveArticle(status);
      });
    }

    if (getContentBtn) {
      getContentBtn.addEventListener('click', () => {
        const content = getGutenbergContent();
        console.log('Gutenberg content:', content);
        if (content) {
          alert('Content retrieved! Check the browser console for details.');
        }
      });
    }
  }
});
</script>

<?php get_footer(); ?>
