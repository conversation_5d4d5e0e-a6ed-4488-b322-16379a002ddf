<?php

// Make sure <PERSON><PERSON> is loaded
if ( ! class_exists( 'Kirki' ) ) return;

// Config
Kirki::add_config( 'baumpress_config', [
  'capability' => 'edit_theme_options',
  'option_type' => 'theme_mod',
]);

// Main Panel
Kirki::add_panel( 'baumpress_panel', [
  'priority' => 10,
  'title'    => esc_html__( 'BaumPress Settings', 'baumpress' ),
]);

//
// SECTION: General
//

Kirki::add_section( 'baumpress_theme_section', [
  'title'    => esc_html__( 'General', 'baumpress' ),
  'panel'    => 'baumpress_panel',
  'priority' => 10,
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'radio',
  'settings' => 'baum_theme',
  'label'    => 'Choose the main theme',
  'section'  => 'baumpress_theme_section',
  'choices'  => [
    'default' => 'Default',
    'ap-news' => 'AP News',
    'github'  => 'GitHub',
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'textarea',
  'settings' => 'baum_about',
  'label'    => 'A paragraph about the organization',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'text',
  'settings' => 'baum_name',
  'label'    => 'Admin Name',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'text',
  'settings' => 'baum_email',
  'label'    => 'Admin Email',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'text',
  'settings' => 'baum_position',
  'label'    => 'Admin Position',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'textarea',
  'settings' => 'baum_address',
  'label'    => 'Organization Address',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo',
  'label'    => 'Logo',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_dark',
  'label'    => 'Dark Logo',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_app',
  'label'    => 'App Logo',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_app_dark',
  'label'    => 'Dark App Logo',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_icon',
  'label'    => 'Icon',
  'section'  => 'baumpress_theme_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_icon_dark',
  'label'    => 'Dark Icon',
  'section'  => 'baumpress_theme_section',
]);

// Additional Logo Types for Comprehensive Branding
Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_black_on_white',
  'label'    => 'Logo - Black on White',
  'section'  => 'baumpress_theme_section',
  'description' => 'Logo optimized for white/light backgrounds',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_white_on_black',
  'label'    => 'Logo - White on Black',
  'section'  => 'baumpress_theme_section',
  'description' => 'Logo optimized for black/dark backgrounds',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_monochrome',
  'label'    => 'Logo - Monochrome',
  'section'  => 'baumpress_theme_section',
  'description' => 'Single color logo for versatile use',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_horizontal',
  'label'    => 'Logo - Horizontal Layout',
  'section'  => 'baumpress_theme_section',
  'description' => 'Wide format logo for headers and footers',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_vertical',
  'label'    => 'Logo - Vertical Layout',
  'section'  => 'baumpress_theme_section',
  'description' => 'Tall format logo for sidebars and narrow spaces',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_square',
  'label'    => 'Logo - Square Format',
  'section'  => 'baumpress_theme_section',
  'description' => 'Square logo for social media and app icons',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_favicon',
  'label'    => 'Logo - Favicon',
  'section'  => 'baumpress_theme_section',
  'description' => 'Small icon for browser tabs (16x16 or 32x32)',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_watermark',
  'label'    => 'Logo - Watermark',
  'section'  => 'baumpress_theme_section',
  'description' => 'Transparent/subtle logo for overlays',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_print',
  'label'    => 'Logo - Print Version',
  'section'  => 'baumpress_theme_section',
  'description' => 'High contrast logo optimized for printing',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'image',
  'settings' => 'baum_logo_email',
  'label'    => 'Logo - Email Header',
  'section'  => 'baumpress_theme_section',
  'description' => 'Logo for email templates and newsletters',
]);


// <?php
// // Exit if accessed directly
// if ( ! defined( 'ABSPATH' ) ) {
//     exit;
// }

// // Bail if Kirki isn't loaded
// if ( ! class_exists( 'Kirki' ) ) return;

// // Config
// Kirki::add_config( 'baumpress_config', [
//     'capability' => 'edit_theme_options',
//     'option_type' => 'theme_mod',
// ]);

// // Panel
// Kirki::add_panel( 'baumpress_panel', [
//     'priority' => 10,
//     'title'    => esc_html__( 'BaumPress Settings', 'baumpress' ),
// ]);

// //
// // SECTION: General
// //
// Kirki::add_section( 'baumpress_theme_section', [
//     'title'    => 'General',
//     'panel'    => 'baumpress_panel',
//     'priority' => 10,
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'radio',
//     'settings' => 'baum_theme',
//     'label'    => 'Choose the main theme',
//     'section'  => 'baumpress_theme_section',
//     'choices'  => [
//         'default' => 'Default',
//         'ap-news' => 'AP News',
//         'github'  => 'GitHub',
//     ],
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'textarea',
//     'settings' => 'baum_about',
//     'label'    => 'About the organization',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'text',
//     'settings' => 'baum_name',
//     'label'    => 'Admin Name',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'text',
//     'settings' => 'baum_email',
//     'label'    => 'Admin Email',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'text',
//     'settings' => 'baum_position',
//     'label'    => 'Admin Position',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'textarea',
//     'settings' => 'baum_address',
//     'label'    => 'Organization Address',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'image',
//     'settings' => 'baum_logo',
//     'label'    => 'Logo',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'image',
//     'settings' => 'baum_logo_dark',
//     'label'    => 'Dark Logo',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'image',
//     'settings' => 'baum_logo_app',
//     'label'    => 'App Logo',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'image',
//     'settings' => 'baum_logo_app_dark',
//     'label'    => 'Dark App Logo',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'image',
//     'settings' => 'baum_icon',
//     'label'    => 'Icon',
//     'section'  => 'baumpress_theme_section',
// ]);

// Kirki::add_field( 'baumpress_config', [
//     'type'     => 'image',
//     'settings' => 'baum_icon_dark',
//     'label'    => 'Dark Icon',
//     'section'  => 'baumpress_theme_section',
// ]);

//
// SECTION: Social
//

Kirki::add_section( 'baumpress_social_section', [
  'title'    => 'Social',
  'panel'    => 'baumpress_panel',
  'priority' => 20,
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'text',
  'settings' => 'baum_social_facebook',
  'label'    => 'Facebook URL',
  'section'  => 'baumpress_social_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'text',
  'settings' => 'baum_social_twitter',
  'label'    => 'Twitter / X URL',
  'section'  => 'baumpress_social_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'text',
  'settings' => 'baum_social_linkedin',
  'label'    => 'LinkedIn URL',
  'section'  => 'baumpress_social_section',
]);

//
// SECTION: Footer
//

Kirki::add_section( 'baumpress_footer_section', [
  'title'    => 'Footer',
  'panel'    => 'baumpress_panel',
  'priority' => 30,
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'textarea',
  'settings' => 'baum_footer_shortcode',
  'label'    => 'Shortcode before footer',
  'section'  => 'baumpress_footer_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'textarea',
  'settings' => 'baum_footer_description',
  'label'    => 'Footer Description',
  'section'  => 'baumpress_footer_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'textarea',
  'settings' => 'baum_copyright',
  'label'    => 'Copyright Notice',
  'section'  => 'baumpress_footer_section',
]);

//
// SECTION: Style & Layout
//

Kirki::add_section( 'baumpress_style_section', [
  'title'    => 'Style & Layout',
  'panel'    => 'baumpress_panel',
  'priority' => 40,
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'checkbox',
  'settings' => 'baum_sidebar_hide',
  'label'    => 'Hide the left navigation sidebar by default',
  'section'  => 'baumpress_style_section',
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'radio',
  'settings' => 'baum_masthead_sub_heading',
  'label'    => 'Display beneath the masthead logo',
  'section'  => 'baumpress_style_section',
  'choices'  => [
    'date'    => 'Current Date',
    'tagline' => 'Site Tagline',
    'random'  => 'Randomize',
    'false'   => 'Nothing',
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'radio',
  'settings' => 'baum_bookmark_icon',
  'label'    => 'Bookmark Icon',
  'section'  => 'baumpress_style_section',
  'choices'  => [
    'bookmark'  => 'Bookmark',
    'gem'       => 'Gem',
    'heart'     => 'Heart',
    'fire'      => 'Fire',
    'star'      => 'Star',
    'thumbs-up' => 'Thumbs-up',
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'radio',
  'settings' => 'baum_breaking_news_location',
  'label'    => 'Breaking Alert Location',
  'section'  => 'baumpress_style_section',
  'choices'  => [
    'above' => 'Above Masthead',
    'below' => 'Below Masthead',
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'radio',
  'settings' => 'baum_breaking_news_pages',
  'label'    => 'Breaking Alert Visibility',
  'section'  => 'baumpress_style_section',
  'choices'  => [
    'none'   => 'None',
    'home'   => 'Homepage only',
    'single' => 'Homepage + Posts',
    'all'    => 'All Pages',
    ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'radio',
  'settings' => 'baum_breaking_news_cron',
  'label'    => 'Breaking Alert Expiration',
  'section'  => 'baumpress_style_section',
  'choices'  => [
    5   => '5 Minutes',
    30  => '30 Minutes',
    90  => '90 Minutes',
    180 => '180 Minutes',
    360 => '6 Hours',
    720 => '12 Hours',
  ],
]);

//
// SECTION: Typography
//

Kirki::add_section( 'baumpress_typography_section', [
  'title'    => 'Typography',
  'panel'    => 'baumpress_panel',
  'priority' => 50,
]);

// Kirki::add_field( 'baumpress_config', [
//   'type'        => 'typography',
//   'settings'    => 'baum_font_body',
//   'label'       => esc_html__( 'Body Font', 'baumpress' ),
//   'section'     => 'baumpress_typography_section',
//   'default'     => [
//       'font-family'    => 'Arial',
//       'variant'        => 'regular',
//       'font-size'      => '16px',
//       'line-height'    => '1.6',
//       'letter-spacing' => '0',
//   ],
//   'output' => [
//       [
//           'element' => 'body',
//       ],
//   ],
// ]);

// Kirki::add_field( 'baumpress_config', [
//   'type'        => 'typography',
//   'settings'    => 'baum_font_headings',
//   'label'       => esc_html__( 'Heading Font', 'baumpress' ),
//   'section'     => 'baumpress_typography_section',
//   'default'     => [
//       'font-family'    => 'Georgia',
//       'variant'        => '700',
//       'font-size'      => '28px',
//       'line-height'    => '1.2',
//   ],
//   'output' => [
//       [
//           'element' => 'h1, h2, h3, h4, h5, h6',
//       ],
//   ],
// ]);

//
// SECTION: Colors
//

Kirki::add_section( 'baumpress_colors_section', [
  'title'    => 'Color Palette',
  'panel'    => 'baumpress_panel',
  'priority' => 60,
]);

/*
// COMMENTED OUT: Use theme-based color system instead of individual color pickers
// These basic color settings are replaced by the comprehensive theme system below

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_primary',
  'label'    => 'Primary Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '#d32f2f',
  'output'   => [
    [
      'element'  => ':root',
      'property' => '--color-primary',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_accent',
  'label'    => 'Accent Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '#ff9800',
  'output'   => [
    [
      'element'  => ':root',
      'property' => '--color-accent',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_background',
  'label'    => 'Background Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '#ffffff',
  'output'   => [
    [
      'element'  => 'body',
      'property' => 'background-color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_text',
  'label'    => 'Text Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '#222222',
  'output'   => [
    [
      'element'  => 'body',
      'property' => 'color',
    ],
  ],
]);
*/

// Navigation & Header Colors
Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_masthead_bg',
  'label'    => 'Masthead Background Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '#ffffff',
  'output'   => [
    [
      'element'  => '.navbar-masthead', // '.baum-masthead, .navbar-top',
      'property' => 'background-color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_nav_bg',
  'label'    => 'Main Navigation Background',
  'section'  => 'baumpress_colors_section',
  'default'  => '#2c3e50',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => '#navbar', // .navbar-center, .main-nav, .baum-main-nav',
      'property' => 'background-color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_nav_text',
  'label'    => 'Main Navigation Text Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '#ffffff',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => '.navbar-center a, .main-nav a, .baum-main-nav a, .navbar-center .menu-item a, .baum-apple-menu > li > a',
      'property' => 'color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_nav_text_hover',
  'label'    => 'Main Navigation Text Hover Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => '.navbar-center a:hover, .main-nav a:hover, .baum-main-nav a:hover, .navbar-center .menu-item a:hover',
      'property' => 'color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_footer_bg',
  'label'    => 'Footer Background Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '#34495e',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => 'footer, .baum-footer',
      'property' => 'background-color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_footer_text',
  'label'    => 'Footer Text Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '#ffffff',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => 'footer, footer p, footer a, .baum-footer, .baum-footer p, .baum-footer a',
      'property' => 'color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_footer_link_hover',
  'label'    => 'Footer Link Hover Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => 'footer a:hover, .baum-footer a:hover',
      'property' => 'color',
    ],
  ],
]);

// Category Header Navigation Colors
Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_category_nav_bg',
  'label'    => 'Category Navigation Background',
  'section'  => 'baumpress_colors_section',
  'default'  => '',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => '.small-navbar, .categories-nav',
      'property' => 'background-color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_category_nav_text',
  'label'    => 'Category Navigation Text Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => '.categories-nav ul li a, .category-item a',
      'property' => 'color',
    ],
  ],
]);

Kirki::add_field( 'baumpress_config', [
  'type'     => 'color',
  'settings' => 'baum_color_category_nav_text_hover',
  'label'    => 'Category Navigation Text Hover Color',
  'section'  => 'baumpress_colors_section',
  'default'  => '',
  'choices'  => [
    'alpha' => true,
  ],
  'output'   => [
    [
      'element'  => '.categories-nav ul li a:hover, .category-item a:hover, .category-item.active a',
      'property' => 'color',
    ],
  ],
]);




















Kirki::add_config('baum_config', [
  'capability'  => 'edit_theme_options',
  'option_type' => 'theme_mod',
]);

Kirki::add_section('baum_images_settings', [
  'title'       => __('Images Settings'),
  'description' => __('Configure image settings.'),
  'priority'    => 160,
]);

Kirki::add_field('baum_config', [
  'type'        => 'number',
  'settings'    => 'max_featured_images',
  'label'       => __('Max Featured Images'),
  'section'     => 'baum_images_settings',
  'default'     => 5,
  'choices'     => [
    'min'  => 1,
    'max'  => 25,
    'step' => 1,
  ],
]);

Kirki::add_config('baum_config', [
  'capability' => 'edit_theme_options',
  'option_type' => 'theme_mod',
]);

Kirki::add_section('baum_fonts', [
  'title'       => __('Baum Custom Fonts'),
  'description' => __('Select font settings for headings and important text'),
  'priority'    => 160,
]);

Kirki::add_field('baum_config', [
  'type'        => 'select',
  'settings'    => 'heading_font',
  'label'       => __('Heading Font'),
  'section'     => 'baum_fonts',
  'default'     => 'system',
  'choices'     => $baum_font_choices,
]);

Kirki::add_field('baum_config', [
  'type'        => 'select',
  'settings'    => 'heading_text_transform',
  'label'       => __('Heading Text Transform'),
  'section'     => 'baum_fonts',
  'default'     => 'none',
  'choices'     => [
    'none'           => 'None',
    'capitalize'     => 'Capitalize',
    'uppercase'      => 'Uppercase',
    'lowercase'      => 'Lowercase'
  ],
]);

Kirki::add_field('baum_config', [
  'type'        => 'slider',
  'settings'    => 'heading_letter_spacing',
  'label'       => __('Heading Letter Spacing (em)'),
  'section'     => 'baum_fonts',
  'default'     => 0, // Default letter spacing in px
  'choices'     => [
    'min'  => -1,    // Minimum value
    'max'  => 1,     // Maximum value
    'step' => 0.001,   // Increment
  ],
]);

Kirki::add_section('baum_custom_theme', [
  'title'       => esc_html__('Baum Custom Theme', 'baum'),
  'description' => esc_html__('Customize Baum Theme Settings', 'baum'),
  'priority'    => 120,
]);

Kirki::add_field('baum_config', [
  'type'        => 'color',
  'settings'    => 'baum_site_title_color',
  'label'       => esc_html__('Color of the Site Title', 'baum'),
  'section'     => 'baum_custom_theme',
  'default'     => '#222222',
]);

Kirki::add_section('baum_theme_style', [
  'title'       => __('Baum Custom Style'),
  'description' => __('Select settings for important styles.'),
  'priority'    => 160,
]);



// Load the themes from JSON
$color_themes = include get_stylesheet_directory() . '/inc/baum-color-themes.php';

// Transform the theme keys into an associative array for the dropdown
$theme_choices = array_combine(
  array_keys($color_themes['themes']),
  array_map('ucfirst', array_keys($color_themes['themes'])) // Capitalize labels for display
);

// Add a slider to choose a theme
Kirki::add_field('baum_config', [
  'type'        => 'select',
  'settings'    => 'baum_custom_theme_selector',
  'label'       => esc_html__('Select Theme', 'baum'),
  'section'     => 'baum_custom_theme',
  'default'     => 'default',
  'choices'     => $theme_choices,
]);

Kirki::add_field('baum_config', [
  'type'        => 'select',
  'settings'    => 'baum_dark_mode',
  'label'       => esc_html__('Dark Mode', 'baum'),
  'section'     => 'baum_custom_theme',
  'default'     => 'auto',
  'choices'     => [
    'auto' => esc_html__('Auto (System Preference)', 'baum'),
    'on'   => esc_html__('On (Always Dark)', 'baum'),
    'off'  => esc_html__('Off (Always Light)', 'baum'),
  ],
]);

// Dynamically generate fields for light and dark colors
foreach ($color_themes['themes'] as $theme_name => $modes) {
  foreach ($modes as $mode => $colors) {
    foreach ($colors as $key => $value) {
      if ($key === 'color' && is_array($value)) {
        foreach ($value as $sub_key => $sub_value) {
          Kirki::add_field('baum_config', [
            'type'        => 'color',
            'settings'    => "baum_{$theme_name}_{$mode}_color_{$sub_key}",
            'label'       => ucfirst($sub_key) . ' (' . ucfirst($mode) . ')',
            'section'     => 'baum_custom_theme',
            'default'     => $sub_value,
            'transport'   => 'auto',
            'active_callback' => [
              [
                'setting'  => 'baum_custom_theme_selector',
                'operator' => '==',
                'value'    => $theme_name,
              ],
            ],
          ]);
        }
      } else {
          // Add regular color and corresponding background color
        Kirki::add_field('baum_config', [
          'type'        => 'color',
          'settings'    => "baum_{$theme_name}_{$mode}_{$key}",
          'label'       => ucfirst($key) . ' (' . ucfirst($mode) . ')',
          'section'     => 'baum_custom_theme',
          'default'     => $value,
          'transport'   => 'auto',
          'active_callback' => [
            [
              'setting'  => 'baum_custom_theme_selector',
              'operator' => '==',
              'value'    => $theme_name,
            ],
          ],
        ]);
      }
    }
  }
}

function baum_customize_css() {
  $theme = get_theme_mod('baum_custom_theme_selector', 'default');
  $dark_mode = get_theme_mod('baum_darkmode', 'auto'); // 'auto', 'on', or 'off'

  // Include the themes configuration
  $color_themes = include get_stylesheet_directory() . '/inc/baum-color-themes.php';
  $theme_data = $color_themes['themes'][$theme] ?? $color_themes['themes']['default'];

  // Initialize CSS
  $css = ':root {';

  // Generate CSS for light mode
  foreach ($theme_data['light'] as $key => $value) {
    if (is_array($value)) {
      foreach ($value as $sub_key => $sub_value) {
        $custom_value = get_theme_mod("baum_{$theme}_light_color_{$sub_key}", $sub_value);
        $css .= "--color-{$sub_key}: {$custom_value};";
        $css .= "--background-{$sub_key}: " . adjust_alpha($custom_value, 0.77) . ";";
      }
    } else {
      $custom_value = get_theme_mod("baum_{$theme}_light_{$key}", $value);
      $css .= "--color-{$key}: {$custom_value};";
      $css .= "--background-{$key}: " . adjust_alpha($custom_value, 0.77) . ";";
    }
  }
  $css .= '}';

  // Generate CSS for dark mode if enabled
  if ($dark_mode === 'auto' || $dark_mode === 'on') {
    $css .= '@media (prefers-color-scheme: dark) { :root {';
    foreach ($theme_data['dark'] as $key => $value) {
      if (is_array($value)) {
        foreach ($value as $sub_key => $sub_value) {
          $custom_value = get_theme_mod("baum_{$theme}_dark_color_{$sub_key}", $sub_value);
          $css .= "--color-{$sub_key}: {$custom_value};";
          $css .= "--background-{$sub_key}: " . adjust_alpha($custom_value, 0.77) . ";";
        }
      } else {
        $custom_value = get_theme_mod("baum_{$theme}_dark_{$key}", $value);
        $css .= "--color-{$key}: {$custom_value};";
        $css .= "--background-{$key}: " . adjust_alpha($custom_value, 0.77) . ";";
      }
    }
    $css .= '} }';
  }

  // Output the CSS
  echo "<style>{$css}</style>";
}

add_action('wp_head', 'baum_customize_css');
add_action('admin_head', 'baum_customize_css');

function convert_to_rgba($hex, $alpha) {
  $hex = str_replace('#', '', $hex);

  // Handle short-hand HEX format (e.g., #abc)
  if (strlen($hex) === 3) {
    $r = hexdec(str_repeat(substr($hex, 0, 1), 2));
    $g = hexdec(str_repeat(substr($hex, 1, 1), 2));
    $b = hexdec(str_repeat(substr($hex, 2, 1), 2));
  } else {
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
  }
  return "rgba($r, $g, $b, $alpha)";
}

function adjust_alpha($hex_color, $alpha = 1) {
  $hex_color = ltrim($hex_color, '#');
  $r = hexdec(substr($hex_color, 0, 2));
  $g = hexdec(substr($hex_color, 2, 2));
  $b = hexdec(substr($hex_color, 4, 2));
  return "rgba($r, $g, $b, {$alpha})";
}

//
// Load colors from the centralized colors file
//

function baum_get_colors() {
  return include get_template_directory() . '/inc/colors.php';
}

function baum_add_gutenberg_colors() {
  $colors = baum_get_colors();
  $palette = [];

  // Build Gutenberg color palette from centralized color array
  foreach ($colors as $slug => $color) {
    $palette[] = [
      'name'  => ucfirst($slug),
      'slug'  => $slug,
      'color' => $color,
    ];
  }

  add_theme_support('editor-color-palette', $palette);
}

add_action('after_setup_theme', 'baum_add_gutenberg_colors');




























// add_action('customize_register', function ($wp_customize) {

// //
// // Add Baum Custom Theme section
// //
// $wp_customize->add_section('baum_custom_theme', array(
//   'title' => 'Baum Custom Theme',
//   'description' => '',
//   'priority' => 120,
// ));

// $wp_customize->add_setting('baum_site_title_color');
// $wp_customize->add_setting( 'baum_site_title_color', array(
//   'default' => '#222222',
// ));

// $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_site_title_color', array(
//   'label' => 'Color of the site title:',
//   'section' => 'baum_custom_theme',
//   'settings' => 'baum_site_title_color'
// )));


  // error_log('Kirki class exists' . print_r(new Kirki, true));

  // error_log(print_r($color_themes, true));

  // // Add the color theme section
  // Kirki::add_section('baum_color_theme', [
  //     'title'       => esc_html__('Baum\'s Color Themes', 'baum'),
  //     'description' => esc_html__('Choose a color theme', 'baum'),
  //     'priority'    => 120,
  // ]);















// //
// // Create Settings
// //

// function baum_customizer_settings ($wp_customize) {

//   // add a setting for the theme radio selection
//   $wp_customize->add_setting('baum_theme');

//   // Add a control to change the theme style
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_theme', [
//       'label' => 'Choose the main theme',
//       'section' => 'baum_custom_theme',
//       'settings' => 'baum_theme',
//       'type' => 'radio',
//       'choices' => [
//         'default' => 'Default',
//         'ap-news' => 'AP News',
//         'github' => 'GitHub',
//       ]
//     ]
//   ));

//   $wp_customize->add_setting('baum_about', [
//     // 'capability' => 'edit_theme_options',
//     'sanitize_callback' => 'sanitize_textarea_field',
//   ]);

//   $wp_customize->add_control('baum_about', [
//     'type' => 'textarea',
//     'section' => 'title_tagline',
//     'label' => 'A paragraph about the organization:',
//     'description' => '',
//   ]);

//   // add a setting for the baum name
//   $wp_customize->add_setting('baum_name');

//   // Add a control to change the baum name
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_name', array(
//     'label' => 'Admin Name',
//     'section' => 'title_tagline',
//     'settings' => 'baum_name',
//     'type' => 'text'
//   )));

//   // add a setting for the baum email
//   $wp_customize->add_setting('baum_email');

//   // Add a control to change the baum email
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_email', array(
//     'label' => 'Admin Email',
//     'section' => 'title_tagline',
//     'settings' => 'baum_email',
//     'type' => 'text'
//   )));

//   // add a setting for the twitter link
//   $wp_customize->add_setting('baum_position');

//   // Add a control to change the twitter link
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_position', array(
//     'label' => 'Admin Position:',
//     'section' => 'title_tagline',
//     'settings' => 'baum_position',
//     'type' => 'text'
//   )));

//   $wp_customize->add_setting('baum_address', [
//     'sanitize_callback' => 'sanitize_textarea_field',
//   ]);

//   $wp_customize->add_control('baum_address', [
//     'type' => 'textarea',
//     'section' => 'title_tagline',
//     'label' => 'Organization address:',
//     'description' => '',
//   ]);

//   // add a setting for the app logo
//   $wp_customize->add_setting('baum_logo_app');

//   // Add a control to upload the app logo
//   $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'baum_logo_app', [
//     'label' => 'App Logo:',
//     'section' => 'title_tagline',
//     'settings' => 'baum_logo_app'
//   ]));

//   // add a setting for the app logo
//   $wp_customize->add_setting('baum_logo_app_dark');

//   // Add a control to upload the app logo
//   $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'baum_logo_app_dark', [
//     'label' => 'Dark App Logo:',
//     'section' => 'title_tagline',
//     'settings' => 'baum_logo_app_dark'
//   ]));

//   // add a setting for the site logo
//   $wp_customize->add_setting('baum_logo');

//   // Add a control to upload the logo
//   $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'baum_logo', [
//     'label' => 'Logo:',
//     'section' => 'title_tagline',
//     'settings' => 'baum_logo'
//   ]));

//   // add a setting for the dark logo
//   $wp_customize->add_setting('baum_logo_dark');

//   // Add a control to upload the logo
//   $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'baum_logo_dark', [
//     'label' => 'Dark Logo:',
//     'section' => 'title_tagline',
//     'settings' => 'baum_logo_dark'
//   ]));

//   // add a setting for the icon
//   $wp_customize->add_setting('baum_icon');

//   // Add a control to upload the icon
//   $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'baum_icon', [
//     'label' => 'Icon:',
//     'section' => 'title_tagline',
//     'settings' => 'baum_icon'
//   ]));

//   // add a setting for the dark icon
//   $wp_customize->add_setting('baum_icon_dark');

//   // Add a control to upload the icon
//   $wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'baum_icon_dark', [
//     'label' => 'Dark Icon:',
//     'section' => 'title_tagline',
//     'settings' => 'baum_icon_dark'
//   ]));

//   //
//   // Add Social Section
//   //

//   $wp_customize->add_section('baum_theme_social', array(
//     'title' => 'Social',
//     'description' => '',
//     'priority' => 120,
//   ));

//   // add a setting for the facebook link
//   $wp_customize->add_setting('baum_social_facebook');

//   // Add a control to change the facebook link
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_social_facebook', array(
//     'label' => 'Facebook URL:',
//     'section' => 'baum_theme_social',
//     'settings' => 'baum_social_facebook',
//     'type' => 'text'
//   )));

//   // add a setting for the twitter link
//   $wp_customize->add_setting('baum_social_twitter');

//   // Add a control to change the twitter link
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_social_twitter', array(
//     'label' => 'Twitter / X URL:',
//     'section' => 'baum_theme_social',
//     'settings' => 'baum_social_twitter',
//     'type' => 'text'
//   )));

//   // add a setting for the twitter link
//   $wp_customize->add_setting('baum_social_linkedin');

//   // Add a control to change the twitter link
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_social_linkedin', array(
//     'label' => 'LinkedIn URL:',
//     'section' => 'baum_theme_social',
//     'settings' => 'baum_social_twitter',
//     'type' => 'text'
//   )));

//   //
//   // Add Footer Section
//   //

//   $wp_customize->add_section('baum_theme_footer', array(
//     'title' => 'Footer',
//     'description' => '',
//     'priority' => 120,
//   ));

//   // add a setting for the footer description
//   $wp_customize->add_setting('baum_footer_shortcode');

//   // Add a control to change the description
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_footer_shortcode', array(
//     'label' => 'Shortcode before footer',
//     'section' => 'baum_theme_footer',
//     'settings' => 'baum_footer_shortcode',
//     'type' => 'textarea'
//   )));

//   // add a setting for the footer description
//   $wp_customize->add_setting('baum_footer_description');

//   // Add a control to change the description
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_footer_description', array(
//     'label' => 'Description',
//     'section' => 'baum_theme_footer',
//     'settings' => 'baum_footer_description',
//     'type' => 'textarea'
//   )));

//   // add a setting for the footer description
//   $wp_customize->add_setting('baum_copyright');

//   // Add a control to change the description
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_copyright', array(
//     'label' => 'Copyright',
//     'section' => 'baum_theme_footer',
//     'settings' => 'baum_copyright',
//     'type' => 'textarea'
//   )));

//   $wp_customize->add_setting('baum_sidebar_hide');

//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_sidebar_hide', array(
//     'label' => 'Hide the left navigation sidebar by default:',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_sidebar_hide',
//     'type' => 'checkbox'
//   )));


//    // add a setting for the radio selection
//    $wp_customize->add_setting('baum_masthead_sub_heading', [
//     'default' => 180
//   ]);

//   // Add a control to change the duration of the breaking alert
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_masthead_sub_heading', array(
//     'label' => 'Display beneath the logo on the masthead:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_masthead_sub_heading',
//     'type' => 'radio',
//     'choices' => [
//       'date' => 'Display current date',
//       'tagline' => 'Display tagline',
//       'random' => 'Randomize date and tagline',
//       'false' => 'Display nothing'
//     ]
//   )));

//   // add a setting for the bookmark radio selection
//   $wp_customize->add_setting('baum_bookmark_icon', [ 'default' => 'bookmark' ]);

//   // Add a control to change the bookmark style
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_bookmark_icon', array(
//     'label' => 'Choose the icon for the bookmarks',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_bookmark_icon',
//     'type' => 'radio',
//     'choices' => [
//       'bookmark' => 'Bookmark icon',
//       'gem' => 'Gem icon',
//       'heart' => 'Heart icon',
//       'fire' => 'Fire icon',
//       'star' => 'Star icon',
//       'thumbs-up' => 'Thumbs-up icon',
//     ]
//   )));

//   // add a setting for the radio selection
//   $wp_customize->add_setting('baum_breaking_news_location', [
//     'default' => 'above'
//   ]);

//   // Add a control to change the location of the breaking news alert
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_breaking_news_location', array(
//     'label' => 'Location of the "breaking" story alert:',
//     'description' => 'When you tag a post with "breaking" it will appear in a special place above or below the masthead of the site.',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_breaking_news_location',
//     'type' => 'radio',
//     'choices' => [
//       'above' => 'Above masthead',
//       'below' => 'Below masthead'
//     ]
//   )));

//   // add a setting for the radio selection
//   $wp_customize->add_setting('baum_breaking_news_pages', [
//     'default' => 'home'
//   ]);

//   // Add a control to change the location of the latest alert
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_breaking_news_pages', array(
//     'label' => 'Pages the "breaking" story alert appears:',
//     'description' => 'You can choose which pages the "breaking" story alert will appear.',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_breaking_news_pages',
//     'type' => 'radio',
//     'choices' => [
//       'none' => 'Do not display it at all',
//       'home' => 'Display it only on the homepage.',
//       'single' => 'Display it on the homepage and post / story pages.',
//       'all' => 'Display it on all pages'
//     ]
//   )));



//    // add a setting for the radio selection
//    $wp_customize->add_setting('baum_breaking_news_cron', [
//     'default' => 180
//   ]);

//   // Add a control to change the duration of the breaking alert
//   $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_breaking_news_cron', array(
//     'label' => 'Breaking story alert expiration:',
//     'description' => 'The breaking news alert expires automatically after a specified amount of time.',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_breaking_news_cron',
//     'type' => 'radio',
//     'choices' => [
//       5 => '5min',
//       30 => '30min',
//       90 => '90min',
//       180 => '180min',
//       360 => '360min',
//       720 => '720min'
//     ]
//   )));

// }

// add_action('customize_register', 'baum_customizer_settings');










// function unused () {

//   //
//   // Gray
//   //
//   $wp_customize->add_setting('baum_gray_color');
//   $wp_customize->add_setting( 'baum_gray_color', array(
//     'default' => $color_gray,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_gray_color', array(
//     'label' => 'Gray:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_gray_color'
//   )));

//   //
//   // Black
//   //
//   $wp_customize->add_setting('baum_black_color');
//   $wp_customize->add_setting( 'baum_black_color', array(
//     'default' => $color_black,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_black_color', array(
//     'label' => 'Black:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_black_color'
//   )));

//   //
//   // Pink
//   //
//   $wp_customize->add_setting('baum_pink_color');
//   $wp_customize->add_setting( 'baum_pink_color', array(
//     'default' => $color_pink,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_pink_color', array(
//     'label' => 'Pink:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_pink_color'
//   )));

//   //
//   // Red
//   //
//   $wp_customize->add_setting('baum_red_color');
//   $wp_customize->add_setting( 'baum_red_color', array(
//     'default' => $color_red,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_red_color', array(
//     'label' => 'Red:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_red_color'
//   )));

//   //
//   // Purple
//   //
//   $wp_customize->add_setting('baum_purple_color');
//   $wp_customize->add_setting( 'baum_purple_color', array(
//     'default' => $color_purple,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_purple_color', array(
//     'label' => 'Purple:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_purple_color'
//   )));

//   //
//   // Blue
//   //
//   $wp_customize->add_setting('baum_blue_color');
//   $wp_customize->add_setting( 'baum_blue_color', array(
//     'default' => $color_blue,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_blue_color', array(
//     'label' => 'Blue:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_blue_color'
//   )));

//   //
//   // Green
//   //
//   $wp_customize->add_setting('baum_green_color');
//   $wp_customize->add_setting( 'baum_green_color', array(
//     'default' => $color_green,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_green_color', array(
//     'label' => 'Green:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_green_color'
//   )));

//   //
//   // Teal
//   //
//   $wp_customize->add_setting('baum_teal_color');
//   $wp_customize->add_setting( 'baum_teal_color', array(
//     'default' => $color_teal,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_teal_color', array(
//     'label' => 'Teal:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_teal_color'
//   )));

//   //
//   // Yellow
//   //
//   $wp_customize->add_setting('baum_yellow_color');
//   $wp_customize->add_setting( 'baum_yellow_color', array(
//     'default' => $color_yellow,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_yellow_color', array(
//     'label' => 'Yellow:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_yellow_color'
//   )));

//   //
//   // Orange
//   //
//   $wp_customize->add_setting('baum_orange_color');
//   $wp_customize->add_setting( 'baum_orange_color', array(
//     'default' => $color_orange,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_orange_color', array(
//     'label' => 'Orange:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_orange_color'
//   )));

//   //
//   // Primary
//   //
//   $wp_customize->add_setting('baum_primary_color');
//   $wp_customize->add_setting( 'baum_primary_color', array(
//     'default' => $color_primary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_primary_color', array(
//     'label' => 'Primary color:',
//     'description' => 'NOTE: White text will go on top of this color, so choose one dark enough.',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_primary_color'
//   )));

//   //
//   // Secondary
//   //
//   $wp_customize->add_setting('baum_secondary_color');
//   $wp_customize->add_setting( 'baum_secondary_color', array(
//     'default' => $color_secondary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_secondary_color', array(
//     'label' => 'Secondary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_secondary_color'
//   )));

//   //
//   // Tertiary
//   //
//   $wp_customize->add_setting('baum_tertiary_color');
//   $wp_customize->add_setting( 'baum_tertiary_color', array(
//     'default' => $color_tertiary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_tertiary_color', array(
//     'label' => 'Tertiary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_tertiary_color'
//   )));

//   //
//   // Quaternary
//   //
//   $wp_customize->add_setting('baum_quaternary_color');
//   $wp_customize->add_setting('baum_quaternary_color', array(
//     'default' => $color_quaternary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_quaternary_color', array(
//     'label' => 'Quaternary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_quaternary_color'
//   )));

//   //
//   // Quinary
//   //
//   $wp_customize->add_setting('baum_quinary_color');
//   $wp_customize->add_setting( 'baum_quinary_color', array(
//     'default' => $color_quinary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_quinary_color', array(
//     'label' => 'Quinary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_quinary_color'
//   )));

//   //
//   // Senary
//   //
//   $wp_customize->add_setting('baum_senary_color');
//   $wp_customize->add_setting( 'baum_senary_color', array(
//     'default' => $color_senary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_senary_color', array(
//     'label' => 'Senary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_senary_color'
//   )));

//   //
//   // Septenary
//   //
//   $wp_customize->add_setting('baum_septenary_color');
//   $wp_customize->add_setting( 'baum_septenary_color', array(
//     'default' => $color_septenary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_septenary_color', array(
//     'label' => 'Septenary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_septenary_color'
//   )));

//   //
//   // Octonary
//   //
//   $wp_customize->add_setting('baum_octonary_color');
//   $wp_customize->add_setting( 'baum_octonary_color', array(
//     'default' => $color_octonary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_octonary_color', array(
//     'label' => 'Octonary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_octonary_color'
//   )));

//   //
//   // Nonary
//   //
//   $wp_customize->add_setting('baum_nonary_color');
//   $wp_customize->add_setting( 'baum_nonary_color', array(
//     'default' => $color_nonary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_nonary_color', array(
//     'label' => 'Nonary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_nonary_color'
//   )));

//   //
//   // Denary
//   //
//   $wp_customize->add_setting('baum_denary_color');
//   $wp_customize->add_setting( 'baum_denary_color', array(
//     'default' => $color_denary,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_denary_color', array(
//     'label' => 'Denary color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_denary_color'
//   )));

//   //
//   // Primary (dark)
//   //
//   $wp_customize->add_setting('baum_primary_dark_color');
//   $wp_customize->add_setting( 'baum_primary_dark_color', array(
//     'default' => $color_primary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_primary_dark_color', array(
//     'label' => 'Primary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_primary_dark_color'
//   )));

//   //
//   // Secondary (dark)
//   //
//   $wp_customize->add_setting('baum_secondary_dark_color');
//   $wp_customize->add_setting( 'baum_secondary_dark_color', array(
//     'default' => $color_secondary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_secondary_dark_color', array(
//     'label' => 'Secondary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_secondary_dark_color'
//   )));

//   //
//   // Tertiary (dark)
//   //
//   $wp_customize->add_setting('baum_tertiary_dark_color');
//   $wp_customize->add_setting( 'baum_tertiary_dark_color', array(
//     'default' => $color_tertiary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_tertiary_dark_color', array(
//     'label' => 'Tertiary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_tertiary_dark_color'
//   )));

//   //
//   // Quaternary (dark)
//   //
//   $wp_customize->add_setting('baum_quaternary_dark_color');
//   $wp_customize->add_setting( 'baum_quaternary_dark_color', array(
//     'default' => $color_quaternary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_quaternary_dark_color', array(
//     'label' => 'Quaternary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_quaternary_dark_color'
//   )));

//   //
//   // Quinary (dark)
//   //
//   $wp_customize->add_setting('baum_quinary_dark_color');
//   $wp_customize->add_setting( 'baum_quinary_dark_color', array(
//     'default' => $color_quinary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_quinary_dark_color', array(
//     'label' => 'Quinary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_quinary_dark_color'
//   )));

//   //
//   // Senary (dark)
//   //
//   $wp_customize->add_setting('baum_senary_dark_color');
//   $wp_customize->add_setting( 'baum_senary_dark_color', array(
//     'default' => $color_senary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_senary_dark_color', array(
//     'label' => 'Senary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_senary_dark_color'
//   )));

//   //
//   // Septenary (dark)
//   //
//   $wp_customize->add_setting('baum_septenary_dark_color');
//   $wp_customize->add_setting( 'baum_septenary_dark_color', array(
//     'default' => $color_septenary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_septenary_dark_color', array(
//     'label' => 'Septenary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_septenary_dark_color'
//   )));

//   //
//   // Octonary (dark)
//   //
//   $wp_customize->add_setting('baum_octonary_dark_color');
//   $wp_customize->add_setting( 'baum_octonary_dark_color', array(
//     'default' => $color_octonary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_octonary_dark_color', array(
//     'label' => 'Octonary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_octonary_dark_color'
//   )));

//   //
//   // Nonary (dark)
//   //
//   $wp_customize->add_setting('baum_nonary_dark_color');
//   $wp_customize->add_setting( 'baum_nonary_dark_color', array(
//     'default' => $color_nonary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_nonary_dark_color', array(
//     'label' => 'Nonary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_nonary_dark_color'
//   )));

//   //
//   // Denary (dark)
//   //
//   $wp_customize->add_setting('baum_denary_dark_color');
//   $wp_customize->add_setting( 'baum_denary_dark_color', array(
//     'default' => $color_denary_dark,
//   ));

//   $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_denary_dark_color', array(
//     'label' => 'Denary (dark) color:',
//     'description' => '',
//     'section' => 'baum_theme_style',
//     'settings' => 'baum_denary_dark_color'
//   )));


// }




  // $wp_customize->add_setting('baum_navbar_color');
  // $wp_customize->add_setting( 'baum_navbar_color', array(
  //   'default' => '',
  // ));

  // $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'baum_navbar_color', array(
  //   'label' => 'Color of the navigation menu:',
  //   'section' => 'baum_theme_style',
  //   'settings' => 'baum_navbar_color'
  // )));



  // // add a setting for the date heading on the homepage
  // $wp_customize->add_setting('baum_home_date_headline');

  // // Add a control to change the twitter link
  // $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_home_date_headline', array(
  //   'label' => 'Enable date headline on the homepage:',
  //   'section' => 'baum_theme_style',
  //   'settings' => 'baum_home_date_headline',
  //   'type' => 'checkbox'
  // )));



  // // add a setting for the light / dark theme toggle
  // $wp_customize->add_setting('baum_darkmode');

  // // Add a control to change the twitter link
  // $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_darkmode', array(
  //   'label' => 'Enable darkmode (displays based on system setting)',
  //   'section' => 'baum_theme_style',
  //   'settings' => 'baum_darkmode',
  //   'type' => 'checkbox'
  // )));

  // // add a setting for the font radio selection
  // $wp_customize->add_setting('baum_font');

  // // Add a control to change the font style
  // $wp_customize->add_control(new WP_Customize_Control($wp_customize, 'baum_font', array(
  //   'label' => 'Choose the main font',
  //   'section' => 'baum_theme_style',
  //   'settings' => 'baum_font',
  //   'type' => 'radio',
  //   'choices' => array(
  //     'erbaum' => 'erbaum',
  //     'video-font' => 'video-font',
  //     'copperplate' => 'copperplate',
  //     'amboy' => 'amboy',
  //     'aptly' => 'aptly',
  //   )
  // )));



// Load Kirki if not already loaded (optional safety check)
if ( ! class_exists( 'Kirki' ) ) {
  return;
}

// Set up Kirki configuration
Kirki::add_config( 'baumpress_config', [
  'capability' => 'edit_theme_options',
  'option_type' => 'theme_mod',
] );

// Create the panel (optional - future proofing for more sections)
Kirki::add_panel( 'baumpress_theme_options', [
  'priority'    => 10,
  'title'       => esc_html__( 'BaumPress Theme Options', 'baumpress' ),
  'description' => esc_html__( 'Customize BaumPress theme settings.', 'baumpress' ),
] );

// === Logos Section ===
Kirki::add_section( 'baumpress_logos', [
  'title'       => esc_html__( 'Logos & Site Icons', 'baumpress' ),
  'description' => esc_html__( 'Set your light/dark logos and icons.', 'baumpress' ),
  'panel'       => 'baumpress_theme_options',
  'priority'    => 10,
] );

// === Fields ===
// Masthead Logos
Kirki::add_field( 'baumpress_config', [
  'type'        => 'image',
  'settings'    => 'masthead_logo_light',
  'label'       => esc_html__( 'Masthead Logo (Light Background)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => '',
] );

Kirki::add_field( 'baumpress_config', [
  'type'        => 'image',
  'settings'    => 'masthead_logo_dark',
  'label'       => esc_html__( 'Masthead Logo (Dark Background)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => '',
] );

// Footer Logos
Kirki::add_field( 'baumpress_config', [
  'type'        => 'image',
  'settings'    => 'footer_logo_light',
  'label'       => esc_html__( 'Footer Logo (Light Background)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => '',
] );

Kirki::add_field( 'baumpress_config', [
  'type'        => 'image',
  'settings'    => 'footer_logo_dark',
  'label'       => esc_html__( 'Footer Logo (Dark Background)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => '',
] );

// Primary Logos
Kirki::add_field( 'baumpress_config', [
  'type'        => 'image',
  'settings'    => 'primary_logo_light',
  'label'       => esc_html__( 'Primary Logo (Light Background)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => '',
] );

Kirki::add_field( 'baumpress_config', [
  'type'        => 'image',
  'settings'    => 'primary_logo_dark',
  'label'       => esc_html__( 'Primary Logo (Dark Background)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => '',
] );

// Site Icons
Kirki::add_field( 'baumpress_config', [
  'type'        => 'image',
  'settings'    => 'site_icon_light',
  'label'       => esc_html__( 'Site Icon (Light Mode)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => '',
] );

Kirki::add_field( 'baumpress_config', [
  'type'        => 'image',
  'settings'    => 'site_icon_dark',
  'label'       => esc_html__( 'Site Icon (Dark Mode)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => '',
] );

// Logo Sizes
Kirki::add_field( 'baumpress_config', [
  'type'        => 'slider',
  'settings'    => 'masthead_logo_width',
  'label'       => esc_html__( 'Masthead Logo Width (px)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => 200,
  'choices'     => [
      'min'  => 50,
      'max'  => 600,
      'step' => 1,
  ],
] );

Kirki::add_field( 'baumpress_config', [
  'type'        => 'slider',
  'settings'    => 'footer_logo_width',
  'label'       => esc_html__( 'Footer Logo Width (px)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => 150,
  'choices'     => [
      'min'  => 50,
      'max'  => 600,
      'step' => 1,
  ],
] );

Kirki::add_field( 'baumpress_config', [
  'type'        => 'slider',
  'settings'    => 'primary_logo_width',
  'label'       => esc_html__( 'Primary Logo Width (px)', 'baumpress' ),
  'section'     => 'baumpress_logos',
  'default'     => 250,
  'choices'     => [
      'min'  => 50,
      'max'  => 600,
      'step' => 1,
  ],
] );
