/**
 * BaumPress Skeleton Loaders CSS
 *
 * Comprehensive skeleton loading system for smooth UX transitions.
 * Includes loaders for category headers, cards, text, buttons, and more.
 *
 * @package BaumPress
 * @since 1.0.0
 */

/* ==========================================================================
   BASE SKELETON ANIMATION
   ========================================================================== */

@keyframes baum-skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Base skeleton element */
.baum-skeleton {
  background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
  background-size: 400% 100%;
  animation: baum-skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

/* Dark mode skeleton */
.dark-mode .baum-skeleton,
.dark-mode .baum-skeleton-category-item,
.dark-mode .baum-skeleton-more-btn,
.dark-mode .baum-skeleton-darkmode-toggle {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 400% 100%;
}

/* ==========================================================================
   CATEGORY HEADER SKELETON
   ========================================================================== */

.baum-skeleton-category-header {
  position: fixed;
  top: 0;
  width: 100%;
  height: 28px;
  background-color: var(--color-tertiary, #f5f5f5);
  z-index: 999;
  overflow: hidden;
}

.dark-mode .baum-skeleton-category-header {
  background-color: var(--color-tertiary, #1a1a1a);
}

.baum-skeleton-category-nav {
  padding: 0;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 85px auto;
  height: 100%;
  align-items: center;
}

.baum-skeleton-category-items {
  display: flex;
  align-items: center;
  gap: 5px;
  flex: 1;
  padding: 0 10px;
  margin: 0 0 0 10px;
  overflow: hidden;
}

.baum-skeleton-category-item {
  height: 11px;
  border-radius: 6px;
  background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
  background-size: 400% 100%;
  animation: baum-skeleton-loading 1.2s ease-in-out infinite;
  white-space: nowrap;
  flex-shrink: 0;
}

.dark-mode .baum-skeleton-category-item {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 400% 100%;
}

/* Varied widths matching actual category names */
.baum-skeleton-category-item:nth-child(1) { width: 45px; animation-delay: 0s; }
.baum-skeleton-category-item:nth-child(2) { width: 55px; animation-delay: 0.1s; }
.baum-skeleton-category-item:nth-child(3) { width: 38px; animation-delay: 0.2s; }
.baum-skeleton-category-item:nth-child(4) { width: 62px; animation-delay: 0.3s; }
.baum-skeleton-category-item:nth-child(5) { width: 48px; animation-delay: 0.4s; }
.baum-skeleton-category-item:nth-child(6) { width: 42px; animation-delay: 0.5s; }

.baum-skeleton-more-btn {
  width: 35px;
  height: 11px;
  border-radius: 6px;
  background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
  background-size: 400% 100%;
  animation: baum-skeleton-loading 1.2s ease-in-out infinite;
  animation-delay: 0.6s;
  margin: 0;
  padding: 0;
}

.dark-mode .baum-skeleton-more-btn {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 400% 100%;
}

.baum-skeleton-darkmode-toggle {
  width: 24px;
  height: 11px;
  border-radius: 6px;
  background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
  background-size: 400% 100%;
  animation: baum-skeleton-loading 1.2s ease-in-out infinite;
  animation-delay: 0.7s;
  margin: 0;
}

.dark-mode .baum-skeleton-darkmode-toggle {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 400% 100%;
}

/* ==========================================================================
   CARD SKELETON
   ========================================================================== */

.baum-skeleton-card {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dark-mode .baum-skeleton-card {
  background: #1a1a1a;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.baum-skeleton-card__avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
  background-size: 400% 100%;
  animation: baum-skeleton-loading 1.2s ease-in-out infinite;
  margin-right: 1rem;
  flex-shrink: 0;
}

.baum-skeleton-card__content {
  flex: 1;
}

.baum-skeleton-card__line {
  height: 12px;
  background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
  background-size: 400% 100%;
  animation: baum-skeleton-loading 1.2s ease-in-out infinite;
  margin-bottom: 8px;
  border-radius: 4px;
}

.baum-skeleton-card__line:nth-child(1) { width: 80%; animation-delay: 0.1s; }
.baum-skeleton-card__line:nth-child(2) { width: 60%; animation-delay: 0.2s; }
.baum-skeleton-card__line:nth-child(3) { width: 90%; animation-delay: 0.3s; }

/* ==========================================================================
   TEXT SKELETON
   ========================================================================== */

.baum-skeleton-text {
  height: 16px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.baum-skeleton-text--title {
  height: 24px;
  width: 70%;
  margin-bottom: 12px;
}

.baum-skeleton-text--subtitle {
  height: 18px;
  width: 50%;
  margin-bottom: 10px;
}

.baum-skeleton-text--line {
  height: 14px;
  margin-bottom: 6px;
}

.baum-skeleton-text--line:nth-child(odd) { width: 85%; }
.baum-skeleton-text--line:nth-child(even) { width: 75%; }

/* ==========================================================================
   BUTTON SKELETON
   ========================================================================== */

.baum-skeleton-button {
  height: 36px;
  width: 120px;
  border-radius: 6px;
  display: inline-block;
  margin-right: 10px;
}

.baum-skeleton-button--small {
  height: 28px;
  width: 80px;
}

.baum-skeleton-button--large {
  height: 44px;
  width: 160px;
}

/* ==========================================================================
   NAVIGATION SKELETON
   ========================================================================== */

.baum-skeleton-nav {
  height: 60px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.baum-skeleton-nav-item {
  height: 20px;
  border-radius: 10px;
  margin-right: 20px;
}

.baum-skeleton-nav-item:nth-child(1) { width: 100px; }
.baum-skeleton-nav-item:nth-child(2) { width: 80px; }
.baum-skeleton-nav-item:nth-child(3) { width: 120px; }

/* ==========================================================================
   RESPONSIVE ADJUSTMENTS
   ========================================================================== */

@media (max-width: 768px) {
  .baum-skeleton-category-items {
    gap: 3px;
  }

  .baum-skeleton-category-item:nth-child(n+4) {
    display: none;
  }

  .baum-skeleton-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .baum-skeleton-card__avatar {
    margin-right: 0;
    margin-bottom: 1rem;
  }
}

@media (max-width: 650px) {
  .baum-skeleton-category-header {
    height: 50px;
  }

  .baum-skeleton-category-item:nth-child(n+3) {
    display: none;
  }

  .baum-skeleton-category-nav {
    padding: 0 10px;
  }

  .baum-skeleton-category-items {
    margin: 0;
    padding: 0 5px;
  }
}

/* ==========================================================================
   FADE IN ANIMATION
   ========================================================================== */

.baum-skeleton-fade-in {
  animation: baum-skeleton-fade-in 0.3s ease-out;
}

@keyframes baum-skeleton-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */

.baum-skeleton-hidden {
  display: none !important;
}

.baum-skeleton-visible {
  display: block !important;
}

.baum-skeleton-loading {
  pointer-events: none;
  user-select: none;
}

/* Pulse effect for interactive elements */
.baum-skeleton-pulse {
  animation: baum-skeleton-pulse 2s ease-in-out infinite;
}

@keyframes baum-skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
