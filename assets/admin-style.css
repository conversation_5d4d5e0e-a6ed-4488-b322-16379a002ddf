/* styles.css */

/* .avatar {
  border-radius: 50%; 
} */
.button.disabled, 
.button[disabled], 
button.disabled, 
button[disabled], 
.input.disabled, 
.input[disabled], 
input.disabled, 
input[disabled], 
.a.disabled, 
.a[disabled], 
a.disabled, 
a[disabled] {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.5;
}

.media-frame-title h1 {
  letter-spacing: -1px;
}

.media-router .media-menu-item {
  letter-spacing: -0.5px;
}

.media-toolbar-secondary h2 {
  letter-spacing: -0.5px;
}

.media-modal-content .media-frame select.attachment-filters {
  min-width: 150px;
}

h1,
h2,
h3,
h4,
h5,
h6,
strong,
small,
b,
p,
input,
form,
button,
select,
optgroup,
option,
textarea,
submit, 
table,
thead,
tbody,
tfoot,
footer,
header,
tr,
th,
td,
span:not(.dashicons) {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !important;
}

.wrap h1 {
  /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !important; */
  font-size: 28px;
  font-weight: 1000;
  text-transform: uppercase;
  letter-spacing: -0.5px !important;
}

#wp-user-avatars-user-settings h2 { 
  /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol" !important;  */
  text-transform: uppercase;
  font-weight: 1000;
}

/* Preserve icons by not overriding specific classes used for icons */
/* body * .dashicons, 
body * .dashicons-before::before, 
body * .dashicons::before {
  font-family: dashicons !important;
} */

body * .wp-admin #adminmenu div.wp-menu-image:before, 
body * .wp-admin .collapse-button .collapse-button-icon:before,
body * .wp-admin .folded #adminmenu li.menu-top .wp-menu-image:before {
  font-family: dashicons !important;
}

.wrap h1.wp-heading-inline {
  display: inline-block;
  margin-right: 5px;
  font-weight: 1000;
  letter-spacing: -1px;
}

/* .wrap h1, .wrap h2 { */
  /* font-family: 'Arial', sans-serif; */
  /* color: #474747; */
  /* border-radius: 10px; */
/* } */

.wrap h1:has(+ form), 
.wrap h2 {
  /* font-family: 'Arial', sans-serif; */
  /* color: #474747; */
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
} 

.wrap h1:has(+ form), 
.wrap h2 {
  max-width: 864px !important;
  width: 864px !important;
}

.wrap h1:has(+ form), 
.wrap form h2 {
  max-width: 840px !important;
  width: 840px !important;
  /* min-width: 840px !important; */
}

.form-table th {
  font-weight: bold;
  color: #565656;
  border-radius: 0px;
}

.form-table td {
  padding: 10px;
  background-color: #eeeeee;
  /* border-radius: 10px; */
}


.form-table td.acf-label {
  border-radius: 0px;
}

input:focus, 
textarea:focus, 
select:focus {
  outline-offset: 0px;
}

.form-table input[type="text"], 
.form-table input[type="email"], 
.form-table input[type="url"], 
.form-table input[type="password"], 
.form-table select, 
.form-table textarea, 
.form-table .wp-editor-container, 
.form-table .wp-editor-container textarea, 
.form-table .button {
  /* border: 1px solid #c9c9c9; */
  border: none;
  /* padding: 4px 10px; */
  /* width: 100%; */
  box-sizing: border-box;
  /* outline: 2.5px solid rgb(231, 231, 231); */
  /* border-radius: 25px;  */
}

body .acf-input-wrap {
  /* position: relative; */
  overflow: initial;
}

/* .form-table input[type="checkbox"], 
.form-table input[type="radio"] {
  width: auto;
  margin-right: 10px;
} */

.form-table .wp-editor-container {
  width: 100% !important;
  max-width: 100%;
}

.wp-editor-container textarea {
  height: 300px !important;
  border-radius: 10px;
}

.submit input {
  background-color: #d5d5d5;
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  cursor: pointer;
}

.submit input:hover {
  background-color: #1f1f1f;
  border-radius: 10px;
}

/* Hide the redundant Biographical Info section */
/* #wp-description-wrap,
#wp-description-wrap ~ table:first-of-type,
#wp-description-wrap ~ h3 {
  display: none !important;
} */

/* Additional styles for the Biographical Info WYSIWYG editor */
#description_ifr {
  border-radius: 10px !important;
}

body #your-profile > table {
  margin-right: 0;
  width: 100%; 
  max-width: 864px !important;
  width: 864px !important;
}

#wp-user-avatars-user-settings { 
  position: initial !important;
}

/* .form-table input[type="text"], 
.form-table input[type="email"], 
.form-table input[type="url"], 
.form-table input[type="password"], 
.form-table select, 
.form-table textarea, 
.form-table .wp-editor-container, 
.form-table .wp-editor-container textarea, 
.form-table .button {
  border-color: gray;
} */


#normal-sortables #user_avatar img, 
#wp-user-avatars-user-settings img {
  -webkit-box-shadow: none;
  box-shadow: none; 
  height: 96px;
  width: 96px;
  border-radius: 50px;
}

#wp-user-avatars-user-settings h2 {
  display: inline-block; 
}

/* label[for="description"] {
  display: none; 
} */

/* Ensure h2 before SS88-VUM-table-wrapper is displayed as block or inline-block */
#SS88-VUM-table-wrapper {
  display: block; /* Ensure SS88-VUM-table-wrapper is displayed as block */
}

#SS88-VUM-table-wrapper ~ h2 {
  display: inline-block !important;
}

/* Specific selector for the h2 immediately before SS88-VUM-table-wrapper */
#SS88-VUM-table-wrapper {
  display: block; /* Ensure SS88-VUM-table-wrapper is block */
}

#SS88-VUM-table-wrapper:before {
  content: '';
  display: block;
}

#SS88-VUM-table-wrapper:before + h2 {
  display: inline-block !important;
}

h2:has(+#SS88-VUM-table-wrapper) {
  display: inline-block; 
}

div:has(+#wp-user-avatars-media) {
  display: flex; 
}

.wp-core-ui p .button {
  vertical-align: baseline;
  border-radius: 10px;
  font-weight: 600;
  line-height: 36px;
}

#wp-user-avatars-actions div, 
#wp-user-avatars-actions input[type=file] {
  text-overflow: ellipsis;
  width: 100%;
  max-width: 450px;
}

.wp-core-ui p .button {
  background: var(--color-secondary); 
}

#wp-user-avatars-actions input[type=file], 
.wp-core-ui .button, 
.wp-core-ui .button-secondary, 
.wp-core-ui .button-primary, 
.wp-core-ui .button-group.button, 
.page-title-action, 
.button.action, 
.button-primary.action, 
.button-secondary.action {
  background-color: var(--color-tertiary); 
  border-color: #000;
  color: #fff;
  text-shadow: none;
  box-shadow: none;
  /* min-width: 100%; */
}

/* body #baum-user-info .baum-user-buttons a { */
  /* color: var(--color-primary); */
  /* color: #999; */
/* } */

a, 
a:hover, 
a:focus, 
a:active {
  /* background: var(--color-primary); */
  color: var(--color-primary); 
  color: var(--color-quaternary); 
}

#adminmenu li.current a.menu-top, 
#adminmenu li.wp-has-current-submenu .wp-submenu .wp-submenu-head, 
#adminmenu li.wp-has-current-submenu a.wp-has-current-submenu, 
.folded #adminmenu li.current.menu-top {
  color: #fff;
  background: var(--color-primary);
}
#adminmenu li.current a.menu-top, #adminmenu li.wp-has-current-submenu .wp-submenu .wp-submenu-head, #adminmenu li.wp-has-current-submenu a.wp-has-current-submenu, .folded #adminmenu li.current.menu-top {
  color: #fff;
  background: var(--color-quaternary);
  height: 30px;
  border-radius: var(--border-radius-small);
}


#adminmenu .wp-has-current-submenu.opensub .wp-submenu li.current a:focus, #adminmenu .wp-has-current-submenu.opensub .wp-submenu li.current a:hover, #adminmenu .wp-submenu li.current a:focus, 
#adminmenu .wp-submenu li.current a:hover, 
#adminmenu a.wp-has-current-submenu:focus+.wp-submenu li.current a:focus, #adminmenu a.wp-has-current-submenu:focus+.wp-submenu li.current a:hover {
  /* color: #e92b2b;  */
  /* color: #e92b2b;  */
  color: white; 
} 

#adminmenu .wp-has-current-submenu .wp-submenu a:focus, 
#adminmenu .wp-has-current-submenu .wp-submenu a:hover, 
#adminmenu .wp-has-current-submenu.opensub .wp-submenu a:focus, 
#adminmenu .wp-has-current-submenu.opensub .wp-submenu a:hover, 
#adminmenu .wp-submenu a:focus,
 #adminmenu .wp-submenu a:hover, 
#adminmenu a.wp-has-current-submenu:focus+.wp-submenu a:focus, 
#adminmenu a.wp-has-current-submenu:focus+.wp-submenu a:hover {
  /* color: #e92b2b;  */
  color: white; 
}

h1 a:hover { color: rgb(220, 220, 220); }

h1 a {
  font-size: 12px !important;
  display: block;
  margin: 10px 0;
  font-weight: 800;
  font-size: 12px !important;
  display: inline-block;
  margin: 10px;
  font-weight: 800;
  background: #717171;
  color: white;
  border-radius: 10px;
  padding: 10px 15px;
  text-decoration: none;
  line-height: 16px;
  position: relative;
  top: -5px;
  letter-spacing: normal;
}


#wp-user-avatars-actions div, 
#wp-user-avatars-actions input[type=file] {
  width: 455px;
  max-width: 100%;
}

#wp-user-avatars-actions div { 
  width: auto !important; 
  min-width: 450px !important;
}


#wp-user-avatars-actions div {
  /* text-overflow: ellipsis; */
  min-width: 0 !important;
}

.wp-core-ui p .button {
  /* vertical-align: baseline; */
  border-radius: 10px;
  font-weight: 500;
  line-height: 14px;
}

.wp-core-ui p .button {
  /* vertical-align: baseline; */
  border-radius: 10px;
  font-weight: 600;
  line-height: 20px;
}

#new_application_password_name_desc {
  outline: 0; 
  padding: 5px; 
}

.application-passwords p, 
.form-wrap p, 
p.description, 
p.help, 
span.description {
  font-size: 12px;
  clear: left;
  display: block;
  margin: 0px;
  margin-left: 0px; 
  margin-top: 2.5px; 
  color: #646970;
}

span.description {
  margin-left: 5px;
  margin-top: 7.5px; 
}

p.submit {
  margin-left: 0px; 
}

#wp-user-avatars-actions input[type=file] {
  margin: 10px;
}

#wp-user-avatars-user-settings {
  width: 500px;
  border: 1px solid gray;
  border-radius: 10px;
  overflow: hidden;
  padding-right: 10px;
}

#wp-user-avatars-actions input[type=file] {
  width: 350px !important;
}

#wp-user-avatars-user-settings {
  width: 500px !important;
  border: 1px solid gray !important;
  border-radius: 10px !important;
  box-shadow: none !important; 
  overflow: hidden !important; 
}

/* .wrap .wp-heading-inline+.page-title-action {
  margin: 0px 15px;
  line-height: 12px !important;
  height: auto;
  padding: 10px 50px;
  width: auto;
  font-size: 12px;
  display: inline-flex;
  vertical-align: middle;
  font-weight: 500;
  position: relative;
  top: -12.5px;
} */

.wrap .wp-heading-inline+.page-title-action {
  /* float: left; */
  margin: 0px 15px;
  margin-top: 0px;
  margin-top: -5px;
  line-height: 12px !important;
  height: auto;
  padding: 10px 35px;
  width: auto;
  font-size: 12px;
  display: inline-flex;
  vertical-align: middle;
  /* float: right; */
  font-weight: 700;
  position: relative;
  top: 0;





background: var(--color-secondary) !important;
/* border-color: var(--color-primary); */
/* outline: 1px solid var(--color-senary) !important; */
color: #fff;
box-shadow: none;
outline: 0;
}







.tablenav {
  clear: both;
  height: 35px;
  margin: 0px 0 0px;
  padding-top: 10px;
  vertical-align: middle;
}

.subsubsub {
  list-style: none;
  margin: 15px 0 0;
  padding: 0;
  font-size: 13px;
  float: left;
  color: #646970;
}


.persistent-login-table {
  margin-left: 0px !important; 
}

.settings_page_baum-wp-login-redirect .wrap form {
  max-width: 755px !important;
}

.settings_page_baum-wp-login-redirect  .wrap form .form-table tr { 
  display: flex; 
}

#wpadminbar .ab-empty-item, 
#wpadminbar a.ab-item, 
#wpadminbar>#wp-toolbar span.ab-label, 
#wpadminbar>#wp-toolbar span.noticon {
  color: #ddd;
}

#adminmenu {
   /* width: 200px; */
  width: 220px;
}

#adminmenu .wp-submenu, 
#adminmenuback, 
#adminmenuwrap {
  background-color: #1c1c1c;
}

#adminmenu .wp-submenu {
  background-color: #000000;
}

#wpadminbar {
  background-color: #1c1c1c;
  display: none; 
}

/* #wpadminbar .ab-top-menu>li.hover>.ab-item, #wpadminbar.nojq .quicklinks .ab-top-menu>li>.ab-item:focus, #wpadminbar:not(.mobile) .ab-top-menu>li:hover>.ab-item, #wpadminbar:not(.mobile) .ab-top-menu>li>.ab-item:focus {
  background: #363636;
  color: #11baa1;
} */

#wpadminbar .menupop .ab-sub-wrapper, 
#wpadminbar .shortlink-input {
  margin: 0;
  padding: 0;
  box-shadow: 0 10px 15px rgba(0, 0, 0, .5);
  background: #272727;
  display: none;
  position: absolute;
  float: none;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

#wpadminbar .quicklinks .ab-sub-wrapper .menupop.hover>a, #wpadminbar .quicklinks .menupop ul li a:focus, #wpadminbar .quicklinks .menupop ul li a:focus strong, #wpadminbar .quicklinks .menupop ul li a:hover, #wpadminbar .quicklinks .menupop ul li a:hover strong, #wpadminbar .quicklinks .menupop.hover ul li a:focus, #wpadminbar .quicklinks .menupop.hover ul li a:hover, #wpadminbar .quicklinks .menupop.hover ul li div[tabindex]:focus, #wpadminbar .quicklinks .menupop.hover ul li div[tabindex]:hover, #wpadminbar li #adminbarsearch.adminbar-focused:before, #wpadminbar li .ab-item:focus .ab-icon:before, #wpadminbar li .ab-item:focus:before, #wpadminbar li a:focus .ab-icon:before, #wpadminbar li.hover .ab-icon:before, #wpadminbar li.hover .ab-item:before, #wpadminbar li:hover #adminbarsearch:before, #wpadminbar li:hover .ab-icon:before, #wpadminbar li:hover .ab-item:before, #wpadminbar.nojs .quicklinks .menupop:hover ul li a:focus, #wpadminbar.nojs .quicklinks .menupop:hover ul li a:hover {
  /* color: var(--color-primary); */
  /* color: #222; */
  color: #fff;
}

.folded #baum-user-info {
  display: none !important; 
}

/* #collapse-button:focus {
  color: #11baa1;
  outline: 1px solid transparent;
  outline-offset: -1px;
} */

#collapse-button:hover {
  color: #222;
}

body {
  background: #f0f0f1;
  color: #3d3d3d;
}

/* #adminmenu li.menu-top:hover, 
#adminmenu li.opensub>a.menu-top, 
#adminmenu li>a.menu-top:focus {
  position: relative;
  background-color: #222222;
  color: #11baa1;
} */

/* #adminmenu .wp-submenu a:focus, 
#adminmenu .wp-submenu a:hover, 
#adminmenu a:hover, 
#adminmenu li.menu-top>a:focus {
  color: #11baa1;
} */
 
#adminmenu .wp-submenu-head, 
#adminmenu a.menu-top {
  font-size: 11px;
  font-weight: 500;
  line-height: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* .wp-menu-name:active, */
li:focus .wp-menu-name, 
li:hover .wp-menu-name { 
  color: #fff !important;
  /* background: #323232; */
}

.wp-menu-open .wp-menu-name:focus, 
.wp-menu-open .wp-menu-name:hover { 
  color: #fff !important; 
  /* background: var(--color-primary);  */
}


/* Disable background color change on mouseout */
#adminmenu li.menu-top:hover {
  /* background-color: #940c0c !important;  */
  color: #ffffff !important;
}

#adminmenu li.menu-top {
  /* background-color: #940c0c !important;  */
  color: #ffffff !important;
}

#adminmenu li.menu-top {
  color: #ffffff; 
  /* transition: background-color 0.3s ease, color 0.3s ease; */
  transition: none; 
}

#adminmenu li.menu-top:hover {
  color: #ffffff; 
}

#adminmenu li.wp-has-current-submenu a.menu-top,
#adminmenu li.current a.menu-top {
  color: #ffffff; 
}

#adminmenu li.menu-top:mouseout {
  color: #ffffff; 
}

.wp-core-ui .button-link {
  margin: 0;
  padding: 0;
  box-shadow: none;
  border: 0;
  border-radius: 0;
  background: 0 0;
  cursor: pointer;
  text-align: left;
  color: var(--color-primary);
  text-decoration: underline; 
  /* transition-property: border,background,color; */
  /* transition-duration: .05s; */
  /* transition-timing-function: ease-in-out; */
  transition: none; 
} 

#adminmenu div.wp-menu-image:before { 
  color: rgba(236, 236, 236, 0.6); 
} 

input[type=color], 
input[type=date], 
input[type=datetime-local], 
input[type=datetime], 
input[type=email], 
input[type=month], 
input[type=number], 
input[type=password], 
input[type=search], 
input[type=tel], 
input[type=text], 
input[type=time], 
input[type=url], 
input[type=week], 
select, 
textarea {
  /* border-radius: 20px;  */
}

/* .wp-core-ui select { 
  color: #303030; 
  border-color: #939393; 
}  */

/* .wp-core-ui select:active {
  border-color: #5b5b5b;
  box-shadow: none;
} */

/* .wp-core-ui select:focus {
  border-color: var(--color-primary, #222);
  color: #666666;
  box-shadow: 0 0 0 1px #aeaeae;
} */

.media-frame input[type=email]:focus, 
.media-frame input[type=number]:focus, 
.media-frame input[type=password]:focus, 
.media-frame input[type=search]:focus, 
.media-frame input[type=text]:focus, 
.media-frame input[type=url]:focus, 
.media-frame select:focus, 
.media-frame textarea:focus {
  border-color: var(--color-primary, #222);
  box-shadow: none; 
  outline: none; 
}

#baum-avatar-upload-section {
  position: relative;
  margin-bottom: 50px;
  max-width: 600px;
  margin: auto;
}

#wp-admin-bar-my-account .avatar {
  float: right;
  border-radius: 50% !important;
  margin: 2.5px 5px !important;
}

.wrap .avatar {
  border-radius: 50% !important;
}

.media-frame input[type=color], 
.media-frame input[type=date], 
.media-frame input[type=datetime-local], 
.media-frame input[type=datetime], 
.media-frame input[type=email], 
.media-frame input[type=month], 
.media-frame input[type=number], 
.media-frame input[type=password], 
.media-frame input[type=search], 
.media-frame input[type=tel], 
.media-frame input[type=text], 
.media-frame input[type=time], 
.media-frame input[type=url], 
.media-frame input[type=week], 
.media-frame select, 
.media-frame textarea { 
  border-radius: var(--border-radius, 10px); 
} 

.postbox {
  border-radius: var(--border-radius, 10px);
}

.postbox {
  box-shadow: 0 5px 5px rgba(0,0,0, 0.1);
  background: #fff;
}


#wp-admin-bar-user-info .avatar {
  position: absolute;
  left: -72px;
  top: 4px;
  width: 48px;
  height: 48px;
}


.postbox .inside h2, 
.wrap [class$=icon32]+h2, 
.wrap h1, 
.wrap>h2:first-child {
  /* font-size: 23px; */
  /* font-weight: 400; */
  /* margin: 0; */
  padding: 0px 0px 5px;
  line-height: 1.25;
}

.user-rich-editing-wrap {
  display: none; 
}

.user-syntax-highlighting-wrap {
  display: none; 
}

.user-nickname-wrap, 
.user-display-name-wrap, 
.user-googleplus-wrap,
.show-admin-bar, 
.user-comment-shortcuts-wrap {
  display: none; 
}

.user-profile-picture .description, 
.user-email-wrap .description {
  display: none; 
}

#pass1, 
#pass-strength-result {
  max-width: 350px;
  width: 350px;
  min-width: 350px;
}

h2:has(+ table .user-googleplus-wrap), 
table:has(.user-googleplus-wrap) {
  display: none; 
}

/* #wpcontent { */
  /* outline: 50px solid #1c1c1c;; */
  /* border-radius: 20px;  */
  /* margin: 5px; */
/* } */

#wpcontent { 
  outline: 50px solid #1c1c1c; 
  border-radius: 20px; 
  margin: 15px; 
} 

html.wp-toolbar {
  padding-top: 0; 
}



































body:not(.taxonomy-product_cat):not(.woocommerce-page) .wrap {
  padding-top: 0 !important
}

.wrap {
  min-width: auto !important; 
  margin: 20px 20px 0 2px;
}


@media only screen and (min-width:782px) {

  .wrap h1:has(+ form), 
  .wrap:not(#poststuff) form#createuser,
  .wrap:not(#poststuff) form#edittag,
  .wrap:not(#poststuff) form#your-profile,
  .wrap:not(#poststuff) form[action="options-permalink.php"],
  .wrap:not(#poststuff) form[action="options.php"] {
      /* max-width: calc(100% - 300px) !important; */
      position: relative
  }

  .wrap:not(#poststuff) form#createuser .edit-tag-actions,
  .wrap:not(#poststuff) form#createuser p.submit,
  .wrap:not(#poststuff) form#edittag .edit-tag-actions,
  .wrap:not(#poststuff) form#edittag p.submit,
  .wrap:not(#poststuff) form#your-profile .edit-tag-actions,
  .wrap:not(#poststuff) form#your-profile p.submit,
  .wrap:not(#poststuff) form[action="options-permalink.php"] .edit-tag-actions,
  .wrap:not(#poststuff) form[action="options-permalink.php"] p.submit,
  .wrap:not(#poststuff) form[action="options.php"] .edit-tag-actions,
  .wrap:not(#poststuff) form[action="options.php"] p.submit {
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      /* position: sticky; */
      /* top: 37px; */
      width: 280px;
      /* right: -300px; */
      padding: 0px;
      margin: 0;
      background: #f5f5f5;
      text-align: left;
      border: 1px solid #ccd0d4;
      border-radius: 0;
      -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
      box-shadow: 0 1px 1px rgba(0, 0, 0, .04); 
      border-bottom-right-radius: 10px; 
      border-bottom-left-radius: 10px; 
      


        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        /* position: fixed; */
        /* top: 90px; */
        top: 135px;
        width: 280px;
        right: 10px;
        padding: 0px;
        margin: 0;
        background: none;
        text-align: left;
        border: 0;
        border-radius: 0;
        outline: 0;
        -webkit-box-shadow: none;
        box-shadow: none;
        border-bottom-right-radius: 10px;
        border-bottom-left-radius: 10px;
        
  }

  .wrap:not(#poststuff) form#createuser .edit-tag-actions::before,
  .wrap:not(#poststuff) form#createuser p.submit::before,
  .wrap:not(#poststuff) form#edittag .edit-tag-actions::before,
  .wrap:not(#poststuff) form#edittag p.submit::before,
  .wrap:not(#poststuff) form#your-profile .edit-tag-actions::before,
  .wrap:not(#poststuff) form#your-profile p.submit::before,
  .wrap:not(#poststuff) form[action="options-permalink.php"] .edit-tag-actions::before,
  .wrap:not(#poststuff) form[action="options-permalink.php"] p.submit::before,
  .wrap:not(#poststuff) form[action="options.php"] .edit-tag-actions::before,
  .wrap:not(#poststuff) form[action="options.php"] p.submit::before {
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      content: 0;
      background: #fff;
      border: 1px solid #ccd0d4;
      border-bottom: 0;
      position: absolute;
      width: 280px;
      top: -38px;
      height: 37px;
      left: -1px;
      border-top-left-radius: 10px; 
      border-top-right-radius: 10px; 
  }

  .wrap:not(#poststuff) form#createuser .edit-tag-actions .button,
  .wrap:not(#poststuff) form#createuser p.submit .button,
  .wrap:not(#poststuff) form#edittag .edit-tag-actions .button,
  .wrap:not(#poststuff) form#edittag p.submit .button,
  .wrap:not(#poststuff) form#your-profile .edit-tag-actions .button,
  .wrap:not(#poststuff) form#your-profile p.submit .button,
  .wrap:not(#poststuff) form[action="options-permalink.php"] .edit-tag-actions .button,
  .wrap:not(#poststuff) form[action="options-permalink.php"] p.submit .button,
  .wrap:not(#poststuff) form[action="options.php"] .edit-tag-actions .button,
  .wrap:not(#poststuff) form[action="options.php"] p.submit .button {
      /* min-height: 32px; */
      /* line-height: 2.30769231; */
      /* padding: 0 12px;  */

      min-height: 30px;
      line-height: 28px;
      padding: 0 63px;

      min-height: 49px;
      line-height: 49px;
      padding: 0 98px;

      font-size: 16px; 
      font-weight: 800; 
  }

  .wrap:not(#poststuff) form#createuser .edit-tag-actions #delete-link,
  .wrap:not(#poststuff) form#createuser p.submit #delete-link,
  .wrap:not(#poststuff) form#edittag .edit-tag-actions #delete-link,
  .wrap:not(#poststuff) form#edittag p.submit #delete-link,
  .wrap:not(#poststuff) form#your-profile .edit-tag-actions #delete-link,
  .wrap:not(#poststuff) form#your-profile p.submit #delete-link,
  .wrap:not(#poststuff) form[action="options-permalink.php"] .edit-tag-actions #delete-link,
  .wrap:not(#poststuff) form[action="options-permalink.php"] p.submit #delete-link,
  .wrap:not(#poststuff) form[action="options.php"] .edit-tag-actions #delete-link,
  .wrap:not(#poststuff) form[action="options.php"] p.submit #delete-link {
      float: left;
      margin: 0;
      text-decoration: underline
  }
}

.wrap>form:not(.search-form) {
  /* margin-top: 13px; */
  max-width: 100% !important
}

.form-table { 
  margin-top: 0; 
}

.wrap>form>div>h2, 
.wrap>form>div>h3, 
.wrap>form>div>div>h2, 
.wrap>form>div>div>h3, 
/* form + .wrap>h1,  */
.wrap h1:has(+ form), 
.wrap>form>h2, 
.wrap>form>h3 {
  background: #fff;
  outline: 1px solid #c3c4c7;
  margin: 0;
  color: #23282d;
  font-size: 21px;
  padding: 8px 12px;
  line-height: 1.75;
  margin-bottom: 1px;
  font-weight: 800;
  text-transform: capitalize; 
}

.wrap h1:has(+ form) {
  max-width: 864px !important;
}

.application-passwords p, 
.wrap>form>div>h2~p,
.wrap>form>div>h3~p,
.wrap>form>div>div>h2~p,
.wrap>form>div>div>h3~p,
.wrap>form>h2~p,
.wrap>form>h3~p {
  margin: 0;
  border: 1px solid #c3c4c7;
  border-top: 0;
  padding: 15px 12px;
  background: #ffffff;
  font-weight: 400;
  /* background: #f4f4f4; */
}

.wrap>form>h2+p {
  /* background: #f1f1f1; */
  background: #fff;
}

.application-passwords-list-table-wrapper .tablenav, 
.application-passwords-list-table-wrapper .tablenav, 
.application-passwords-list-table-wrapper table {
  max-width: 864px;
  width: 864px;
  min-width: 864px;
}

.application-passwords p, 
.wrap>form>div>h2~p, 
.wrap>form>div>h3~p, 
.wrap>form>div>div>h2~p, 
.wrap>form>div>div>h3~p, 
.wrap>form>h2~p, 
.wrap>form>h3~p {
  margin: 0;
  /* border-left: 1px solid #c3c4c7;  */
  /* border-right: 1px solid #c3c4c7;  */
  outline: 1px solid #c3c4c7; 
  border: 0; 
  margin-top: 1px;
  margin-bottom: 1px;
  max-width: 840px;
  width: 840px;
  min-width: 840px;
}

textarea.large-text { 
  max-width: 864px; 
  border-top-left-radius: 0px; 
  border-top-right-radius: 0px; 
} 

.wrap>form>div>div>h2+.form-table,
.wrap>form>div>div>h2~p+.form-table,
.wrap>form>div>div>h3+.form-table,
.wrap>form>div>div>h3~p+.form-table,
.wrap>form>h2+.form-table,
.wrap>form>h2~p+.form-table,
.wrap>form>h3+.form-table,
.wrap>form>h3~p+.form-table {
  margin-top: 0
}

.wrap>form .form-table {
  background: #fff;
  width: 100%;
  /* border: 1px solid #c3c4c7; */
  border-top: 0;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
  box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
  margin-bottom: 20px; 

  outline: 1px solid #c3c4c7;
  -webkit-box-shadow: none;
  box-shadow: none;
  margin-bottom: 28px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.wrap>form .form-table {
  background: #fff;
  width: 100%;
  outline: 1px solid #c3c4c7;
  border-top: 0;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
  box-shadow: 0 1px 1px rgba(0, 0, 0, .04);
  margin-bottom: 20px;
  -webkit-box-shadow: none;
  box-shadow: none;
  margin-bottom: 28px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  overflow: hidden;
}


.wrap>form .form-table.rank-math-metabox-frame {
  border-top: 1px solid #c3c4c7
}

.wrap>form .form-table>tbody>tr.acf-field {
  position: initial
}

.wrap>form .form-table>tbody>tr.acf-tab-wrap>td {
  background: #f9f9f9;
  padding: 15px 0 0 0
}

.wrap>form .form-table>tbody>tr.acf-tab-wrap>td ul.acf-tab-group {
  border-bottom: 0;
  padding: 0 0 0 20%
}

.wrap>form .form-table>tbody>tr.acf-tab-wrap>td ul.acf-tab-group>li.active a {
  background: #fff
}


.wrap>form .create-application-password, 
.wrap>form .form-table>tbody>tr>td {
  background: #fff;
  border-top: 1px solid #eee;
  position: initial;
  padding: 10px;
  line-height: 14px;
}

.wrap>form .create-application-password {
  padding-bottom: 15px; 
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  outline: 1px solid #c3c4c7;
  max-width: 844px;
  width: 844px;
  min-width: 844px;
}

.wrap>form .form-table>tbody>tr>td.td-full {
  padding: 15px 12px
}

.wrap>form .form-table>tbody>tr>td p {
  font-size: 12px
}

.wrap>form .form-table>tbody>tr>td.acf-label,
.wrap>form .form-table>tbody>tr>th {
  border-top: 1px solid #eee;
  border-right: 1px solid #e1e1e1;
  border-right: 0;
  background: #f9f9f9;
  padding: 15px 12px;
  width: 20%;
  font-size: 13px;
  position: initial;
  color: #444
}

.wrap>form .form-table>tbody>tr>td.acf-label label,
.wrap>form .form-table>tbody>tr>th label {
  vertical-align: top
}

.wrap>form .form-table>tbody>tr:first-of-type td,
.wrap>form .form-table>tbody>tr:first-of-type th {
  border-top: 0
}

.wrap>form .form-table textarea {
  width: 100%
}

.wrap>form .form-table .regular-text {
  width: 99%;
  /* max-width: 25em */
}

@media only screen and (max-width:782px) {

  .wrap>form>div>div>h2,
  .wrap>form>div>div>h3,
  .wrap>form>h2,
  .wrap>form>h3 {
      padding: 12px
  }

  .wrap>form table.form-table>tbody>tr {
      display: block;
      padding: 15px 12px;
      border-top: 1px solid #eee
  }

  .wrap>form table.form-table>tbody>tr>td.acf-label,
  .wrap>form table.form-table>tbody>tr>th {
      margin-bottom: 10px
  }

  .wrap>form table.form-table>tbody>tr>td,
  .wrap>form table.form-table>tbody>tr>td.acf-label,
  .wrap>form table.form-table>tbody>tr>th {
      padding: 0;
      display: block;
      width: auto;
      background: 0 0;
      border: 0 !important
  }

  .wrap>form table.form-table>tbody>tr:first-child {
      border-top: 0
  }
}

.wrap #titlediv {
  margin-bottom: 10px
}

.wrap #titlediv input {
  padding: 3px 8px;
  font-size: 1.7em;
  line-height: 100%;
  height: 1.7em;
  width: 100%;
  outline: 0;
  margin: 0 0 3px;
  background-color: #fff
}

.wrap .postbox .hndle {
  cursor: auto;
  -webkit-user-select: auto;
  -moz-user-select: auto;
  -ms-user-select: auto;
  user-select: auto
}

.wrap .postbox .handle-actions button,
.wrap .postbox .handle-actions span {
  display: none
}

.wrap .postbox .handle-actions .acf-hndle-cog {
  width: 2.2rem
}

.wrap #submitdiv #major-publishing-actions {
  border-top: 0
}

.wrap #submitdiv #major-publishing-actions #publishing-action {
  float: none;
  margin: 0
}

.wrap #submitdiv #major-publishing-actions #publishing-action .button {
  float: right
}

.wrap #submitdiv #major-publishing-actions #publishing-action .spinner {
  margin: 5px 10px 0
}

.wrap #submitdiv #major-publishing-actions #publishing-action #delete-link {
  float: left;
  margin-left: 0
}

.wrap #submitdiv #major-publishing-actions #publishing-action #delete-link a {
  text-decoration: underline
}

.wrap form#createuser .acf-field input[type=email],
.wrap form#createuser .acf-field input[type=number],
.wrap form#createuser .acf-field input[type=password],
.wrap form#createuser .acf-field input[type=search],
.wrap form#createuser .acf-field input[type=text],
.wrap form#createuser .acf-field input[type=url],
.wrap form#createuser .acf-field select,
.wrap form#your-profile .acf-field input[type=email],
.wrap form#your-profile .acf-field input[type=number],
.wrap form#your-profile .acf-field input[type=password],
.wrap form#your-profile .acf-field input[type=search],
.wrap form#your-profile .acf-field input[type=text],
.wrap form#your-profile .acf-field input[type=url],
.wrap form#your-profile .acf-field select {
  max-width: 100%
}

.wrap form#createuser .acf-field textarea,
.wrap form#your-profile .acf-field textarea {
  width: 100%;
  max-width: none
}

.wrap form#createuser select#ure_select_other_roles,
.wrap form#your-profile select#ure_select_other_roles {
  width: 100% !important;
  max-width: 500px !important
}

.wrap form#createuser select#ure_select_other_roles+.ms-parent,
.wrap form#your-profile select#ure_select_other_roles+.ms-parent {
  width: 100% !important;
  max-width: 500px !important
}

.wrap form#createuser select#ure_select_other_roles+.ms-parent .ms-choice,
.wrap form#your-profile select#ure_select_other_roles+.ms-parent .ms-choice {
  -webkit-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  border-radius: 3px;
  border: 1px solid #7e8993;
  color: #32373c;
  padding: 4px 8px;
  margin: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 14px;
  line-height: 2.1;
  min-height: 30px;
  background: #fff url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E') no-repeat right 5px top 55%;
  background-size: 16px 16px;
  cursor: pointer
}

.wrap form#createuser select#ure_select_other_roles+.ms-parent .ms-choice>span.placeholder,
.wrap form#your-profile select#ure_select_other_roles+.ms-parent .ms-choice>span.placeholder {
  color: #32373c
}

.wrap form#createuser select#ure_select_other_roles+.ms-parent .ms-choice>div,
.wrap form#your-profile select#ure_select_other_roles+.ms-parent .ms-choice>div {
  display: none
}

.wrap form#createuser .yoast-settings,
.wrap form#your-profile .yoast-settings {
  padding: 0;
  margin-bottom: 20px;
  font-size: 13px
}

.wrap form#createuser .yoast-settings h2,
.wrap form#your-profile .yoast-settings h2 {
  margin: 0
}

.wrap form#createuser .yoast-settings label,
.wrap form#your-profile .yoast-settings label {
  margin: 0;
  padding: 0;
  width: auto;
  font-size: 13px;
  color: #444;
  line-height: 1.7
}

.wrap form#createuser .yoast-settings .description,
.wrap form#your-profile .yoast-settings .description {
  font-size: 13px;
  margin: 0 0 5px;
  color: #666
}

.wrap form#createuser .yoast-settings input,
.wrap form#createuser .yoast-settings textarea,
.wrap form#your-profile .yoast-settings input,
.wrap form#your-profile .yoast-settings textarea {
  margin: 0
}

.wrap form#createuser .yoast-settings textarea,
.wrap form#your-profile .yoast-settings textarea {
  margin-bottom: 10px
}

/* .wrap form#createuser .yoast-settings input[type=checkbox],
.wrap form#your-profile .yoast-settings input[type=checkbox] {
  margin-right: 5px;
  vertical-align: -6px
} */

.wrap form#createuser .form-table[role=presentation] tr.user-language-wrap th[colspan="2"],
.wrap form#your-profile .form-table[role=presentation] tr.user-language-wrap th[colspan="2"] {
  display: none
}

.wrap form#addtag .acf-fields.-left>.acf-field,
.wrap form#edittag .acf-fields.-left>.acf-field {
  padding-left: 0
}

.wrap form#addtag .acf-fields.-left>.acf-field.acfe-bt-no-label,
.wrap form#edittag .acf-fields.-left>.acf-field.acfe-bt-no-label {
  padding-left: 20%
}

@media only screen and (max-width:640px) {

  .wrap form#addtag .acf-fields.-left>.acf-field.acfe-bt-no-label,
  .wrap form#edittag .acf-fields.-left>.acf-field.acfe-bt-no-label {
      padding-left: 0
  }
}

.wrap form#addtag .acf-fields.-left>.acf-field::before,
.wrap form#edittag .acf-fields.-left>.acf-field::before {
  width: 20%
}

.wrap form#addtag .acf-fields.-left>.acf-field>.acf-label,
.wrap form#edittag .acf-fields.-left>.acf-field>.acf-label {
  width: 20%;
  margin-left: 0;
  padding: 0 12px
}

.wrap form#addtag .acf-fields.-left>.acf-field>.acf-label label,
.wrap form#edittag .acf-fields.-left>.acf-field>.acf-label label {
  color: #444
}

.wrap form#addtag .acf-fields.-left>.acf-field>.acf-input,
.wrap form#edittag .acf-fields.-left>.acf-field>.acf-input {
  padding: 0 12px
}

.wrap form#addtag .form-field p,
.wrap form#addtag .form-field select,
.wrap form#edittag .form-field p,
.wrap form#edittag .form-field select {
  max-width: 100%
}

.wrap form#addtag .form-field input[type=email],
.wrap form#addtag .form-field input[type=number],
.wrap form#addtag .form-field input[type=password],
.wrap form#addtag .form-field input[type=search],
.wrap form#addtag .form-field input[type=tel],
.wrap form#addtag .form-field input[type=text],
.wrap form#addtag .form-field input[type=url],
.wrap form#addtag .form-field select,
.wrap form#addtag .form-field textarea,
.wrap form#edittag .form-field input[type=email],
.wrap form#edittag .form-field input[type=number],
.wrap form#edittag .form-field input[type=password],
.wrap form#edittag .form-field input[type=search],
.wrap form#edittag .form-field input[type=tel],
.wrap form#edittag .form-field input[type=text],
.wrap form#edittag .form-field input[type=url],
.wrap form#edittag .form-field select,
.wrap form#edittag .form-field textarea {
  width: 100%
}

.wrap form#edittag .acf-column-1>#wp-description-wrap {
  display: none
}

.wrap form#edittag .postbox.wpseo-taxonomy-metabox-postbox>h2 {
  border-bottom: 1px solid #ccd0d4
}

.wrap form#edittag tr.wpml-term-languages-wrap {
  display: none
}

.wrap form#edittag #icl-tax-postbox .inside {
  margin: 0;
  padding: 15px 12px
}

.wrap form#edittag #icl-tax-postbox .inside table.icl_translations_table {
  width: 100%
}

.wrap #add-term-translations,
.wrap #edit-term-translations {
  border: none;
  width: 100%
}

.wrap #select-add-term-language select,
.wrap #select-edit-term-language select {
  width: auto !important;
  padding-right: 25px
}

.wrap #select-add-term-language+p,
.wrap #select-edit-term-language+p {
  display: none
}

.wrap #select-add-term-language p.description,
.wrap #select-edit-term-language p.description {
  display: none
}

.wrap #term-translations>p {
  position: relative;
  width: 20%;
  padding: 0 12px;
  float: left;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: #444
}

.wrap #term-translations>.icl_subsubsub {
  position: relative;
  width: 20%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 12px;
  float: left
}

.wrap #term-translations #add-term-translations {
  width: 80%;
  position: relative;
  float: left;
  clear: none;
  background: 0 0;
  padding: 0 12px
}

.wrap #term-translations #add-term-translations td.pll-translation-column {
  padding-left: 0;
  padding-right: 0
}

.wrap #term-translations #add-term-translations td,
.wrap #term-translations #add-term-translations th {
  padding-top: 4px;
  padding-bottom: 4px
}

.wrap #term-translations #add-term-translations tr:first-of-type td,
.wrap #term-translations #add-term-translations tr:first-of-type th {
  padding-top: 0
}

.wrap #term-translations #add-term-translations tr:last-of-type td,
.wrap #term-translations #add-term-translations tr:last-of-type th {
  padding-bottom: 0
}

.wrap #term-translations #edit-term-translations td,
.wrap #term-translations #edit-term-translations th {
  padding-top: 2px;
  padding-bottom: 2px
}

.wrap #term-translations #edit-term-translations tr:first-of-type td,
.wrap #term-translations #edit-term-translations tr:first-of-type th {
  padding-top: 0
}

.wrap #term-translations #edit-term-translations tr:last-of-type td,
.wrap #term-translations #edit-term-translations tr:last-of-type th {
  padding-bottom: 0
}

.wrap #acf-group_term>.acf-fields>.rank-math-metabox-frame.acf-field,
.wrap #acf-group_term>.acf-fields>.yoast-settings-table.acf-field,
.wrap #acf-group_term>.acf-fields>table.acf-field,
.wrap #acf-group_user>.acf-fields>.rank-math-metabox-frame.acf-field,
.wrap #acf-group_user>.acf-fields>.yoast-settings-table.acf-field,
.wrap #acf-group_user>.acf-fields>table.acf-field,
.wrap #acf-group_user_customer>.acf-fields>.rank-math-metabox-frame.acf-field,
.wrap #acf-group_user_customer>.acf-fields>.yoast-settings-table.acf-field,
.wrap #acf-group_user_customer>.acf-fields>table.acf-field {
  border: none;
  margin-bottom: 0
}

.wrap #acf-group_term>.acf-fields>.rank-math-metabox-frame.acf-field::before,
.wrap #acf-group_term>.acf-fields>.yoast-settings-table.acf-field::before,
.wrap #acf-group_term>.acf-fields>table.acf-field::before,
.wrap #acf-group_user>.acf-fields>.rank-math-metabox-frame.acf-field::before,
.wrap #acf-group_user>.acf-fields>.yoast-settings-table.acf-field::before,
.wrap #acf-group_user>.acf-fields>table.acf-field::before,
.wrap #acf-group_user_customer>.acf-fields>.rank-math-metabox-frame.acf-field::before,
.wrap #acf-group_user_customer>.acf-fields>.yoast-settings-table.acf-field::before,
.wrap #acf-group_user_customer>.acf-fields>table.acf-field::before {
  content: unset
}

.wrap #acf-group_term>.acf-fields>.rank-math-metabox-frame.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_term>.acf-fields>.rank-math-metabox-frame.acf-field>tbody>tr:first-of-type>th,
.wrap #acf-group_term>.acf-fields>.yoast-settings-table.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_term>.acf-fields>.yoast-settings-table.acf-field>tbody>tr:first-of-type>th,
.wrap #acf-group_term>.acf-fields>table.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_term>.acf-fields>table.acf-field>tbody>tr:first-of-type>th,
.wrap #acf-group_user>.acf-fields>.rank-math-metabox-frame.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_user>.acf-fields>.rank-math-metabox-frame.acf-field>tbody>tr:first-of-type>th,
.wrap #acf-group_user>.acf-fields>.yoast-settings-table.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_user>.acf-fields>.yoast-settings-table.acf-field>tbody>tr:first-of-type>th,
.wrap #acf-group_user>.acf-fields>table.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_user>.acf-fields>table.acf-field>tbody>tr:first-of-type>th,
.wrap #acf-group_user_customer>.acf-fields>.rank-math-metabox-frame.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_user_customer>.acf-fields>.rank-math-metabox-frame.acf-field>tbody>tr:first-of-type>th,
.wrap #acf-group_user_customer>.acf-fields>.yoast-settings-table.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_user_customer>.acf-fields>.yoast-settings-table.acf-field>tbody>tr:first-of-type>th,
.wrap #acf-group_user_customer>.acf-fields>table.acf-field>tbody>tr:first-of-type>td,
.wrap #acf-group_user_customer>.acf-fields>table.acf-field>tbody>tr:first-of-type>th {
  border-top: 0
}

.wrap #acf-group_term>.acf-fields>.rank-math-metabox-frame.acf-field,
.wrap #acf-group_user>.acf-fields>.rank-math-metabox-frame.acf-field,
.wrap #acf-group_user_customer>.acf-fields>.rank-math-metabox-frame.acf-field {
  padding: 0
}

/* .edit-tags-php #col-left {
  display: none;
  float: none;
  width: 100%
} */

.edit-tags-php #col-left .col-wrap {
  padding: 0
}

.edit-tags-php #col-left .col-wrap #poststuff {
  min-width: 100%
}

.edit-tags-php #col-left .col-wrap #poststuff #addtag h2.hndle {
  border-bottom: 1px solid #ccd0d4
}

.edit-tags-php #col-left .col-wrap #poststuff #addtag .inside .term-name-wrap {
  border-top: none
}

.edit-tags-php #col-left .col-wrap #poststuff #addtag .submit {
  display: block;
  position: relative;
  margin: 0;
  padding: 15px 12px;
  border-top: #eee solid 1px;
  padding-left: 20%
}

.edit-tags-php #col-left .col-wrap #poststuff #addtag .submit::before {
  content: "";
  display: block;
  position: absolute;
  z-index: 0;
  background: #f9f9f9;
  border-color: #e1e1e1;
  border-style: solid;
  border-width: 0 1px 0 0;
  top: 0;
  bottom: 0;
  left: 0;
  width: 20%
}

.edit-tags-php #col-left .col-wrap #poststuff #addtag .submit::after {
  content: "";
  display: block;
  clear: both
}

.edit-tags-php #col-left .col-wrap #poststuff #addtag .submit>.acf-input {
  vertical-align: top;
  float: left;
  width: 80%;
  margin: 0;
  padding: 0 12px
}

@media screen and (max-width:640px) {
  .edit-tags-php #col-left .col-wrap #poststuff #addtag .submit {
      padding: 15px 0
  }

  .edit-tags-php #col-left .col-wrap #poststuff #addtag .submit::before {
      display: none
  }

  .edit-tags-php #col-left .col-wrap #poststuff #addtag .submit>.acf-input {
      width: 100%
  }
}

.edit-tags-php #col-container #col-right {
  /* float: none;  */
  float: right; 
  width: auto; 
  width: 60%; 
} 

.edit-tags-php #col-container #col-right .col-wrap {
  padding: 0
}

body.post-type-attachment .wrap #submitdiv #major-publishing-actions {
  border-top: 1px solid #dcdcde
}

body.post-type-attachment .wrap>form:not(.search-form) {
  margin-top: 0
}

body.post-type-attachment .wrap>form .wp_attachment_holder {
  background: #fff;
  padding: 15px;
  border: 1px solid #c3c4c7;
  -webkit-box-shadow: 0 1px 1px rgb(0 0 0 / 4%);
  box-shadow: 0 1px 1px rgb(0 0 0 / 4%)
}

body.post-type-attachment .wrap>form .wp_attachment_holder .wp_attachment_image>p:first-of-type {
  margin-top: 0
}

body.post-type-attachment .wrap>form .wp_attachment_holder .image-editor .imgedit-settings .imgedit-group {
  background: #f9f9f9
}

body.post-type-attachment .wrap>form .wp_attachment_details {
  margin-bottom: 0;
  margin-top: 20px
}

body.post-type-attachment .wrap>form .wp_attachment_details #attachment_alt {
  max-width: 100%
}

body.post-type-attachment .wrap>form .wp_attachment_details p.attachment-alt-text-description {
  margin-bottom: 0
}

.wrap form > h2 {
  margin-top: 20px;
}

.wrap h1:has(+ form>h2) {
  display: none; 
}

.wrap h1:has(+ form) {
  /* margin-top: 20px; */
  margin-top: 0px;
}

.term-php .wrap h1:has(+ form) {
  display: block; 
}

.term-php textarea.large-text {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.acf-image-uploader .image-wrap {
  border-radius: 10px;
  overflow: hidden;
}

.selected {
  border-color: var(--color-secondary) !important;
  background: var(--color-primary) !important;
}

.acf-button-group label:hover {
  color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.acf-button-group label.selected:hover {
  color: var(--color-secondary) !important;
  border-color: var(--color-secondary) !important;
}

.wrap>form .form-table>tbody>tr>td {
  line-height: 1.75;
}

.form-table>tbody>.acf-field>.acf-label label {
  font-size: 13px;
  font-weight: 600;
}

a.acf-icon.dark:hover {
  color: var(--color-senary);
}

.acf-input .acf-button-group label {
  padding: 0px 10px;
}


.options-discussion-php .indent-children ul, 
#front-static-pages ul {
  margin-left: 0px;
}

.options-permalink-php .wrap h1, 
.options-media-php .wrap h1, 
.wrap h1 {
  background:none;
  outline:0;
  display: block; 
  font-size: 21px;
  font-weight: 1000;
  text-transform: uppercase;
  padding: 0px 0px 5px;
  line-height: 1.25;
  display: inline-block;
  margin-right: 5px;
  letter-spacing: -0.5px;
  color: var(--color-secondary); 
}

.options-discussion-php .wrap h1, 
.options-writing-php .wrap h1 { 
  display: block; 
} 

.options-discussion-php textarea { 
  border-radius: 10px; 
} 

.options-permalink-php .wrap form > p {
  max-width: 864px;
  width: 864px;
  min-width: 864px;
}

.options-permalink-php .wrap form > p {
  max-width: 840px;
  width: 840px;
  min-width: 840px; 
}

.widgets-php div#widgets-right .sidebar-name h2 {
  width: auto !important; 
}

.widgets-php div#widgets-right .widgets-sortables {
  min-height: auto;
}

input::placeholder {
  color: rgba(0, 0, 0, 0.25); 
}

#baum_disinformation_widget form h2, 
#baum_competitor_tracking_widget form h2, 
#baum_historical_trends_widget form h2 {
  display: none; 
}

#baum_disinformation_widget .inside, 
#baum_competitor_tracking_widget .inside, 
#baum_historical_trends_widget .inside {
  margin: 0; 
  padding: 0;
}

#baum_disinformation_widget .wrap, 
#baum_competitor_tracking_widget .wrap, 
#baum_historical_trends_widget .wrap {
  margin: 0;
}

#baum_disinformation_widget .postbox-header, 
#baum_competitor_tracking_widget .postbox-header, 
#baum_historical_trends_widget .postbox-header { 
  border-bottom: 0px solid #c3c4c7;
}

#baum_disinformation_widget .wrap>form>div>div>h2+.form-table, 
#baum_disinformation_widget .wrap>form>div>div>h2~p+.form-table, 
#baum_disinformation_widget .wrap>form>div>div>h3+.form-table, 
#baum_disinformation_widget .wrap>form>div>div>h3~p+.form-table, 
#baum_disinformation_widget .wrap>form>h2+.form-table, 
#baum_disinformation_widget .wrap>form>h2~p+.form-table, 
#baum_disinformation_widget .wrap>form>h2~p+.form-table, 
#baum_disinformation_widget .wrap>form>h3~p+.form-table, 
#baum_competitor_tracking_widget .wrap>form>div>div>h2+.form-table, 
#baum_competitor_tracking_widget .wrap>form>div>div>h2~p+.form-table, 
#baum_competitor_tracking_widget .wrap>form>div>div>h3+.form-table, 
#baum_competitor_tracking_widget .wrap>form>div>div>h3~p+.form-table, 
#baum_competitor_tracking_widget .wrap>form>h2+.form-table, 
#baum_competitor_tracking_widget .wrap>form>h2~p+.form-table, 
#baum_competitor_tracking_widget .wrap>form>h3+.form-table, 
#baum_competitor_tracking_widget .wrap>form>h3~p+.form-table, 
#baum_historical_trends_widget .wrap>form>div>div>h2+.form-table, 
#baum_historical_trends_widget .wrap>form>div>div>h2~p+.form-table, 
#baum_historical_trends_widget .wrap>form>div>div>h3+.form-table, 
#baum_historical_trends_widget .wrap>form>div>div>h3~p+.form-table, 
#baum_historical_trends_widget .wrap>form>h2+.form-table, 
#baum_historical_trends_widget .wrap>form>h2~p+.form-table, 
#baum_historical_trends_widget .wrap>form>h3+.form-table, 
#baum_historical_trends_widget .wrap>form>h3~p+.form-table {
  margin: 0;
}


.form-table {
  max-width: 864px;
  /* max-width: 840px; */
  width: 864px;
}

#adminmenu .wp-not-current-submenu .wp-submenu, 
.folded #adminmenu .wp-has-current-submenu .wp-submenu {
  min-width: 200px;
}

.user-admin-color-wrap, 
.user-profile-picture {
  display: none; 
}

table.media .column-title .media-icon img {
  max-width: 35px;
}

td.column-title strong, 
td.plugin-title strong {
  font-size: 13px;
}

.widefat td, 
.widefat td ol, 
.widefat td p, 
.widefat td ul {
  font-size: 13px;
  line-height: 1.25;
}

.wp-filter {
  /* font-size: 12px; */
  border-radius: var(--border-radius, 10px);
}


.wp-list-table tr:hover:last-child {
  background-color: transparent;
}



.metabox-holder .postbox>h3, 
.metabox-holder .stuffbox>h3, 
.metabox-holder h2.hndle, 
.metabox-holder h3.hndle {
  font-size: 13px;
  background-color: var(--color-secondary);
  color: var(--color-white);
}

#adminmenu, 
#adminmenu .wp-submenu, 
#adminmenuback, 
#adminmenuwrap {
  /* width: 160px; */
  background-color: #1c1c1c;
  background-color: var(--color-secondary);
}

#wpcontent {
  outline-color: var(--color-secondary);
}

#adminmenu .wp-submenu {
  background-color: var(--color-black);
}


#adminmenu li.wp-has-submenu.wp-not-current-submenu.opensub:hover:after, #adminmenu li.wp-has-submenu.wp-not-current-submenu:focus-within:after {
  border-right-color: transparent;
}


.widefat thead td, 
.widefat thead th {}


.widefat tfoot tr td, 
.widefat tfoot tr th, 
.widefat thead tr td, 
.widefat thead tr th,
th.sortable a span, 
th.sorted a span, 
.widefat thead tr td, 
.widefat thead tr th { 
  /* color: #2c3338; */ 
  color: var(--color-white); 
  font-size: 12px; 
  font-weight: 700;
}

.widefat tfoot, 
.widefat thead { 
  /* background: var(--color-secondary);  */
  background: var(--color-tertiary);
  color: var(--color-white); 
  /* border-bottom: 1px solid #c3c4c7; */ 
} 

th .comment-grey-bubble:before {
  color: #fff; 
}

.updates-table td input, 
.widefat tfoot td input, 
.widefat th input, 
.widefat thead td input {
  margin: 0px 0px 5px 5px;
  padding: 0;
  vertical-align: middle;
}

th.sorted.asc .sorting-indicator.asc:before {
  color: rgba(255,255,255,0.25);
}

th.sorted.asc a:focus .sorting-indicator.desc:before, 
th.sorted.asc:hover .sorting-indicator.desc:before, 
th.sorted.desc a:focus .sorting-indicator.asc:before, 
th.sorted.desc:hover .sorting-indicator.asc:before {
  color: rgba(255,255,255,0.25);
}

th.sorted.desc .sorting-indicator.desc:before {
  color: rgba(255,255,255,0.25);
}



.tablenav .actions select {
  float: left;
  margin-right: 5px;
  max-width: 12.5rem;
  margin-bottom: 0;
  margin-top: 0;
}

.wp-core-ui select {
  font-size: 13px;
  line-height: 25px;
  color: #2c3338;
  border-color: #8c8f94;
  box-shadow: none;
  /* border-radius: 3px; */
  /* padding: 0 50px 0 10px; */
  min-height: 25px;
  max-width: 25rem;
  -webkit-appearance: none;
  background-size: 12.5px 12.5px;
  cursor: pointer;
  vertical-align: middle;
}

.wp-core-ui select {
  font-size: 13px;
  line-height: 25px;
  color: #2c3338;
  border-color: #8c8f94;
  box-shadow: none;
  /* border-radius: 3px; */
  padding: 0 5px;
  min-height: 28px;
  width: 99%;
  max-width: 100%;
  -webkit-appearance: none;
  background-size: 12.5px 12.5px;
  cursor: pointer;
  vertical-align: middle;
}



.wp-core-ui .button, 
.wp-core-ui .button-primary, 
.wp-core-ui .button-secondary {
  display: inline-block;
  text-decoration: none;
  font-size: 10px;
  line-height: 25px;
  min-height: 25px;
  /* margin-top: -2.5px; */
  /* margin-top: -10px; */
  padding: 0 15px;
  cursor: pointer;
  border-width: 0px;
  position: relative;
  /* top: -1px; */
}

.wp-core-ui .button, 
.wp-core-ui .button-primary, 
.wp-core-ui .button-secondary {
  display: inline-block;
  text-decoration: none;
  font-size: 10px;
  /* line-height: 25px; */
  /* min-height: 25px; */
  /* margin-top: -2.5px; */
  /* margin-top: -10px; */
  padding: 0 15px;
  cursor: pointer;
  border-width: 0px;
  position: relative;
  /* top: -1px; */
  line-height: 26px;
  min-height: 26px;
  /* top: -1.5px; */
  margin-top: 0px;
}

.wp-core-ui p .button {
  vertical-align: baseline;
  border-radius: 10px;
  font-weight: 700;
  font-size: 10px;
  line-height: 12px;
  width: auto;
  background: var(--color-tertiary);
  border: 0;
  outline: 0;
  text-transform: uppercase;
  border-radius: 10px;
  padding: 0px 15px;
  line-height: 25px;
  height: 25px;
  margin-top: -1px;
}

.tablenav .tablenav-pages .button, 
.tablenav .tablenav-pages .tablenav-pages-navspan {
  display: inline-block;
  vertical-align: baseline;
  min-width: 30px;
  /* height: 25px !important; */
  height: 25px !important;
  min-height: 25px;
  margin: 0;
  padding: 0 5px;
  font-size: 14px;
  line-height: 22.5px;
  text-align: center;
  }

input[type=date], 
input[type=datetime-local], 
input[type=datetime], 
input[type=email], 
input[type=month], 
input[type=number], 
input[type=password], 
input[type=search], 
input[type=tel], 
input[type=text], 
input[type=time], 
input[type=url], 
input[type=week] {
  /* padding: 0 10px; */
  /* line-height: 25px; */
  /* min-height: 25px; */
  /* padding: 0 10px; */
  /* line-height: 23px; */
  /* min-height: 23px; */
  /* margin-bottom: 1px; */
  padding: 0 10px;
  line-height: 25px;
  min-height: 25px;
  margin-bottom: 1px;
}

.toplevel_page_clerk-admin .wrap h1 { 
  background: none;
  outline: 0;
  display: block;
  font-size: 21px;
  font-weight: 1000;
  text-transform: uppercase;
  padding: 0px 0px 5px;
  line-height: 1.25;
  display: inline-block;
  margin-right: 5px;
  letter-spacing: -0.5px;
  color: var(--color-secondary);
  letter-spacing: -0.5px !important;
  margin: 0;
}


.site-icon-section {
  display: none; 
}

.plugins tr {
  background: transparent;
}

.media-modal-content {
  border-radius: 10px;
}

#message { 
  display: none; 
} 

#ajax-response { 
  display: none; 
} 

#adminmenu .wp-submenu {
  background-color: var(--color-quaternary);
}

#adminmenu .wp-submenu {
  background-color: var(--color-tertiary);
}

/* #adminmenu .wp-submenu { background-color: #181d22; } */

#adminmenu .wp-submenu {
  background-color: var(--color-tertiary);
}

input, .wp-core-ui select {
  border-radius: 5px;
}

input[type=color], 
input[type=date], 
input[type=datetime-local], 
input[type=datetime], 
input[type=email], 
input[type=month], 
input[type=number], 
input[type=password], 
input[type=search], 
input[type=tel], 
input[type=text], 
input[type=time], 
input[type=url], 
input[type=week], 
select, 
textarea {
  border-radius: 5px;
}

.wp-core-ui p .button {
  border-radius: 5px;
}

#doaction, #doaction2, #post-query-submit {
  margin: -1.5px 8px 0 0px;
  margin: 0.5px 8px 0 0px;
}

#screen-meta-links .show-settings:after {
  bottom: 0px;
}

/* body #baum-user-info .baum-user-buttons a { */
  /* color: var(--color-primary); */
  /* color: var(--color-primary); */
/* } */

#baum-user-info .baum-user-buttons a {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 5px;
  background-color: var(--color-quinary);
  color: var(--color-denary);
  text-align: center;
  font-size: 10px;
}

#adminmenu li.menu-top:hover, 
/* #adminmenu li.opensub>a.menu-top,  */
#adminmenu li>a.menu-top:focus { 
  /* background-color: #34393d;  */
  background-color: transparent; 
}

#adminmenu .wp-has-current-submenu .wp-submenu, 
#adminmenu .wp-has-current-submenu .wp-submenu.sub-open, 
#adminmenu .wp-has-current-submenu.opensub .wp-submenu, 
.no-js li.wp-has-current-submenu:hover .wp-submenu {
  /* outline: 4px solid var(--color-primary); */
  background: var(--color-tertiary);
  margin-bottom: 5px;
  border-radius: var(--border-radius-small);
  margin: 0px;
  width: 100%;
}

.wp-list-table tr td {
  vertical-align: top;
}

.wp-core-ui .button-link {
  color: var(--color-senary);
  font-size: 12px; 
}

.wp-core-ui .button-link:active, 
.wp-core-ui .button-link:hover {
  color: var(--color-primary);
}

.wp-list-table a {
  color: var(--color-secondary);
}

.row-actions a {
  color: var(--color-senary);
  font-size: 12px; 
}

.wrap button, .wrap .button, .wrap .page-title-action {
  border: 0px !important;
}

ul.acf-radio-list li label, 
ul.acf-checkbox-list li label {
  background: none !important; 
}










.baum-dropdown-wrapper {
  margin: 10px 0;
  max-width: 400px;
  border-radius: var(--border-radius-small);
  /* overflow: hidden;  */
}

.baum-dropdown {
  position: relative;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
}

.baum-selected {
  padding: 8px 12px;
}

/* .baum-options {
  display: none;
  max-height: 280px;
  overflow-y: auto;
  position: absolute;
  width: 100%;
  background: #fff;
  border: 1px solid #ccc;
  border-top: none;
  z-index: 99;
  border-radius: var(--border-radius);
  overflow: hidden;
  padding: 10px 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
} */

/* .baum-options {
  display: none;
  max-height: 280px;
  overflow-y: auto;
  position: absolute;
  width: 100%;
  background: var(--color-white);
  border: 1px solid var(--color-octonary);
  border-top: 1px solid var(--color-octonary);
  z-index: 999;
  border-radius: var(--border-radius);
  padding: 10px 0;
  border-top-right-radius: var(--border-radius-small);
  border-top-left-radius: var(--border-radius-small);
  margin: 0px -1px;
} */


/* .baum-options li {
  padding: 8px 12px;
  border-bottom: 1px solid #eee;
} */

/* .baum-options li:hover {
  background: #f1f1f1;
} */

.baum-dropdown.open .baum-options {
  display: block;
}


.baum-custom-select {
  position: relative;
  width: 100%;
  max-width: 370px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0px;
  background: #fff;
  margin: 10px 0; 
}

.baum-custom-select .baum-select-input {
  width: 100%;
  border: none;
  outline: none;
  padding: 0px 5px;
  box-sizing: border-box;
}

/* .baum-options {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background: #fff;
  border: 1px solid #ccc;
  border-top: none;
  z-index: 999;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  margin: 0;
  padding: 0;
  list-style: none;
} */

.baum-options {
  display: none;
  max-height: 280px;
  overflow-y: auto;
  position: absolute;
  width: 100%;
  background: var(--color-white);
  border: 1px solid var(--color-octonary);
  border-top: 1px solid var(--color-octonary);
  z-index: 999;
  border-radius: var(--border-radius);
  padding: 10px 0;
  border-top-right-radius: var(--border-radius-small);
  border-top-left-radius: var(--border-radius-small);
  margin: 0px -1px;
}

.baum-options.open,
.baum-options.show {
  display: block;
}

.baum-options li {
  padding: 8px 10px;
  cursor: pointer;
}

.baum-options li:hover {
  background: #f0f0f0;
}


pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
  border-radius: var(--border-radius);
  max-width: 640px;
}


#wpbody-content {
  /* padding-bottom: 65px; */
  /* float: left; */
  width: 100%;
  overflow: visible;
  position: initial;
  /* overflow: hidden; */
  min-height: auto;
  top: 0px;
  padding-bottom: 0;
}

.notice, 
div.error, 
div.updated { 
  /* -webkit-box-shadow: 0 0px 0px 0 rgba(0,0,0,.0); */ 
  /* box-shadow: 0 0px 0px 0 rgba(0,0,0,.0); */ 
  box-shadow: none; 
  border-radius: var(--border-radius); 
  font-size: 12px; 
  border-width: 12.5px; 
  border-top: 1px solid var(--color-septenary); 
  border-bottom: 1px solid var(--color-septenary); 
  border-right: 1px solid var(--color-septenary); 
} 

.notice-warning { 
  border-left: 12.5px solid var(--color-orange); 
} 


.tablenav .actions {
  min-width: 300px;
}


#adminmenu .wp-submenu-head, 
#adminmenu a.menu-top {
  font-size: 10px;
  font-weight: 500;
  line-height: 18px;
  text-transform: uppercase;
  letter-spacing: 0px;
  }

  #adminmenu li.menu-top:hover, 
  /* #adminmenu li.opensub>a.menu-top,  */
  #adminmenu li>a.menu-top:focus {
    background-color: var(--color-tertiary);
    border-radius: var(--border-radius-small);
    /* padding: 0px; */
    /* margin: 10px 5px; */
  }

  #adminmenu li.opensub>a.menu-top {
    background-color: transparent;
  }

#adminmenu div.wp-menu-name {
  padding: 4px 8px 4px 12px;
}
#adminmenu div.wp-menu-name {
  padding: 10px 0px 0px 5px;
}

/* #adminmenu li>a.menu-top,  */
#adminmenu li.menu-top 
/* #adminmenu li.menu-top:hover,  */
/* #adminmenu li.opensub>a.menu-top  */
/* #adminmenu li>a.menu-top:focus  */
{
  /* margin: 2px 10px; */
  margin: 0px 5px;
}

/* #adminmenu div.wp-menu-image {
  display: none; 
} */

/* #adminmenu .wp-submenu-head, 
#adminmenu a.menu-top {
  font-size: 10px;
  font-weight: 500;
  line-height: 10px;
  text-transform: uppercase;
  letter-spacing: 0px;
} */

#adminmenu .wp-submenu-head, 
#adminmenu a.menu-top {
  font-size: 12px;
  font-weight: 600;
  line-height: 11px;
  text-transform: capitalize;
  letter-spacing: 0.5px;
}


#adminmenu li.menu-top {
  min-height: 30px;
}

ul#adminmenu a.wp-has-current-submenu:after, 
ul#adminmenu>li.current>a.current:after {
  display:none; 
}

/* Style our custom header items in the admin menu */
#adminmenu .custom-header a {
  pointer-events: none;
  cursor: default;
  color: white !important;
  /* background: var(--color-primary) !important; */
  text-transform: uppercase;
  font-weight: 600;
  font-size: 10px;
  padding: 0 0px;
  margin-top: 10px;
  margin-left: -25px;
}
#adminmenu .custom-header {
  margin: 0px; 
  margin-top: 10px; 
}
#adminmenu .custom-header a {
  pointer-events: none;
  cursor: default;
  color: white !important;
  /* background: var(--color-primary) !important; */
  text-transform: uppercase;
  font-weight: 700;
  font-size: 11px;
  padding: 0px;
  border-radius: var(--border-radius-small);
  margin: 0 5px;
  line-height: 18px;
}
#adminmenu .custom-header a .wp-menu-image { display: none; }
#adminmenu .custom-header a .wp-menu-name {
  color: var(--color-white);
}
#adminmenu li.menu-top {
  clear: both;
}
#adminmenu .custom-header div.wp-menu-name {
  padding: 5px 0px 5px 10px;
}


#adminmenu div.wp-menu-image {
  float: left;
  width: 30px;
  height: 30px;
  margin: 0;
  text-align: center;
}

#adminmenu div.wp-menu-image.svg {
  background-repeat: no-repeat;
  background-position: center;
  background-size: 16px auto;
}


.widefat .column-baum_author {
  width: 5em;
}

#adminmenu .wp-submenu a {
  color: #c3c4c7;
  color: rgba(240,246,252,.7);
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  padding: 5px 0;
}