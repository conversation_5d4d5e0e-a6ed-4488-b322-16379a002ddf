/* :root {
  --border-radius: 10px;
  --color-primary: #bc0c0c;
  --color-quaternary: #2d2d2d; 
} */

.baum-color-base { 
  color: #b4b4b4; /* Color 1 (base) */ 
} 

.baum-color-highlight { 
  color: var(--color-primary); /* Color 2 (highlight) */ 
} 

.baum-color-notifications { 
  color: #fff; /* Color 3 (notifications) */ 
} 

.baum-color-background { 
  color: #4b4b4b; /* Color 4 (background) */ 
} 

.right {
  float: right;
  text-align: right;  
}

.left {
  float: left;
  text-align: left;  
}

.center {
  margin: 0; 
  vertical-align: middle;
  text-align: center;  
}

/* Base colors */ 
body.wp-admin { 
  background-color: #ebebeb; 
  color: #111; 
} 

#adminmenu, 
#adminmenu .wp-submenu, 
#adminmenuback, 
#adminmenuwrap { 
  background-color: #e8e8e8; 
} 

#adminmenu a {
  color: #999999;
}

#adminmenu .wp-submenu a {
  color: #bebebe;
}

#adminmenu .wp-submenu .wp-submenu-head {
  color: #f2f2f2;
}

#adminmenu li.menu-top:hover, 
#adminmenu li.opensub>a.menu-top, 
#adminmenu li>a.menu-top:focus { 
  /* background-color: var(--color-primary);  */
  /* background-color: #323232;  */
  color: #ffffff; 
} 

#adminmenu li.menu-top.current>a.menu-top, 
#adminmenu .wp-has-current-submenu a.wp-has-current-submenu {
  color: #ffffff;
}

/* Highlight colors */
#wpadminbar, 
#wpadminbar .ab-top-menu > .menupop > .ab-sub-wrapper, 
#wpadminbar .ab-top-menu > .ab-item {
  /* background-color: var(--color-primary); */
  background-color: #505050; 
  color: #ffffff;
}

/* Notification colors */
#adminmenu .awaiting-mod, 
#adminmenu .update-plugins { 
  background-color: var(--color-primary); 
  color: #ffffff; 
} 

#adminmenu .wp-menu-name, 
#adminmenu .wp-menu-image:before {
  color: var(--color-septenary);
  transition: none;
}

#adminmenu .wp-menu-image.dashicons:before {
  transition: none !important;
  color: inherit !important;
}





#adminmenu .wp-menu-image.dashicons-before {
  transition: none !important;
}

#adminmenu .wp-menu-image.dashicons-before:before {
  color: rgba(255, 255, 255, 0.6) !important;
  font-size: 16px;
  transition: none !important;
  line-height: 16px;
}

#adminmenu li:hover .wp-menu-image.dashicons-before:before,
#adminmenu li:focus .wp-menu-image.dashicons-before:before,
#adminmenu li:active .wp-menu-image.dashicons-before:before {
  /* color: #bcbcbc !important;  */
  color: rgba(255, 255, 255, 1) !important;
  transition: none !important; 
}






/* Hover state for menu items */ 
#adminmenu li.menu-top:hover .wp-menu-name, 
#adminmenu li.menu-top:hover .wp-menu-image:before { 
  /* color: #ccc;  */
} 

/* Maintain the text and icon color after hover */ 
/* #adminmenu li.menu-top .wp-menu-name, 
#adminmenu li.menu-top .wp-menu-image:before { 
  color: #fff;  
}  */

/* Optional: Change color for active menu items */ 
#adminmenu li.current .wp-menu-name, 
#adminmenu li.wp-has-current-submenu .wp-menu-name, 
#adminmenu li.current .wp-menu-image:before, 
#adminmenu li.wp-has-current-submenu .wp-menu-image:before { 
  color: #fff !important; /* Active/Current menu item color */ 
} 

#wp-admin-bar-user-info .avatar { 
  border-radius: 50%; 
} 

#wpadminbar #wp-admin-bar-user-info .display-name { 
  display: none; 
} 

#wpadminbar #wp-admin-bar-user-info .username { 
  display: block; 
} 

#wpadminbar #wp-admin-bar-user-info .username { 
  color: #adadad; 
  font-size: 14px; 
} 

/* Default link color in the admin bar */
#wpadminbar .ab-item, 
#wpadminbar a.ab-item {
  color: #ffffff; /* Default text color */
  /* transition: color 0.3s ease;  */
  transition: none; 
}

/* Hover state for links in the admin bar */
#wpadminbar .ab-item:hover, 
#wpadminbar a.ab-item:hover {
  color: #fff; /* Hover color (change to red or your desired color) */
}

/* Optional: Change the background color on hover */
#wpadminbar .ab-item:hover, 
#wpadminbar a.ab-item:hover {
  background-color: #444444; /* Hover background color (change to gray or your desired color) */
}

/* Remove underline on hover */
#wpadminbar .ab-item:hover, 
#wpadminbar a.ab-item:hover {
  text-decoration: none; /* Remove underline on hover */
}

#wpadminbar .ab-top-menu>li.hover>.ab-item, 
#wpadminbar.nojq .quicklinks .ab-top-menu>li>.ab-item:focus, 
#wpadminbar:not(.mobile) .ab-top-menu>li:hover>.ab-item, 
#wpadminbar:not(.mobile) .ab-top-menu>li>.ab-item:focus {
  background: #343434;
  color: #fff;
}

#wpadminbar:not(.mobile)>#wp-toolbar a:focus span.ab-label, 
#wpadminbar:not(.mobile)>#wp-toolbar li:hover span.ab-label, 
#wpadminbar>#wp-toolbar li.hover span.ab-label {
  color: #fff;
}

#wpadminbar #wp-admin-bar-my-account.with-avatar>.ab-empty-item img, #wpadminbar #wp-admin-bar-my-account.with-avatar>a img {
  /* width: auto; */
  /* height: 16px; */
  /* padding: 0; */
  border: 1px solid #757575;
  /* background: #f0f0f1; */
  /* line-height: 1.********; */
  /* vertical-align: middle; */
  /* margin: -4px 0 0 6px; */
  /* float: none; */
  /* display: inline; */
  border-radius: 50%;
}

.wrap .wp-heading-inline+.page-title-action {
  float: none; 
  margin-top: 10px;
  line-height: 20px !important;
  height: auto;
  padding: 5px 20px;
  width: auto;
  font-size: 12px;
  display: inline-block;
}

.wp-core-ui .button, 
.wp-core-ui .button-primary, 
.wp-core-ui .button-secondary {
  display: inline-block;
  text-decoration: none;
  font-size: 12px;
  line-height: 30px;
  min-height: 30px;
  margin: 0;
  padding: 0 15px;
  cursor: pointer;
  border-width: 0px;
}

.button.media-button.button-primary, 
.page-title-action, 
.wrap .wp-heading-inline+.page-title-action, 
.wp-core-ui p .button.button-primary, 
.button-primary.action {
  background: var(--color-primary, #000);
  background-color: var(--color-primary, #000);
  /* font-size: 14px; */
  /* line-height: 14px; */
  /* padding: 14px 50px; */
  /* background-color: #333 !important; */
  border: 0 !important;
  color: #fff !important;
  text-shadow: none !important;
  box-shadow: none !important;
  /* outline: 0 !important; */
}

.wp-core-ui .button-group.button-small .button, 
.wp-core-ui .button.button-small {
  min-height: 30px;
  line-height: 30px;
  padding: 0 15px;
  font-size: 12px;
}

.wp-core-ui .button-group.button-large .button, 
.wp-core-ui .button.button-large { 
  min-height: 30px; 
  line-height: 30px; 
  padding: 0px 30px; 
  font-size: 12px; 
  font-weight: 500; 
} 

.media-router .media-menu-item {
  line-height: 18px;
  font-size: 12px;
}

.wrap .wp-heading-inline+.page-title-action {
  float: none; 
  margin-top: 10px;
  line-height: 20px !important;
  height: auto;
  padding: 5px 20px;
  width: auto;
  font-size: 12px;
  display: inline-block;
}


.wrap .wp-heading-inline+.page-title-action {
  margin: 10px;
  max-width: 350px;
  width: 200px;
  min-width: 0px;
  padding: 15px;
  line-height: 12px;
  font-weight: 500;
  text-align: center;
  border-radius: 10px; 
  /* text-transform: uppercase; */

  /* float: right; */
  margin-top: 25px;
  margin-left: 25px; 

  margin: 10px;
  max-width: 350px;
  width: auto;
  min-width: 0px;
  padding: 10px 20px;
  line-height: 12px;
  font-weight: 500;
  text-align: center;
  border-radius: 5px;
  text-transform: uppercase;
  font-size: 12px;

  top: -7px; 
}


table.wp-list-table {
  border-radius: 15px;
}
input,
.wp-core-ui select {
  border-radius: 10px;
}

.wp-core-ui p .button {
  vertical-align: baseline;
  border-radius: 10px;
  font-weight: 500;
  font-size: 12px;
  line-height: 12px;
  width: auto;
  background: #232323;
  border: 0;
  outline: 0;
  text-transform: uppercase;
  border-radius: 10px;
  padding: 5px 20px;
}

.wp-core-ui .submit .button {
  /* font-size: 16px; */
  line-height: 16px;
}

#wp-user-avatars-actions input[type=file], 
#wp-user-avatars-remove, #wp-user-avatars-media {
  margin: 10px;
  max-width: 350px;
  width: auto;
  min-width: 0px;
  padding: 5px 20px;
  line-height: 10px;
  font-weight: 700;
  text-align: center;
  border-radius: 10px;
  text-transform: uppercase;
  font-size: 11px;
}
.wrap .page-title-action:hover, 
.wrap .add-new-h2:hover, 
.tablenav .add-new-h2:hover, 
.bulk-actions input[type="submit"]:hover, 
.handle-actions .button-primary:hover, 
.post-type-attachment .media-button:hover, 
.acf-postbox .button-primary:hover, 
.acf-postbox .button-secondary:hover, 
.acf-postbox .acf-button:hover {
  background-color: inherit; 
  /* border: 0 !important; */
  color: #fff !important;
  opacity: 0.75; 
  opacity: 1; 
}
.wp-core-ui .button:hover, 
.wp-core-ui .button-secondary:hover, 
.wp-core-ui .button-primary:hover, 
.wp-core-ui .button-group.button:hover, 
.page-title-action:hover, 
.button.action:hover, 
.button-primary.action:hover, 
.button-secondary.action:hover {
  background-color: #111;
  border-color: transparent;
  /* border: 0;  */
  color: #aaa;
  opacity: 0.75; 
  opacity: 1; 
}
input[type=checkbox]:focus, 
input[type=color]:focus, 
input[type=date]:focus, 
input[type=datetime-local]:focus, 
input[type=datetime]:focus, 
input[type=email]:focus, 
input[type=month]:focus, 
input[type=number]:focus, 
input[type=password]:focus, 
input[type=radio]:focus, 
input[type=search]:focus, 
input[type=tel]:focus, 
input[type=text]:focus, 
input[type=time]:focus, 
input[type=url]:focus, 
input[type=week]:focus, 
select:focus, 
textarea:focus {
  border-color: rgb(175, 175, 175);
  box-shadow: none; 
  outline: 1px solid #646970; 

  outline: 1.5px solid #646970;
  /* color: rgb(31, 31, 31); */
  color: #646970;
  color: #2a2c30;
}


input[type=color], 
input[type=date], 
input[type=datetime-local], 
input[type=datetime], 
input[type=email], 
input[type=month], 
input[type=number], 
input[type=password], 
input[type=search], 
input[type=tel], 
input[type=text], 
input[type=time], 
input[type=url], 
input[type=week], 
select, 
textarea {
  box-shadow: none;
  border-radius: 10px;
  outline: 1px solid rgb(175, 175, 175); 
  background-color: #fff;
  color: #000;
  /* min-width: 3.5em !important;  */
}

.form-field input[type=email], .form-field input[type=number], .form-field input[type=password], .form-field input[type=search], .form-field input[type=tel], .form-field input[type=text], .form-field input[type=url], .form-field textarea {
  border-style: solid;
  border-width: 0;
  width: 95%;
}

#toplevel_page_logo_based_menu > a, 
#toplevel_page_logo_based_menu > a > div.wp-menu-image {
  display: none;
}

#adminmenu #toplevel_page_logo_based_menu a:hover, 
#adminmenu li#toplevel_page_logo_based_menu:hover {
  background-color: unset;
}

#adminmenu, 
#adminmenu .wp-submenu, 
#adminmenuback, 
#adminmenuwrap {
  /* width: 200px;  */
  /* Adjust this value to increase the width */
  width: 220px;
}

.folded #wpcontent, 
.folded #wpfooter {
  margin-left: 36px !important;
}

#wpcontent, 
#wpfooter {
  /* margin-left: 200px !important;  */
  /* Adjust this value to match the width above */
  margin-left: 220px !important;
}
  
#adminmenu .wp-submenu {
  /* left: 200px;  */
  /* Adjust this value to match the width above */
  left: 220px;
}


#adminmenu .wp-submenu .wp-submenu-head {
  color: #fff;
  font-weight: 800;
  font-size: 11px;
  margin: -2px -1px 4px -5px;
}


#adminmenu a.menu-top:focus+.wp-submenu, 
.js #adminmenu .opensub .wp-submenu, 
.js #adminmenu .sub-open, 
.no-js li.wp-has-submenu:hover .wp-submenu {
  top: -1px;
  border-radius: 8px;
}
#baum-user-info { display: none; } 
#baum-user-info {
  text-align: center;
  padding: 10px;
  /* border-bottom: 1px solid #ccc; */
  margin-bottom: 10px !important;
  margin-top: 15px !important;
}

#baum-user-info .baum-user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  display: inline-block;
  margin-bottom: 10px;
  border: 1px solid #444; 
}

#baum-user-info .baum-user-initials {
  width: 80px; /* Match avatar size */
  height: 80px; /* Match avatar size */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ececec, #c6c6c6);
  font-size: 24px;
  color: #333;
}

#baum-user-info .baum-user-fullname { 
  /* color: #ffffff; */ 
  /* color: #777; */ 
  color: #fff; 
  margin-bottom: 20px; 
  font-size: 16px; 
  font-weight: 900; 
} 

#baum-user-info .baum-user-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px; 
}
#baum-user-info .baum-user-buttons a {
  display: inline-block;
  width: 16px; /* Smaller icon size */
  height: 12.5px; /* Smaller icon size */
  line-height: 12px; /* Adjust line height to match icon size */
  border-radius: 5px;
  background-color: var(--color-quaternary); 
  color: #aaa;
  text-align: center;
  font-size: 10px; /* Smaller icon size */
}
#baum-user-info .baum-user-buttons a:hover {
  /* background-color: #444; */
  color: #fff;
}

#adminmenu { 
  margin-top: 30px;
  /* margin-top: 0px;  */
}

#adminmenu a:focus, 
#adminmenu a:hover, 
.folded #adminmenu .wp-submenu-head:hover {
  box-shadow: none !important;
}
.comment-ays, 
.feature-filter, 
.popular-tags, 
.stuffbox, 
.widgets-holder-wrap, 
.wp-editor-container, 
p.popular-tags, 
table.widefat { 
  border-radius: 10px;
  overflow: hidden;
}

.menu-item-handle, 
.widget .widget-top { 
  border-radius: 15px;
}
.widgets-chooser {
  border-radius: 15px !important;
  overflow: hidden;
}
.widgets-chooser .widgets-chooser-selected .widgets-chooser-button { 
  background: var(--color-primary); 
  color: #fff; 
}

.form-table td {
  padding: 10px;
  background-color: #eee;
  border-radius: 0px; 
}

/* Dark buttons */
.wp-core-ui .button,
.wp-core-ui .button-secondary,
.wp-core-ui .button-primary,
.wp-core-ui .button-group.button, 
.page-title-action, 
.button.action,
.button-primary.action,
.button-secondary.action {
    background-color: #222; /* Dark background color */
    border-color: #000; /* Dark border color */
    color: #fff; /* White text color */
    text-shadow: none; /* Remove text shadow */
    box-shadow: none; /* Remove box shadow */
}

.wp-core-ui .button:hover,
.wp-core-ui .button-secondary:hover,
.wp-core-ui .button-primary:hover,
.wp-core-ui .button-group.button:hover, 
.page-title-action:hover, 
.button.action:hover,
.button-primary.action:hover,
.button-secondary.action:hover {
    background-color: #333; /* Darker background on hover */
    border-color: #222; /* Darker border on hover */
    color: #fff; /* White text color */
}

.wp-core-ui .button:focus,
.wp-core-ui .button-secondary:focus,
.wp-core-ui .button-primary:focus,
.wp-core-ui .button-group.button:focus, 
.page-title-action:focus, 
.button.action:focus,
.button-primary.action:focus,
.button-secondary.action:focus {
    background-color: #333; /* Slightly lighter background on focus */
    border-color: #111; /* Slightly lighter border on focus */
    color: #fff; /* White text color */
}

/* Additional dark styles for various admin buttons */
.wrap .add-new-h2,
.tablenav .add-new-h2,
.bulk-actions input[type="submit"],
.handle-actions .button-primary,
.post-type-attachment .media-button,
.acf-postbox .button-primary,
.acf-postbox .button-secondary,
.acf-postbox .acf-button {
  background-color: #333 !important;
  border: 0 !important;
  color: #fff !important;
  text-shadow: none !important;
  box-shadow: none !important;
  outline: 0 !important; 
}

.wrap .add-new-h2:hover,
.tablenav .add-new-h2:hover,
.bulk-actions input[type="submit"]:hover,
.handle-actions .button-primary:hover,
.post-type-attachment .media-button:hover,
.acf-postbox .button-primary:hover,
.acf-postbox .button-secondary:hover,
.acf-postbox .acf-button:hover {
  background-color: inherit !important;
  border: 0 !important;
  color: #fff !important;
  opacity: 0.75; 
  opacity: 1; 
  outline: 0 !important; 
  background: #222 !important;
}

.wrap .add-new-h2:focus,
.tablenav .add-new-h2:focus,
.bulk-actions input[type="submit"]:focus,
.handle-actions .button-primary:focus,
.post-type-attachment .media-button:focus,
.acf-postbox .button-primary:focus,
.acf-postbox .button-secondary:focus,
.acf-postbox .acf-button:focus {
  background-color: inherit !important;
  border: 0 !important;
  color: #fff !important;
  opacity: 0.75; 
  opacity: 1; 
  outline: 0 !important; 
  background: #222 !important;
}

.wrap .add-new-h2:hover {
  background: #333 !important;
  border-color: none;
  color: #fff;
}

.wp-core-ui .button, 
.wp-core-ui .button-secondary, 
.wp-core-ui .button-primary, 
.wp-core-ui .button-group.button, 
.page-title-action, 
.button.action, 
.button-primary.action, 
.button-secondary.action {
  background-color: #232323;
  border: 0;
  color: #fff;
  text-shadow: none;
  box-shadow: none;
  text-transform: uppercase;
  /* font-weight: 600; */
  font-size: 12px;
  border-radius: 10px;
  border-radius: 5px;
  /* padding: 0px 20px; */
  border: 0;
  font-weight: 700;
}

.wp-core-ui .button-secondary:focus, 
.wp-core-ui .button.focus, 
.wp-core-ui .button:focus {
  /* border: 0; */
  color: #fff;
  box-shadow: none;
}

.wp-core-ui .button:active, 
.wp-core-ui .button-secondary:active, 
.wp-core-ui .button-primary:active, 
.wp-core-ui .button-group.button:active, 
.page-title-action:active, 
.button.action:active, 
.button-primary.action:active, 
.button-secondary.action:active,
.wp-core-ui .button:focus, 
.wp-core-ui .button-secondary:focus, 
.wp-core-ui .button-primary:focus, 
.wp-core-ui .button-group.button:focus, 
.page-title-action:focus, 
.button.action:focus, 
.button-primary.action:focus, 
.button-secondary.action:focus,
.wp-core-ui .button:hover, 
.wp-core-ui .button-secondary:hover, 
.wp-core-ui .button-primary:hover, 
.wp-core-ui .button-group.button:hover, 
.page-title-action:hover, 
.button.action:hover, 
.button-primary.action:hover, 
.button-secondary.action:hover {
  background-color: #232323;
  /* border: 0 !important; */
  /* color: #ccc; */
  /* opacity: 0.75; */
  opacity: 1; 
  /* outline: 0 !important; */
  box-shadow: none !important;
}


.hide-if-value .button,
.hide-if-value button {
  margin-left: 10px; 
}

.color-option {
  margin: 5px; 
}

.color-option.selected, 
.color-option:hover {
  background: #e7e7e7;
  border-radius: 10px;
  outline: 1px solid #b4b4b4;
}

/* .wrap h1, 
.wrap h2 {
  color: #000 !important;
  border-radius: 10px;
} */

/* 
.tags.column-tags { 
   display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word !important;
  -webkit-line-clamp: 5; 
}
 */

.tags.column-tags {
  width: 100%;
  overflow-y: -webkit-paged-x;
  max-height: 96px;
}
#adminmenu li.wp-menu-separator {
  height: 0;
  padding: 0;
  margin: 0;
}

#adminmenu div.wp-menu-name {
  padding: 8px 8px 8px 36px;
}

#adminmenu .wp-menu-image:before {
  font-size: 18px;
  width: 16px;
  height: 16px;
  line-height: 22px;
  display: inline-block;
  text-align: center;
  color: var(--color-primary) !important;
}

.current div.wp-menu-image:before, 
#adminmenu a.current:hover div.wp-menu-image:before, 
#adminmenu a.wp-has-current-submenu:hover div.wp-menu-image:before, 
#adminmenu li.wp-has-current-submenu a:focus div.wp-menu-image:before, 
#adminmenu li.wp-has-current-submenu.opensub div.wp-menu-image:before, 
#adminmenu li.wp-has-current-submenu:hover div.wp-menu-image:before {
  /* color: black !important; */
  color: white !important;
}

#adminmenu li.wp-has-current-submenu:hover div.wp-menu-image:before,
#adminmenu a.wp-has-current-submenu:hover div.wp-menu-image:before, 
#adminmenu .wp-has-current-submenu:hover div.wp-menu-image:before, 
#adminmenu .wp-has-current-submenu div.wp-menu-image:before {
  color: var(--color-primary) !important;
}

.wp-core-ui .quicktags-toolbar input.button.button-small {
  margin: 2px;
  border-radius: 4px; 
  background: #222; 
  color: #eee; 
  /* font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;  */
  text-transform: lowercase; 
}

.quicktags-toolbar {
  display: none;
}

/* input[type=checkbox], input[type=radio] {
  border: 1px solid #c8c8c8;
  border-radius: 5px;
  background: #fff;
  color: #090909;
  font-size: 18px;
  clear: none;
  cursor: pointer;
  display: inline-block;
  line-height: 21px;
  margin: 0px 10px 0px 0px;
  outline: 0;
  padding: 2.5px !important;
  text-align: center;
  vertical-align: middle;
  height: 18px;
  width: 18px;
  min-width: 21px;
  min-height: 21px;
  -webkit-appearance: none;
  box-shadow: none;
  transition: none;
} */

/* input[type=radio] {
  border-radius: 15px;
} */

#profile-page .form-table #rich_editing {
  margin-right: 10px;
}

.acf-tab-group li a {
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
}

.form-table td p {
  margin-top: 10px;
  margin-bottom: 0;
}

#profile-page .form-table textarea {
  width: 100%;
  margin-bottom: 0px;
}

input[type=color], 
input[type=date], 
input[type=datetime-local], 
input[type=datetime], 
input[type=email], 
input[type=month], 
input[type=number], 
input[type=password], 
input[type=search], 
input[type=tel], 
input[type=text], 
input[type=time], 
input[type=url], 
input[type=week], 
select, 
textarea, 
/* .form-table .button,  */
.form-table input[type="text"], 
.form-table input[type="email"], 
.form-table input[type="url"], 
.form-table input[type="password"], 
.form-table select, 
.form-table textarea, 
.form-table .wp-editor-container, 
.form-table .wp-editor-container textarea {
  border: 0;
  outline: 1px solid rgb(175, 175, 175);
  min-width: auto;
  /* min-width: 531px; */
  font-weight: 400; 
  color: rgb(175, 175, 175); 
}

.wp-core-ui select {
  color: rgb(175, 175, 175); 
}

.wp-core-ui select:hover {
  color: rgb(31, 31, 31);
  color: #646970;
}

.wp-core-ui select:focus {
  /* border-color: var(--color-primary, #222); */
  /* color: rgb(31, 31, 31); */
  color: #646970;
  box-shadow: 0;
}

.create-application-password label {
  margin-left: 5px;
  margin-bottom: 5px;
}

#new_application_password_name {
  color: #646970;
}

body select {
  font-family: Arial, Helvetica, sans-serif !important;
  font-weight: 600; 
}

body select optgroup {
  font-family: Arial, Helvetica, sans-serif !important; 
  font-weight: 600; 
}

body select option {
  font-family: Arial, Helvetica, sans-serif !important; 
  font-weight: 600; 
}

textarea {
  padding: 10px; 
  line-height: 1.75; 
}


.form-table input:hover:focus, 
.form-table select:hover:focus, 
.form-table textarea:hover:focus, 
.form-table .wp-editor-container:hover:focus, 
.form-table .wp-editor-container textarea:hover:focus,
.form-table input:focus, 
.form-table select:focus, 
.form-table textarea:focus, 
.form-table .wp-editor-container:focus, 
.form-table .wp-editor-container textarea:focus {
  /* outline: 2.5px solid rgb(31, 31, 31); */
  /* outline: 1px solid #646970; */
  outline: 1.5px solid #646970;
  /* color: rgb(31, 31, 31); */
  color: #646970;
  color: #2a2c30;
}

.edit-tag-actions #delete-link {
  float: right; 
  display: none;
}

/* .acf-input input:hover:focus, 
.acf-input select:hover:focus, 
.acf-input textarea:hover:focus {
  outline: none; 
} */


.form-table input[readonly]:focus,
.form-table input[readonly] {
  outline: 1px solid rgb(175, 175, 175) !important;
  color: rgb(175, 175, 175) !important; 
  border-color: none;
}



.wrap form#createuser .acf-field input[type=email], .wrap form#createuser .acf-field input[type=number], .wrap form#createuser .acf-field input[type=password], .wrap form#createuser .acf-field input[type=search], .wrap form#createuser .acf-field input[type=text], .wrap form#createuser .acf-field input[type=url], .wrap form#createuser .acf-field select, .wrap form#your-profile .acf-field input[type=email], .wrap form#your-profile .acf-field input[type=number], .wrap form#your-profile .acf-field input[type=password], .wrap form#your-profile .acf-field input[type=search], .wrap form#your-profile .acf-field input[type=text], .wrap form#your-profile .acf-field input[type=url], .wrap form#your-profile .acf-field select {
  max-width: 100%;
  border: 0; 
}



input[type="range"] {
  -webkit-appearance: none; /* Override default CSS styles */
  width: 50%;
  margin: 10px 0;
  background: transparent; /* Otherwise white in Chrome */
}

/* WebKit (Chrome, Safari) */
input[type="range"]::-webkit-slider-runnable-track {
  width: 50%;
  height: 8px;
  cursor: pointer;
  background: #ddd;
  border-radius: 4px;
  border: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #000;
  cursor: pointer;
  margin-top: -6px; /* Centers the thumb on the track */
}

/* Firefox */
input[type="range"]::-moz-range-track {
  width: 50%;
  height: 8px;
  cursor: pointer;
  background: #ddd;
  border-radius: 4px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #000;
  cursor: pointer;
  border: none;
}

/* Internet Explorer/Edge */
input[type="range"]::-ms-track {
  width: 50%;
  height: 8px;
  cursor: pointer;
  background: transparent;
  border-color: transparent;
  color: transparent;
}

input[type="range"]::-ms-fill-lower {
  background: #ddd;
  border-radius: 4px;
}

input[type="range"]::-ms-fill-upper {
  background: #ddd;
  border-radius: 4px;
}

input[type="range"]::-ms-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #000;
  cursor: pointer;
  border: none;
}

/* Remove focus outline */
input[type="range"]:focus {
  outline: none;
}






/* Hide the default checkbox and radio */
input[type="radio"],
input[type="checkbox"] {
  appearance: none; /* Remove default styling */
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 20px;
  height: 20px;
  margin: 10px 0;
  cursor: pointer;
  position: relative;
  /* border: 2px solid #444;  */
  border-radius: 5px; 
  background-color: white;
  margin-right: 5px;
}

/* Custom radio button circle */
input[type="radio"] {
  border-radius: 50%; /* Make it circular */
}

/* Custom checked styles */
input[type="checkbox"]:checked,
input[type="radio"]:checked {
  /* background-color: #000;  */
  /* border-color: #000;  */
}

input[type="checkbox"]::before {
  content: "✓";
  font-size: 16px;
  font-weight: bold;
  color: #222;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 0%);
  opacity: 0;
}

/* Show checkmark when checked */
input[type="checkbox"]:checked::before {
  opacity: 1;
}

/* Custom radio button dot */
input[type="radio"]::before {
  content: "";
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
}

/* Show dot when radio is checked */
input[type="radio"]:checked::before {
  opacity: 1;
  background-color: white;
}





input[type="radio"]:checked::before {
  opacity: 1;
  background-color: #000;
}

input[type=radio]:checked::before {
  content: "";
  border-radius: 50%;
  width: .5rem;
  height: .5rem;
  margin: 0;
  /* background-color: #000; */
  line-height: 1.14285714;
}

input[type=checkbox]:checked::before, 
input[type=radio]:checked::before {
  float: left;
  display: inline-block;
  vertical-align: middle;
  /* width: 1rem; */
  speak: never;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


input[type=checkbox]:checked::before {
  /* content: url("data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%27http%3A%2F%2Fwww.w3.org%2F2000%…9.67l1.34-1.25%202.57%202.4z%27%20fill%3D%27%233582c4%27%2F%3E%3C%2Fsvg%3E"); */
  /* margin: -.1875rem 0 0 -.25rem; */
  margin: 0;
  content: "✓"; /* Unicode checkmark */
  height: 16px;
  width: 16px;
}

/* .acf-field label input[type="checkbox"] {
  display: none; 
} */

.acf-postbox label:has(input[type="checkbox"]) {
  display: none; 
}

.acf-input ul.acf-checkbox-list li label:has(input[type="checkbox"]) {
  display: inline; 
}

.acf-field .acf-input label:has(input[type="checkbox"]) {
  display: inline; 
}

.form-table input.tog, 
.form-table input[type=radio] {
  margin: 0px;
  margin-right: 5px;
  float: none;
}

.form-table input:hover, 
.form-table select:hover, 
.form-table textarea:hover, 
.form-table .wp-editor-container:hover, 
.form-table .wp-editor-container textarea:hover {
  /* outline: 2.5px solid rgb(31, 31, 31); */
  /* outline: 1px solid #646970; */
  /* color: rgb(31, 31, 31); */
  color: #646970;
}

#date_format_custom {
  min-width: 140px;
}

.wrap button, 
.wrap .button, 
.wrap .page-title-action {
  border: 1px solid var(--color-octonary) !important;
  border-bottom: 0px !important;
}

/* .wrap button,
.wrap .button, 
.wrap .page-title-action {
   outline: 1px solid var(--color-senary) !important; 
} */

.wrap button:active,
.wrap button:hover,
.wrap button:focus,
.wrap .button:active, 
.wrap .button:hover, 
.wrap .button:focus {
  /* outline: 1px solid var(--color-senary) !important;  */
}

.wrap .page-title-action:active, 
.wrap .page-title-action:hover, 
.wrap .page-title-action:focus {
  /* background: var(--color-primary) !important; */
  /* border-color: var(--color-primary); */
  /* outline: 1px solid var(--color-senary) !important;  */
  /* color: #fff; */
  /* box-shadow: none; */
  /* outline: 0; */
  opacity: 1; 
}

#baum_gdelt_top_keywords_widget.postbox .inside {
  margin: 0;
  position: relative; 
  padding: 0;
  line-height: 1;
  font-size: 12px;
}

form > h2 + p {
  font-weight: 400;
  /* background: #efefef; */
}

#wpadminbar .ab-icon, 
#wpadminbar .ab-item:before, #wpadminbar>#wp-toolbar>#wp-admin-bar-root-default .ab-icon, 
.wp-admin-bar-arrow {
  display: none;
}

#adminmenu .wp-not-current-submenu li>a, 
.folded #adminmenu .wp-has-current-submenu li>a {
  transition: none;
}


.nav-tab-active, 
.nav-tab-active:focus, 
.nav-tab-active:focus:active, 
.nav-tab-active:hover {
  /* border-bottom: 1px solid transparent; */
  background: transparent;
  /* color: #000; */
}

.nav-tab {
  font-size: 13px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}


#adminmenu a:has(.fs-submenu-item.pricing.upgrade-mode) {
  margin: 0;
  padding: 0;
}

.fs-submenu-item.pricing.upgrade-mode {
  display: none;
}

.wp-core-ui .button, 
.wp-core-ui .button-primary, 
.wp-core-ui .button-secondary {
  display: inline-block;
  text-decoration: none;
  font-size: 10px;
  line-height: 25px;
  min-height: 25px;
  margin: 0;
  padding: 0 15px;
  cursor: pointer;
  border-width: 0px;
}


.wp-core-ui .button, 
.wp-core-ui .button-primary, 
.wp-core-ui .button-secondary {
  display: inline-block;
  text-decoration: none;
  font-size: 10px;
  line-height: 25px;
  min-height: 25px;
  /* margin-top: -2.5px; */
  /* margin-top: -10px; */
  padding: 0 15px;
  cursor: pointer;
  border-width: 0px;
  position: relative;
  /* top: 5px; */
}


.plugin-install-php .plugin-card {
  border-radius: 10px;
}

.plugin-card-bottom { 
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
}

.filter-links li>a:focus, 
.filter-links li>a:hover, 
.show-filters .filter-links a.current:focus, 
.show-filters .filter-links a.current:hover {
  color: var(--color-primary);
}



