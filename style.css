/*
Theme Name: Baum Press
Theme URI:
Description:
License:
License URI:
Author:
Author URI:
Version: 1.1.9
Text Domain: baumpress
Domain Path:
Tags:
*/

/**
 * BaumPress Main Stylesheet
 *
 * This file imports all the organized CSS modules for the BaumPress theme.
 * The CSS is organized into logical modules for better maintainability.
 *
 * @package BaumPress
 * @since 1.0.0
 */

/* ==========================================================================
   CSS IMPORTS - ORGANIZED MODULES
   ========================================================================== */

/* Core UI Elements */
@import url('style-base.css');

/* WordPress Overrides */
@import url('style-wp-overrides.css');

/* Card Components */
@import url('style-baum-cards.css');

/* Time Capsule Component */
@import url('style-time-capsule.css');
/* Mobile Responsive Styles */
@import url('mobile/style-mobile-layout.css');

/* Theme Variables and Core Styles */
/* ==========================================================================
   THEME VARIABLES & CORE STYLES
   ========================================================================== */

/* Disable selection globally */
body {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Re-enable selection only for desired areas */
/* .comments,  */
.baum-post-byline-container,
.baum-post-title,
.comment-text,
.entry-content {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.baum-card {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}


.author-title { pointer-events: none; }



/* p.baum-foot-text {
  background: var(--color-primary);
  color: var(--color-white);
  display: inline;
} */
::marker {
  color: var(--color-primary);
  font-weight: 900;
}

::selection {
  background: var(--color-gray);
  color: var(--color-black);
  color: var(--color-white);
}

p:focus-visible {
  outline-color: -webkit-focus-ring-color;
  outline-color: var(--color-primary);
  outline-style: auto;
  outline-width: 5px;
}

.light-theme { display: block; }
.dark-theme { display: none; }
.show { display: block; }
.hide { display: none; }

body,
.body {
  min-height: 100vh;
  line-height: 1.75;
}

.wp-element-button,
body,
.body,
button,
.button,
html,
.html,
input,
.input,
select,
.select,
textarea,
.textarea {
  /* font: 400 14px/20px serif; */
  /* font-family: Roboto, sans-serif; */
  -webkit-font-smoothing: antialiased;
}

/* a {
  font-weight: 600;
} */

body, .body {
  background: var(--color-body);
  min-height: 100vh;
  color: var(--color-body-text);
  /* color: var(--color-quaternary);   */
}

p, .p {
  font-size: 18px;
  line-height: 23px;
  font-size: 14px;
  line-height: 1.75;
}

img, .img {
  background: var(--color-black);
}

.small,
small {
  font-size: 80%;
}

.smaller, small small {
  font-size: 70%;
}

html, .html {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

*,
*::before,
*::after { box-sizing: border-box; }

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 800;
  /* font-family: 'erbaum'; */
  /* text-transform: uppercase !important; */
  /* font-weight: 300; */
  margin-bottom: 5px;
  /* font-weight: 500;  */
  letter-spacing: 0;
  color: var(--color-body-text);
}

.h1 a,
.h2 a,
.h3 a,
.h4 a,
.h5 a,
.h6 a,
h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  color: inherit;
  /* font-weight: 1000;  */
}

.h1 strong,
.h2 strong,
.h3 strong,
.h4 strong,
.h5 strong,
.h6 strong,
h1 strong,
h2 strong,
h3 strong,
h4 strong,
h5 strong,
h6 strong {
  font-weight: 1000;
}

h1, .h1 {
  font-weight: 1000;
}

code, .code {
  padding: 2px;
  margin: 0 2px;
  font-size: 100%;
  white-space: nowrap;
  background: var(--color-octonary);
  border: 5px solid var(--color-senary);
}

b, .b, strong, .strong { font-weight: 900; }
/* em { color: var(--color-gray); } */

.wp-element-button,
button,
.button {
  margin-bottom: 0;
  border-radius: var(--border-radius);
  padding: 0 50px;
}

.baum-timeline-img {
  overflow: hidden;
}

.baum-timeline-img .img,
.baum-timeline-img img {
  background-color: gray;
  width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}

.baum-timeline {
  display: block;
}

.baum-accordion {
  display: block;
}

.baum-button {
  display: block;
}

.baum-input {
  display: block;
}

.h6, h6 { text-transform: capitalize; }
.figure, figure { margin: 0; }
.ul, ul {
  list-style: square inside;
  list-style: disc inside;
}

/* .entry-content p { padding: 0 10px; }  */
.entry-content {
  margin: 0 10px !important;
  max-width: 733px;
}

.page-template-page-fullwidth .entry-content {
  max-width: 100%;
}

.entry-content .img,
.entry-content img {
  border-radius: var(--border-radius);
}

.entry-content .ul .li,
.entry-content ul .li,
.entry-content .ul li,
.entry-content ul li {
  padding-right: 20px;
  left: 10px;
  position: relative;
}

input[type="checkbox"] {
  display: grid;
  place-content: center;
}

input[type="checkbox"] {
  appearance: none;
  background-color: #fff;
  margin: 0;
  font: inherit;
  color: currentColor;
  width: 1.15em;
  height: 1.15em;
  border: 0.15em solid currentColor;
  border-radius: 0.15em;
  transform: translateY(-0.075em);
  float:left;
  margin-right: 5px;
  margin-bottom: 10px;
  margin-top: 5px;
}

input[type="checkbox"]::before {
  content: "";
  width: 10px;
  height: 10px;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  box-shadow: inset 1em 1em var(--color-primary);
}

input[type="checkbox"]:checked::before { transform: scale(1); }

.wp-element-button,
#baum-main .tnp-widget input.tnp-submit,
button#leaky-paywall-submit,
#leaky-paywall-registration-next,
.button,
button,
input[type="submit"],
input[type="reset"],
input[type="button"] {
  color: var(--color-button-text);
  border: none;
  /* background: linear-gradient(to bottom, var(--color-gradient-top), var(--color-gradient-bottom)) 10% 10%; */
  /* background: var(--color-button-bg);  */
  /* background: var(--color-gray-bg) !important; */
  font-size: 12px;
  line-height: 14px;
  letter-spacing: normal;
  border-radius: var(--border-radius);
  padding: 0 50px;
}

.baum-button-icon {
  position: relative;
  left: -15px;
  top: -2px;
  border-right: var(--baum-button-icon-border);
  height: 22px;
  /* display: inline-block; */
  padding-right: 10px;
}

.wp-element-button,
.a.button,
a.button,
.button .a,
.button a {
  text-decoration: none;
}

.wp-element-button:hover,
.wp-element-button:focus,
button#leaky-paywall-submit:hover,
#leaky-paywall-registration-next:hover,
button#leaky-paywall-submit:focus,
#leaky-paywall-registration-next:focus,
.button:hover,
button:hover,
input[type="submit"]:hover,
input[type="reset"]:hover,
input[type="button"]:hover,
.button:focus,
button:focus,
input[type="submit"]:focus,
input[type="reset"]:focus,
input[type="button"]:focus {
  color: var(--color-button-text-primary);
  border: var(--color-button-border);
  /* border-color: var(--color-primary); */
  /* background: var(--color-button-bg); */
  /* background: var(--color-secondary); */
  /* background: var(--color-primary); */
  /* background: var(--color-button-bg);  */
  opacity: 0.75;
  outline: 0;
  /* border-radius: var(--border-radius); */
}

.button#more:active,
.button#more:focus,
.button#more:hover,
button#more:active,
button#more:focus,
button#more:hover {
  background: var(--color-button-bg);
}

div.tnp form.tnp-form input.tnp-email,
input[type="email"],
input[type="number"],
input[type="search"],
input[type="text"],
input[type="tel"],
input[type="url"],
input[type="password"],
.textarea,
textarea,
.select,
select {
  /* color: var(--color-body-text) !important; */
  color: var(--color-quaternary) !important;
  /* border: var(--input-border) !important; */
  border: var(--input-border);
  background: var(--input-background) !important;
  background-color: var(--input-background) !important;
  margin: 5px 0 !important;
  font-weight: 600 !important;
}

footer div.tnp form.tnp-form input.tnp-email,
footer input[type="email"],
footer input[type="number"],
footer input[type="search"],
footer input[type="text"],
footer input[type="tel"],
footer input[type="url"],
footer input[type="password"],
footer .textarea,
footer textarea,
footer .select,
footer select {
  background-color: var(--color-quaternary) !important;
  color: var(--color-primary) !important;
  line-height: 32px;
  color: white!important;
  border-radius: var(--border-radius-small);
  height: 32px;
  font-weight: 600 !important;
  /* outline: 1px solid var(--color-quinary); */
  /* border: 2.5px solid var(--color-primary) !important; */
  /* border: 2.5px solid var(--color-primary) !important; */
}

div.tnp form.tnp-form input.tnp-email:focus,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
input[type="text"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
.textarea:focus,
textarea:focus,
.select:focus,
select:focus {
  outline: 2.5px solid var(--color-primary);
}

.comment-form-comment label { display: none; }
/* .comments { margin: 5px; } */
.comments form { margin: 5px; }
.comments form p { margin-bottom: 10px;  }

.comments textarea {
  width: 100%;
  min-height: 150px;
  margin-bottom: 0;
}

.comments ol li {
  list-style-type: none;
}

.comments ol li .comment-author img {
  border-radius: var(--border-radius);
  border-radius: 50%;
  float: left;
}

.baum-meme .comments {
  margin: 0;
}

.baum-meme .comments form {
  margin: 5px;
  margin: 0;
}

.baum-meme .comments form p {
  margin-bottom: 0px;
}

.says { display: none; }
.comment-author cite.fn {
  float: left;
  font-style: normal;
  font-weight: bold;
  padding: 10px;
  line-height: 1.25;
  vertical-align: middle;
  display: flex;
  font-size: 16px;
  max-width: 200px;
}

.comment-meta {
  display: flex;
  /* float: right; */
  float: left;
  /* font-size: 16px; */
  font-size: 11px;
  /* line-height: 42px; */
  /* line-height: 35px; */
  line-height: 28px;
}

/* .comment-body p {
  margin-left: 10px;
  float: right;
  background: var(--card-bg);
  border: var(--card-bottom-border);
  border-radius: var(--border-radius);
  padding: 10px;
  margin: 10px 0;
  margin-left: 84px;
  min-height: 75px;
} */

/* .comment-body {
  margin-bottom: 15px;
} */

.comment-body .comment-content {
  margin-left: 10px;
  /* float: right; */
  /* background: var(--card-bg); */
  /* border: var(--card-bottom-border); */
  border-radius: var(--border-radius);
  padding: 10px;
  margin: 10px 0;
  /* margin-left: 84px; */
  /* width: 100%; */
  /* min-height: 75px; */
  /* margin-bottom: 50px; */
  width: calc(100% - 95px);
  min-height: 75px;
  margin-bottom: 50px;
  float: right;
}

.comment-body .comment-content p {
  margin-bottom: 10px;
  font-weight: 500;
}

.input,
input[type="email"],
input[type="number"],
input[type="search"],
input[type="text"],
input[type="tel"],
input[type="url"],
input[type="password"],
.textarea,
textarea,
.select,
select {
  background: white;
}

.required {
  color: var(--color-red);
}

.input:hover,
input[type="email"]:hover,
input[type="number"]:hover,
input[type="search"]:hover,
input[type="text"]:hover,
input[type="tel"]:hover,
input[type="url"]:hover,
input[type="password"]:hover,
.textarea:hover,
textarea:hover,
.select:hover,
select:hover,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="search"]:focus,
input[type="text"]:focus,
input[type="tel"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
.textarea:focus,
textarea:focus,
.select:focus,
select:focus {
  /* outline: 0; */
  /* border: var(--color-button-border); */
  border-color: var(--color-primary);
  /* border-color: var(--color-primary); */
}

.wp-element-button.disabled,
.wp-element-button[disabled],
.button.disabled,
.button[disabled],
button.disabled,
button[disabled],
.input.disabled,
.input[disabled],
input.disabled,
input[disabled],
.a.disabled,
.a[disabled],
a.disabled,
a[disabled] {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.5;
}

.input,
input[type="email"],
input[type="number"],
input[type="search"],
input[type="text"],
input[type="tel"],
input[type="url"],
input[type="password"],
.textarea,
textarea,
.select,
select {
  border-radius: var(--border-radius);
}

#newsletter,
#donate,
#more {
  width: 360px;
  /* height: 42px; */
  margin: auto;
}

/* .fal:active, .fal:hover { font-weight: 700; } */
/* .fa-link { font-size: 12px; }  */
/* .button .fa-link { font-size: 14px; }  */
/* h1 .fa-link { font-size: 16px; }  */

/* .fa-tree { color: var(--color-primary); }  */

.navbar-spacer {
  display: block;
  width: 100%;
  height: 50px;
}

.fixed {
  position: fixed;
}


.button-thumb,
a.button-thumb {
  line-height: 16px;
  height: 48px;
  color: white;
  font-size: 18px;
  background: var(--color-quaternary);
  width: 75px;
}

a.button-thumb:hover {
  background: var(--color-secondary);
}

.navbar {
  display: block;
  width: 100%;
  height: 42px;
  /* background: linear-gradient(to bottom, var(--color-gradient-top), var(--color-gradient-bottom)) 10% 10%; */

  /* background: var(--color-navbar-bg);  */
  /* background: var(--color-denary);  */
  z-index: 1;
  /* border-top: 3px solid; */
  /* border-image: linear-gradient(to right, blue, red) 10% 10%; */
  /* top: 0; */
  /* left: 0; */
  /* border-bottom: var(--card-border); */

  /* border-bottom: 1px solid var(--color-senary); */
  /* border-bottom: 1px solid var(--color-septenary); */
}

.navbar-list {
  list-style: none;
  margin-bottom: 0;
}

.navbar-item { margin-bottom: 0; }

.navbar-link {
  /* text-transform: uppercase; */
  /* font-size: 16px; */
  /* font-weight: 500; */
  /* letter-spacing: .1rem; */
  /* margin-right: 35px; */
  text-decoration: none;
  /* line-height: 40px; */
  color: var(--color-navbar-link);
  vertical-align: top;
  font-size: 16px;
  line-height: 21px;
}

.baum-home-date-headline {
  line-height: 16px;
  max-width: 240px;
  margin: auto;
}

.baum-home-date-headline h4 {
  /* color: var(--color-black);  */
  width: 100%;
  display: inline-block;
  top: 0;
}

/* .menu .menu-item:before {
  margin: 0px;
  width: 20px;
  display: inline-block;
} */

.faw:before {
  width: 2em;
}

.baum-inf-more-wrap {
  margin: auto;
  /* margin-top: 50px; */
  width: 100%;
  text-align: center;
  display: inline-block;
  padding: 5px;
}

.baum-inf-more-wrap button,
.baum-inf-more-wrap .button {
  font-weight: 1000;
}

#infscr-loading { display: none !important; }

.container {
  max-width: 1110px;
  width: 1110px;
}

.navbar-home .logo {
  height: 70px;
}

.navbar-home .logo img {
  height: 32px;
  width: auto;
  margin: auto;
}

.navbar-top {
  display: grid;
  grid-template-columns: 1fr 420px 1fr;
  margin: auto;
  /* max-width: 1050px; */
  padding: 0 5px;
}

.sidebar-active .navbar-top {
  max-width: 1285px;
}

/* .sidebar-active .navbar-center {
  line-height: 40px;
  text-align: left;
} */

/* .navbar-center {
  line-height: 40px;
  text-align: center;
} */

.navbar-center span { font-weight: 800; }

.navbar-center img {
  height: 25px;
  display: block;
  margin: 5px auto;
  opacity: 1;
  margin-top: 10px;
}

.navbar-left { padding-left: 15px; }

#baum-main,
#main {
  min-height: calc(100vh - 300px);
}


#baum-sidebar {
  /* margin-top:10px; */
  width: 150px;
  /* float: left; */
  position: fixed;
  overflow-y: hidden;
  height: 100vh;
  z-index: 100;
  /* box-shadow: -10px 0 20px 0 black; */
}

#dragbar {
  /* background-color:black; */
  height:100%;
  float: right;
  width: 3px;
  cursor: col-resize;
}

#ghostbar {
  width:3px;
  background-color:gray;
  opacity:0.5;
  position:absolute;
  cursor: col-resize;
  z-index:999
}


.menu-sidebar-spacer {
  float: left;
  width: 250px;
  height: 100%;
  display: block;
}

.menu-sidebar {
  z-index: 200;
  background: var(--menu-sidebar-bg);
  border-right: var(--menu-sidebar-border);
  width: 100%;
  height: 100vh;

  padding-top: 10px;
  /* position: fixed; */
  /* left: 0; */
  /* top: 0; */
}

.navbar-right a { font-size: 16px; }

.navbar-right .navbar-list {
  /* position: fixed; */
  right: 0;
  top: 0;
  display: grid;
  grid-template-columns: 25px 25px;
  float: right;
  grid-column-gap: 20px;
}

.navbar-2 .navbar-right .navbar-list {
  display: flex;
  display: block;
  width: 370px;
}

.navbar-2 .navbar-left {
  float: left;
}

.navbar-2 .navbar-center {
  float: left;
  text-align: left;
  /* display: flex;  */
}

.navbar-2 .navbar-right {
  float: right;
  /* display: flex;  */
}

.navbar-2 .navbar-top {
  grid-template-columns: auto 1fr auto;
}

.navbar-2 .navbar-top .search-icon {
  position: relative;
  top: 3px;
  left: 17px;
  z-index: 250;
  color: var(--color-search-icon);
  width: 0;
  height: 0;
}

.navbar-2 .menu-search {
  margin-top: 8px;
}

.navbar-2 #search {
  width: calc(100% - 15px);
}













.menu .menu-item,
.menu-link {
  display: flex;
  padding: 0px 5px;
}




/* .navbar-3 .menu li:hover { background: #333; } */

.navbar-3 .navbar-left.navbar-left-logo {
  /* padding-top: 5px; */
  margin-right: 5px;
}

/* .navbar-3 .navbar-top .navbar-right,
.navbar-3 .navbar-top .navbar-center {
  display: flex;
  line-height: 50px;
} */

.navbar-home.navbar-3 .navbar-top .navbar-right {
  line-height: 42px;
}

.navbar-3 .menu .menu-item a:hover {
  color: var(--color-menu-link-active);
}

.navbar-3 .menu .menu-item a,
.navbar-3 .menu-link {
  width: 100%;
  cursor: pointer;
  font-size: 16px;
  color: var(--color-menu-link);
  font-weight: 700;
}

.navbar-3 .menu .menu-item a,
.navbar-3 .menu-link {
  line-height: 15px;
}
.navbar-3 .menu .menu-item a,
.navbar-3 .menu-link {
  line-height: 25px;
}

/* .navbar-3 .navbar-top .menu { background: none; } */

.navbar-3 .navbar-top .menu li a {
  width: auto;
  padding: 5px 10px;
  font-size: 12px;
  text-transform: uppercase;
  text-align: center;
  vertical-align: middle;
  line-height: 24px;
}

/* .navbar-3 .navbar-top .menu li.menu-item {
  display: inline;
  text-align: center;
  vertical-align: middle;
} */

.navbar-3 .navbar-top .menu li.menu-item {
  display: inline;
  height: 50px;
  line-height: 50px;
}

.navbar-3 .menu-main-container {
  width: 100%;
}


.navbar-3 .navbar-top {
  grid-template-columns: auto 1fr auto;
  /* top: 7.5px; */
}

/* .navbar-3 ul.menu {
  display: inline-block;
  padding: 0px;
  position: relative;
  top: -2px;
  text-align: right;
} */


.home .navbar-3 ul.menu {
  text-align: center;
}


.navbar-3 ul.menu :not(.sub-menu) li {
  display: inline-block;
}

/* .navbar-3 .sub-menu {
  display: none;
} */

.navbar-3 .navbar-right {
  float: right;
  text-align: right;
  /* display: flex;  */
}

.navbar-3 .navbar-right ul.menu li a {
  margin: 0 10px;
  text-align: center;
  padding: 0;
}

/* .navbar-top .search-icon {
  position: relative;
  top: 1px;
  left: 16px;
  z-index: 250;
  color: var(--color-gray);
  width: 0;
  height: 0;
} */
.navbar-3 .navbar-top .search-icon {
  top: 10px;
  position: relative;
  left: 7.5px;
  z-index: 250;
  color: var(--color-septenary);
  width: 0;
  height: 0;
}

.navbar-3 .menu-search {
  margin-top: 5px;
}
  /* .navbar-3 #search {
    width: calc(100% - 15px);
  } */

.navbar-3 .navbar-right .navbar-list {
  display: flex;
  display: block;
  max-width: 370px;
}


/* .navbar-home .navbar-top .menu li {
  width: auto;
  padding: 0px;
} */


.home .navbar-3 .navbar-top.container {
  padding-left: 5px;
}

.home .navbar-3 .navbar-top.container {
  /* padding-left: 5px; */
  margin: auto;
  /* padding-top: 6px; */
}

.navbar-3 .fa-solid:before,
.navbar-3 .fas:before {
  font-size: 16px;
}

.navbar-3 .menu li.current-menu-item a[aria-current] {
  color: white;
}

.navbar-3 .menu li.current-menu-item :before,
.navbar-3 .menu-link-active i {
  color: white;
  color: var(--color-current-menu-item);
}

.navbar-masthead {
  height: 160px;
  background: var(--gradient-masthead);
}

.navbar-masthead-logo {
  text-align: center;
  margin: auto;
  /* padding-top: 80px;  */
  padding-bottom: 0px;
  /* text-align: center; */
  /* margin: auto; */
  padding-top: 65px;
  /* padding-bottom: 0px; */
}




/*
 *
 * Search
 *
 */

 #search {
  /* float: left; */
  /* margin: auto; */
  /* width: calc(100% - 15px);  */
  /* position: relative; */
  /* left: 10px; */
  width: 370px;
  background: var(--color-black) !important;
  border-color: var(--color-gray) !important;
  border-radius: var(--border-radius);
  border-width: 1px;
  line-height: 26px;
  height: 26px;
  margin-left: 8px;
  font-size: 13px;
  padding-left: 30px;
  color: var(--color-white) !important;
  background-color: var(--color-black) !important;
  font-weight: 700;
  margin: 0px 8px;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
}

/* #search:focus {
  border-color: gray !important;
}  */

.menu-search:focus i,
.menu-search:active i,
.menu-search:hover i {
  /* color: gray; */
  color: var(--color-denary);
}

.search-icon {
  position: absolute;
  top: 15px;
  left: 17px;
  z-index: 250;
  color: var(--color-search-icon);
  width: 15px;
}

/*
 *
 * Navbar
 *
 */

 .sidebar-active { padding-left: 190px; }

.navbar-left.navbar-left-logo {
  padding-left: 0px;
}

#navbar {
  z-index: 30;
  /* top: 10px; */
}

.navbar-top .search-icon {
  position: relative;
  /* top: 15px; */
  /* left: 17px; */
  z-index: 250;
  color: var(--color-search-icon);
  width: 15px;
}

.navbar-top .navbar-left-logo .navbar-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  display: flex;
}

/* .navbar-top .navbar-center {
  display: flex;
} */

.navbar-2 [class*="menu-"][class*="-container"] {
  display: none;
  position: absolute;
  top: 40px;
}

/* .navbar-home .navbar-top .menu {
  margin: 5px;
} */

/* .navbar-top .menu {
  background: var(--menu-sidebar-bg);
  margin: 0px;
  width: 100%;
  padding: 6px;
  text-align: left;
  min-width: 150px;
  border-bottom-left-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
} */

/* .navbar-top .menu li.menu-item {
  width: 100%;
  display: inline-flex;
  padding: 0;
  text-align: center;
}  */

.navbar-left-logo .logo img {
  height: 32px;
  margin: 4px 0;
}

.navbar-left-logo .logo img {
  margin: auto;
  vertical-align: middle;
  /* line-height: 10px; */
  /* justify-content: center; */
  height: 25px;
  display: inline-block;
  position: relative;
  /* bottom: -10px; */
  /* top: 7.5px;  */
  top: 5px;
  /* left: 0; */
  /* margin-left: 5px; */
  /* margin-right: 15px; */
  margin-left: 2.5px;
  margin-right: 10px;
}

#menu-main-right {
  margin: 0;
}

.menu-main-right-container {
  height: 100%;
}

.navbar-top {
  /* display: flex; */
}

.navbar-top .menu-title {
  margin-top: 0px;
}

.navbar-top .menu-title span {
  font-size: 12px;
  text-transform: uppercase;
  line-height: 50px;
  cursor: pointer;
  color: var(--color-black);
  color: var(--color-menu-list-text);
}

.navbar-top .menu-title span:hover {
  color: var(--color-body-text);
}

.navbar-left .logo h2 {
  text-align: left;
}

/*
 *
 * Baum Menu
 *
 */

.baum-page .menu-search {
  padding-top: 10px;
}

.menu-site-icon {
  width: 75px;
  margin: auto;
  position: absolute;
  bottom: 25px;
  left: 50px;
}

.menu,
.menu-list {
  list-style: none;
  margin-bottom: 0;
}

.menu-title { margin-top: 10px; }

#menu-following.menu .menu-item:before {
  margin-right: 0px;
  width: 10px;
  display: inline-block;
}

.menu-title span,
.menu li.menu-item,
.menu-list li.menu-item-title {
  /* font-size: 12px; */
  /* margin: -5px 15px; */
  margin-bottom: 0px;
  display: inline-block;
  font-weight: 600;
  color: var(--color-menu-list-text);
  color: var(--color-denary);
}

/* .fa, .fab, .fad, .fal, .far, .fas {
  line-height: 1.75;
} */

.menu { list-style-type: none; }

.menu li.menu-item,
.menu-list li {
  margin: auto;
  list-style-type: none;
}

.menu .menu-item a,
.menu-link {
  /* width: calc(100% - 25px); */
  /* cursor: default; */
  font-size: 14px;
  color: var(--color-menu-link);
  font-weight: 500;
  text-decoration: none;
  padding: 5px;
  line-height: 24px;
  /* display: inline-grid;  */
  margin: 0;
  /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; */
  display: inline-block;
}

/* .menu-item a:hover,
.menu-link:hover { color: var(--color-menu-link-active); } */

.menu-item a:hover,
.menu-link:hover {
  color: var(--color-senary);
}

/* .menu-item a i, .menu-link i { */
  /* color: var(--color-primary); */
  /* font-size: 16px; */
/* } */

/* .menu li.current-menu-item:hover a,
.menu li.current-menu-item a,
.menu-link-active a { background: var(--color-primary); } */

/* .menu li a:hover, */
/* .menu li:hover { background: var(--color-menu-li-bg); } */
/* .menu li:hover a {
  background: var(--color-nonary);
} */

.menu li.current-menu-item a,
.menu-link-active {
  color: var(--color-current-menu-item);
}

.current-menu-item a:hover,
.menu-link-active:hover {
  color: var(--color-current-menu-item);
}

/* .menu li.current-menu-item :before,
.menu-link-active i { color: var(--color-current-menu-item); } */

/* .menu :before {
  color: var(--color-primary);
} */

.menu li.current-menu-item a[aria-current="page"] i:before,
.menu li.current-menu-item:before {
  color: var(--color-current-menu-item-icon);
}
/* .menu { margin-left: 10px; } */

.menu .menu-item,
.menu-link {
  display: inline-block;
  width: 100%;
  margin: 0px;
  border-radius: var(--border-radius);
  /* border-radius: 4px; */
  padding: 0;
  text-decoration: none;
  line-height: 14px;
  display: flex;
}

/*
.menu li.current-menu-item:hover,
.menu li.current-menu-item {
  background: var(--color-primary);
} */

.menu li a {
  /* color: var(--color-current-menu-item); */
  /* background: var(--color-primary); */
  min-width: 15px;
  /* display: inline-block; */
  border-radius: var(--border-radius);
  /* display: inline-block; */
}

/* .menu li a:hover { */
  /* color: var(--color-current-menu-item); */
  /* background: var(--color-primary); */
  /* background: var(--color-quaternary); */
/* } */

.menu li.current-menu-item a[aria-current] {
  color: var(--color-current-menu-item-sidebar);
  /* color: white; */

  /* background: var(--color-primary); */
  /* min-width: 15px; */
  /* display: inline-block; */
  /* border-radius: var(--border-radius); */
  /* display: inline-block; */
  font-weight: 1000;
}


.menu-search {
  /* margin-bottom: 10px;  */
  margin-bottom: -25px;
  width: 100%;
  /* height: 40px; */
}

.menu,
.menu-list { margin-top: 10px; margin-bottom: 10px; }

#navbar .menu li.current-menu-item a[aria-current] {
  color: var(--color-current-menu-item);
  /* color: white;  */
}

.baum-header h2 {
  color: var(--color-primary);
  margin-bottom: 5px;
}

.baum-post-category a {
  color: var(--color-post-category);
  text-transform: uppercase;
  text-decoration: none;
  font-weight: 500;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
}

/*
 *
 * Author Box
 *
 */

.baum-author a {
  color: var(--color-secondary);
}

.baum-author-box .baum-author-box-wrap .baum-author-box-img .baum-user-initials {
  width: 80px; /* Match avatar size */
  height: 80px; /* Match avatar size */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ececec, #c6c6c6);
  font-size: 24px;
  color: #333;
}

article.baum-author-box .baum-author-box-wrap .baum-author-box-img {
  width: 64px;
  height: 64px;
}

.person .baum-author-box .baum-author-box-wrap .baum-author-box-img,
.contributor .baum-author-box .baum-author-box-wrap .baum-author-box-img,
.author .baum-author-box .baum-author-box-wrap .baum-author-box-img {
  width: 128px;
  height: 128px;
  outline: var(--card-outline);
  margin-left: 10px;
}

article.baum-author-box .baum-author-box-wrap .baum-author-box-name a {
  font-size: 24px;
  font-weight: 800;
  /* font-weight: 500; */
  line-height: 32px;
}

.person .baum-author-box .baum-author-box-wrap .baum-card-body,
.contributor .baum-author-box .baum-author-box-wrap .baum-card-body,
.author .baum-author-box .baum-author-box-wrap .baum-card-body {
  padding: 10px;
  display: grid;
  grid-template-columns: 128px 1fr;
  margin-bottom: 10px;
  max-height: none;
}

article.baum-author-box .baum-author-box-wrap .baum-card-body {
  padding: 10px;
  display: grid;
  grid-template-columns: 64px 1fr;
  margin-bottom: 10px;
  max-height: none;
}

.baum-author-social-info {
  display: block;
  float: right;
}

.baum-author-box-text p {
  /* font-size: 14px; */
  line-height: 1.75;
  font-weight: 400;
  /* margin-bottom: 0; */
  /* font-size: 14px !important; */
  /* margin: 10px;  */
  /* margin-bottom: 25px;  */
}

article .baum-author-box-wrap {
  height: auto;
}

article .baum-author-box-wrap {
  min-height: 84px;
}

.single article .single-footer .baum-author-box-wrap {
  margin-top: 15px;
}

/* .single-footer {
  margin: 15px -5px;
  margin-bottom: 0;
}  */

.single-footer {
  margin: 5px 0px;
}

.single-footer .baum-title-width {
  margin: 5px 0;
}

article .baum-author-box-wrap .baum-card-info ul { list-style: none; }

article .baum-author-box-wrap .baum-card-info li {
  margin: auto 10px;
  display: block;
  float: left;
}

article .baum-author-box-wrap .baum-card-info li a {
  color: var(--color-body-text);
}

article .baum-author-box-wrap .baum-author-box-name a {
  font-size: 16px;
  font-weight: 700;
  line-height: 56px;
}

article .baum-author-box-wrap .baum-author-box-img {
  width: 64px;
  height: 64px;
  overflow: hidden;
  border-radius: 50%;
  margin-left: 10px;
}

article .baum-author-box-text {
  line-height: 1.75;
  margin-top: 0px;
}

article .baum-author-box-wrap .baum-card-body {
  padding: 0px;
  display: grid;
  grid-template-columns: 64px 1fr;
  grid-column-gap: 10px;
}

/* article .baum-sticky-side .baum-author-box-wrap .baum-card-body {
  display: block;
} */

.baum-sticky-side {
  margin: 5px 0;
  padding-right: 5px;
  position: -webkit-sticky;
  position: sticky;
  top: 95px;
}

article .baum-sticky-side .baum-author-box-wrap .baum-author-box-img {
  margin: auto;
}

article .baum-sticky-side .baum-author-box-wrap .baum-card-category {
  display: none;
}

article .baum-sticky-side .baum-author-box-wrap .baum-author-box-img {
  width: 64px;
  height: 64px;
  margin: 10px;
}

article .baum-sticky-side .baum-author-box-wrap .baum-author-box-name a {
  font-size: 16px;
  line-height: 18px;
  line-height: 84px;
  display: inline-block;
}

article .baum-sticky-side .baum-author-box-text p { display: none; }
.baum-sticky-side .baum-author-box .baum-card-bottom { display: none; }
/* .baum-sticky-side .fa-link { display: none; }  */
article .baum-author-box-wrap .baum-card-headline { margin-left: 10px; }
.baum-author-box .baum-card-category { margin-top: 5px; }
.baum-person-box .baum-card-body { }
.baum-person-box .baum-profile-img img { height:auto; }

#main article.baum-person-box h3.baum-person-title a {
  font-size: 36px;
  line-height: 36px;
}

.baum-person-box .baum-card-mini img,
.baum-person-box .baum_most_viewed_widget .post-thumbnail img,
.baum-person-box .widget_post_views_counter_list_widget .post-thumbnail img {
  float: left;
  margin-right: 10px;
  margin-left: 0px;
  overflow: hidden;
  border-radius: var(--border-radius);
}

.baum-post-category-link {}

.baum-post-title {
  inline-size: 100%;
  overflow-wrap: break-word;
  /* overflow-x: hidden; */
  word-wrap: break-word;
  hyphens: auto;
  /* font-family: 'erbaum'; */
  /* text-transform: uppercase !important; */
  /* font-weight: bolder; */
  /* letter-spacing: -2px; */
  /* font-size: 24px; */
}

.page .baum-post-title {
  margin-top: 30px;
  margin-bottom: 20px;
}

.single-announcement .baum-post-title,
.single-document .baum-post-title {
  font-size: 24px;
  line-height: 32px;
}

.baum-post-date { text-align: right; }

.baum-post-byline-container .fa-link {
  font-size: 16px;
  /* position: relative; */
  /* top: -2px; */
}

.baum-post-byline-container {
  /* display: grid; */
  /* grid-template-columns: 275px 1fr; */
  margin-bottom: 10px;
}

.baum-post-byline-icons {
  float: left;
}

.baum-post-byline-wrap {
  width: 100%;
  margin: 0 10px !important;
  max-width: 733px;
}


.baum-post-byline-wrap .baum-channel span {
  line-height: 1;
  font-size: 14px;
  display: inline-block;
  padding-bottom: 10px;
}

.baum-post-byline-wrap span {
  color: var(--color-post-byline);
  color: var(--color-gray);
  font-size: 14px;
  font-weight: 400;
  /* text-transform: uppercase; */
  line-height: 24px;
  /* position: relative; */
  /* top: -4px; */
  vertical-align: middle;
}

.tax-channel .baum-post-byline-wrap .baum-channel::before {
  position: relative;
  top: 8px;
  background-size: 32px;
  height: 32px;
  width: 32px !important;
}

.tax-channel .baum-post-byline-container {
  margin-bottom: -5px;
  margin-top: 15px;
}

.tax-channel .baum-post-byline-wrap span {
  font-size: 18px;
  line-height: 32px;
  color: var(--color-black);
  font-weight: 1000;
}

.tax-author .button-follow-author,
.tax-category .button-follow-category,
.tax-channel .button-follow-channel {
  float: right;
  margin: 10px 0;
}

div.shared-counts-wrap {
  /* text-align: right;  */
  display: inline;
  margin: 0;
}

.baum-post-img-caption {
  color: var(--color-post-byline);
  font-size: 10px;
}

.baum-content-grid-half {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.baum-content-grid {
  display: grid;
  grid-template-columns: 66.66% 33.33%;
}

.baum-content-grid-left {
  display: grid;
  grid-template-columns: 33.33% 66.66%;
}

/* .page-template-page-knowledge .baum-sticky-side {
  margin-right: 50px;
  margin-top: 30px;
} */

/* .page-template-author-list .baum-sticky-side,
.page-template-page-company .baum-sticky-side {
  margin-left: 50px;
  margin-top: 30px;
} */

/* .page .baum-sticky-side,  */
/* .page .theiaStickySidebar { */
  /* transform: none; */
  /* background: var(--color-navbar-bg); */
  /* border-radius: var(--border-radius); */
/* } */

/* .page-template-page-knowledge .baum-sticky-side .menu,
.page-template-author-list .baum-sticky-side .menu,
.page-template-page-company .baum-sticky-side .menu {
  margin-left: 10px;
  padding-bottom: 20px;
} */

/* .page-template-page-knowledge .menu-title span,
.page-template-author-list .menu-title span,
.page-template-page-company .menu-title span {
  font-size: 12px;
  margin: -5px 15px;
  margin-bottom: 0px;
  display: block;
  font-weight: 600;
  color: var(--color-menu-list-text);
} */

.page-template-page-knowledge .baum-widget-side-title,
.page-template-author-list .baum-widget-side-title,
.page-template-page-company .baum-widget-side-title {
  /* padding: 0;
  margin: 0;
  padding-top: 10px;
  margin-left: 10px;
  font-size: 12px;
  display: block;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--color-menu-list-text); */
}

.baum-sticky-side .menu .menu-item a,
.baum-sticky-side .menu-link {
  font-weight: 700;
  color: var(--color-tertiary);
}

.baum-sticky-side .menu .menu-item a:visited,
.baum-sticky-side .menu-link :visited{
  /* color: var(--color-primary); */
}

.baum-content-full {
  padding-left: 5px;
  padding-right: 5px;
}

.post .baum-content-full .entry-content {
  display: grid;
  margin: auto;
  /* max-width: 850px; */
  /* min-height: 75vh; */
  max-width: 800px;
  margin: auto;
  margin-bottom: 30px;
}

.baum-content-grid-full {
  display: grid;
  grid-template-columns: 100px 1fr 100px;
}

.baum-content-left-column {
  padding-right: 15px;
  padding-bottom: 5px;
  padding-bottom: 15px;
}

.baum-grid-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: max-content;
}

.baum-grid-3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-rows: max-content;
}

.baum-grid-4 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-rows: max-content;
}

.baum-grid-5 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-auto-rows: max-content;
}

.baum-content-right-column {
  /* padding-left: 5px;  */
  /* position: relative; */
  /* left: -5px; */
  /* padding-bottom: 15px; */
  margin-bottom: 5px;
}

.baum-content-right-column .baum-card-container {
  margin: 0px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.baum-post-excerpt { margin: 15px 0; }

.baum-post-content {
  margin: 0;
  margin-top: 0;
  /* margin-bottom: 15px;  */
}

h5.baum-widget-side-title { font-weight: 900; }

/* .main-widget-title {
  background: var(--color-quaternary);
  border-radius: 6px;
  padding: 2px 7px;
}  */

.baum-post {
  margin: auto;
  margin-top: 0px;
  padding-left: 5px;
  /* margin-bottom: 3px; */
}

/* .baum-post-content :last-child { margin-bottom: 0; } */

.post .baum-post-category a {
  background: var(--color-primary);
  padding: 0px 10px;
  display: inline-block;
  border-radius: var(--border-radius);
  color: var(--color-black);
  text-transform: uppercase;
  color: var(--color-black);
  color: white;
  font-weight: bold;
}

.baum-widget-home ul {
  margin: 0px 5px;
}

.page-template-default .baum-widget-side-title {
  padding: 0;
  margin: 0;
  padding-top: 10px;
  margin-left: 10px;
  font-size: 12px;
  display: block;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--color-menu-list-text);
}

.btn-follow-channel { font-size: 14px; }

/*
 *
 * Wordpress
 *
 */

.has-drop-cap:not(:focus):first-letter {
  font-size: 64px;
  /* color: var(--color-primary); */
  font-family: 'Arial Black';
}

/* .wp-embed-featured-image.rectangular img {
  border-radius: var(--border-radius);
} */

.comments form {
  margin: 10px 0;
}

/*
 *
 * BaumPress
 *
 */

.baum-post-img {
  margin-bottom: 15px;
}

.baum-post-img.half-left {
  width: 50%;
  float: left;
  margin-right: 20px;
  margin-top: 20px;
}

.baum-post-img.half-right {
  width: 50%;
  float: right;
  margin-left: 20px;
  margin-top: 20px;
}

.baum-post-img img {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}

.baum-post-title {
  /* text-transform: uppercase;  */
  text-transform: capitalize;
  text-align: left;
  font-weight: 900;
  margin-bottom: 0;
  margin-top: 0;
}

.baum-post-title {
  display: inline-block;
  /* margin-top: 50px; */
}

/*
 *
 * Baum Logo
 *
 */

.logo h2 {
  color: var(--color-site-title);
  /* color: var(--color-primary);  */
}
.logo h2 {
  letter-spacing: -2px;
  font-size: 28px;
  text-align: center;
  /* font-family: 'erbaum' !important; */
  font-weight: 1000 !important;
  text-transform: uppercase;
}

.logo a { text-decoration: none; }
.logo h2 {
  margin: 0;
  padding-bottom: 0;
  /* color: var(--color-primary); */
  line-height: 40px;
}

.logo img {
  background: none;
  width: auto;
  max-width: 540px;
}

footer .logo h2 {
  font-size: 21px;
  color: var(--color-white);
  text-align: left;
  letter-spacing: -1px;
}


.logo-sidebar {
  text-align: center;
  font-size: 50px;
}

/* .logo {
  margin: -2px 5px;
} */

.logo .date {
  margin: 0;
  margin-top: 0;
  padding-top: 0;
  /* font-weight: 700; */
  font-size: 16px;


  /* color: var(--color-gray); */
  /* opacity: 0.5; */
  text-transform: uppercase;
  /* font-weight: 500; */
  font-size: 11px;
  font-weight: 700 !important;
  position: relative;
  /* top: -5px;  */
}

footer .baum-home-date-headline h4 {
  /* color: var(--color-white); */
  /* font-weight: 900;  */
}

.navbar-channel {
  height: 35px;
  width: auto;
  margin: auto;
}

.baum-header { font-weight: 900; }

.baum-spacer {
  height: 20px;
  width: 100%;
}

#search {
  z-index: 999;
}

/* #baum-search-form { display: none; } */

/* #baum-search-form::before,
.overlay-blur {
  content: '';
  z-index: -1;
  background: black;
  opacity: 0.7;
  filter: blur(50px);
  position: absolute;
  width: 100%;
  height: 100vh;
  top: 0;
  right: 0;
  display: none;
} */


.channels .baum-post-byline-wrap .baum-channel span,
.categories .baum-post-byline-wrap .baum-category span {
  color: var(--color-post-byline);
  color: var(--color-post-category);
  color: var(--color-secondary);
  font-size: 16px;
  font-weight: 900;
}

.baum-post-byline-wrap .baum-channel::before {
  position: relative;
  top: 0px;
  background-size: 18px;
  height: 18px;
}

.baum-author-box-wrap .baum-author-box-name a {
  color: var(--color-secondary);
}

.baum-author-box-wrap a {
  text-decoration: none;
  /* color: var(--color-card-headline); */
  color: var(--color-senary);
  text-transform: capitalize;
  /* font-weight: 700;  */
  font-weight: 500;
}

.baum-author-box-head {
  margin-left: 10px;
}

.baum-author-box-head .button a {
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 700;
}

.baum-sticky-side .baum-author-box-head .button {
  display: none;
}












/* .baum-story-collection .baum-card-container-text { */
  /* max-width: 50%;  */
/* } */
.baum-story-collection-wrapper:has(.baum-card-container-text) {
  border-bottom: 5px solid var(--color-primary);
  margin-bottom: 25px;
  margin-top: 10px;
}

.baum-card-container-text .baum-title:not(.baum-shield) {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.75;
  color: var(--color-body-text);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  text-transform: capitalize;
  letter-spacing: inherit;
  /* text-transform: uppercase; */
}

.baum-card-container-text a.baum-shield {
  color: var(--color-white);
}

.baum-story-collection-wrapper:has(.baum-card-container-text) .baum-title-width {
  margin: 5px 0;
}

































body .container a.btn-follow-author,
body .container .authors a.btn-follow-author,
body .container a.btn-follow-category,
body .container .categories a.btn-follow-category,
body .container a.btn-follow-channel,
body .container .channels a.btn-follow-channel {
  /* color: var(--color-body-text); */
  /* color: gray; */
  /* color: var(--color-body-text); */
  color: var(--color-white);
  font-weight: 1000;
}

.archive a.btn-follow-channel,
.single a.btn-follow-channel {
  /* position: relative;  */
  /* top: -5px;  */
  /* line-height: 22px; */
  /* margin: 0 0px; */
}







.baum-story-collection .baum-card-container:has(.baum-card-container-text) {
  height: 32px;
}

.baum-story-collection .baum-card-container {
  min-height: 32px;
}

.baum_collection_widget a.post-title.h5 {
  font-weight: 1000;
}

.baum_collection_widget .baum-story-collection-wrapper {
  /* margin: 5px;  */
  width: 100%;
  margin: auto;
  margin-bottom: 5px;
}

.baum-card-story-collection.baum-card-container-text {
  margin: auto 5px;
}


.baum-card-story-collection .baum-card-mini .baum-card-body {
  display: inline-grid;
  grid-template-columns: 1fr 125px;
}

.baum-card-story-collection .baum-card-mini .baum-card-headline {
  padding-right: 10px !important;
}





footer {
  /* background: var(--color-footer-bg);
  border-top: var(--footer-border);
  background: var(--menu-sidebar-bg);
  border-top: var(--menu-sidebar-border); */
  width: 100%;
  margin-top: 25px;
  padding-bottom: 50px;
  margin: auto;
  /* margin-top: 50px; */
}

footer {
  /* background: var(--color-footer-bg); */
  /* border: var(--footer-border); */
  /* background: var(--color-navbar-bg); */
  background: var(--color-black);
  /* border: var(--menu-sidebar-border); */
  /* max-width: 1050px; */
  margin-top: 5px;
  padding: 0px;
  /* margin: 10px auto; */
  /* border-radius: var(--border-radius); */
  /* margin-bottom: 25px;  */
  /* border-bottom-left-radius: 0; */
  /* border-bottom-right-radius: 0; */
}

footer .container {
  /* display: grid;
  grid-template-columns: auto auto; */
  margin: auto;
  padding: 5px;
  /* margin-bottom: 15px; */
  /* padding: 0px 5px;  */
  /* margin-top: 5px; */
  padding: 0 5px;
  padding-bottom: 5px;
}

footer h5 {
  color: var(--color-current-menu-item);
  padding: 0px;
  font-size: 24px;
  font-weight: 900;
  margin: 0;
  /* clear: both; */
  margin-top: 0px;
}

footer p {
  width: 100%;
  cursor: default;
  font-size: 14px;
  /* color: var(--color-menu-link); */
  color: var(--color-current-menu-item);
  font-weight: 400;
  text-decoration: none;
  padding: 0;
  line-height: 18px;
  margin-bottom: 0;
}

.baum-foot-menu > div {
  max-width: 340px;
  margin-bottom: 20px;
}

footer .baum-foot-copy { margin-bottom: 0px; }

footer .baum-foot-copy {
  margin-bottom: 20px;
}

footer .baum-foot-copy p {
  font-size: 10px;
  opacity: 0.5;
}

footer .baum-foot-copy p.baum-foot-text {
  font-size: 12px;
  opacity: 1;
}

/* .baum-foot-copy {
  position: absolute;
  right: 0;
  bottom: 25px;
} */

footer .baum-foot-copy p {
  /* color: gray; */
  color: var(--color-white);
  margin-left: 0px;
  padding: 0px;
  line-height: 18px;
  /* text-align: right;  */
  margin-bottom: 10px;
  display: inline;
}

footer .menu li:hover {
  /* background: var(--color-quaternary); */
  font-weight: 1000;
}

footer div.footer-column { padding: 0 0; }

footer .menu li a:hover {
  background: none;
  opacity: 0.5;
  /* font-weight: 1000; */
}

footer .menu li.current-menu-item a[aria-current] {
  color: var(--color-current-menu-item);
  color: white;
}

footer .baum-foot-menu ul .sub-menu {
  padding-top: 0px;
  padding-bottom: 0px;
  margin-top: 0px;
  margin-bottom: 0px;
}

footer .logo .date {
  margin-top: 10px;
  /* font-size: 11px; */
  line-height: 1.75;
}


.footer-site-icon {
  /* width: 45px; */
  float: left;
  margin: 15px;
}

.baum-footer-logo {
  margin: 10px 0px;
}

.baum-footer-logo .logo {
  margin: 0;
}

.baum-footer-logo img {
  height: 24px;
  /* width: auto;  */
}

.baum-foot-menu ul {
  /* display: flex; */
  clear: both;
  padding-top: 10px;
}

.baum-foot-menu .menu, .menu-list {
  margin-top: 0;
}

.baum-foot-menu .baum-heading,
.baum-foot-menu .menu-title {
  /* float: left; */
  font-weight: 900;
  background: var(--color-quaternary);
  background: none;
}

.baum-foot-menu .baum-heading,
.baum-foot-menu .menu-title span {
  font-weight: 1000;
  /* color: var(--color-senary);  */
  color: var(--color-primary);
}

/* .baum-foot-menu .menu-title span {
  margin: -15px 15px;
} */

.entry-content p a {
  text-decoration-line: underline;
  font-weight: 600;
}

.footer-social-list {
  list-style-type: none;
  float: right;
  margin-right: 25px;
}

.footer-social-list li {
  float: left;
  margin: 10px;
}

/* .baum-card-container-mini { */
  /* margin-top: 5px; */
  /* margin-bottom: 5px; */
/* } */

/* .baum-side-widget:has(div) {
  margin-bottom: 20px;
} */

.baum-side-widget:has(div) {
  margin-bottom: 10px;
}

.baum-side-widget ul {
  /* margin-bottom: 0px;  */
  /* margin-left: 20px; */
  color: gray;
  margin-bottom: 5px;
}

.baum-side-widget ul.sub-menu {
  margin-bottom: 0px;
  margin-top: 0px;
}


/*
 *
 * Post Views Counter
 *
 */

.baum_most_viewed_widget li a,
.widget_post_views_counter_list_widget li a {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word !important;
  -webkit-line-clamp: 3;
}

.baum_most_viewed_widget .post-excerpt,
.widget_post_views_counter_list_widget .post-excerpt {
  font-size: 14px;
  line-height: 1.5;
  /* color: var(--card-info);  */
  color: var(--color-quinary);
}

.post-title:has(+.post-excerpt) {
  font-size: 14px;
  font-weight: 900;
  padding-bottom: 0px;
  display: inline-block;
  /* margin-bottom: 5px;  */
  /* line-height: 1 !important;  */
  /* margin: 4px 0; */
  line-height: 1 !important;
}

.baum-card-mini .post-title:has(+.post-excerpt) {
  font-size: 14px;
  line-height: 1.45 !important;
}

.baum_most_viewed_widget,
.widget_post_views_counter_list_widget { margin: 0 0px; }

.baum_most_viewed_widget ol li,
.widget_post_views_counter_list_widget ol li {
  /* border-radius: var(--border-radius); */
  padding: 10px;
  /* filter: var(--drop-shadow); */
  /* border: var(--card-border); */
  /* background: var(--card-bg); */
  min-height: 75px;
  line-height: 1.25;
  margin-bottom: 0;
}

.baum_most_viewed_widget ol,
.widget_post_views_counter_list_widget ol {
  margin-inline: auto;
  list-style: none;
  counter-reset: cardnr;
  justify-content: center;
  /* border-radius: var(--border-radius); */
  /* filter: var(--drop-shadow); */
  /* border: var(--card-border); */
  /* background: var(--card-bg); */
}

.baum-side-widget.baum_most_viewed_widget ol,
.baum-side-widget.baum_most_viewed_widget ul {
  margin: 0 5px;
}

.baum-side-widget.baum_most_viewed_widget ol,
.baum-side-widget.baum_most_viewed_widget ul {
  margin-bottom: 10px;
}

.baum_most_viewed_widget ol li,
.widget_post_views_counter_list_widget ol li {
  counter-increment: cardnr;
  /* width: calc(var(--width) - var(--inlineP) * 2); */
  /* display: grid;
  margin-inline: var(--inlineP);
  margin-bottom: calc(var(--borderR));
  position: relative; */
}

.baum_most_viewed_widget ol li::before,
.widget_post_views_counter_list_widget ol li::before {
  content: counter(cardnr);
  color: white;
  height: 50px;
  width: 50px;
  /* background: linear-gradient(to top right, black, rgb(67, 67, 67)); */
  /* background: linear-gradient(to top right, var(--color-primary), rgb(67, 67, 67));   */
  background: var(--color-primary);
  /* background: linear-gradient(var(--color-quaternary), var(--color-secondary));  */
  background: linear-gradient(var(--color-tertiary), var(--color-secondary));
  float: left;
  padding: 15px;
  font-weight: 900;
  font-size: 18px;
  /* font-family: 'erbaum'; */
  text-align: center;
  border-radius: 50%;
  margin-right: 15px;
}

.baum_most_viewed_widget ul,
.widget_post_views_counter_list_widget ul {
  /* border-radius: var(--border-radius); */
  /* filter: var(--drop-shadow); */
  /* border: var(--card-border); */
  /* background: var(--card-bg); */
  padding: 0px 0;
  padding-bottom: 0px;
  margin-bottom: 5px;
  margin-top: 0px;
}

.baum_most_viewed_widget .post-excerpt,
.widget_post_views_counter_list_widget .post-excerpt {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word !important;
  -webkit-line-clamp: 2;
}


/* .baum-card-mini .post-excerpt {
  display: inline-block;
} */

.baum_most_viewed_widget ul li,
.widget_post_views_counter_list_widget ul li {
  list-style-type: none;
  border-radius: var(--border-radius);
  padding: 10px;
  /* filter: var(--drop-shadow); */
  border: var(--card-border);
  outline: var(--card-outline);
  /* background: var(--card-bg); */
  min-height: 84px;
  /* line-height: 1; */
  /* margin-bottom: 0; */
  background: var(--card-bg);
  filter: var(--drop-shadow);
  line-height: 1.5;
  /* margin: 10px 0; */
  margin: 5px 0;
}

.baum_most_viewed_widget li a,
.widget_post_views_counter_list_widget li a {
  text-decoration: none;
  font-size: 14px;
  word-wrap: break-word;
  color: var(--color-card-headline);
  text-transform: capitalize;
  font-weight: 700;
  /* display: inline; */
  /* height: 34px; */
  /* overflow: hidden; */
  /* margin-bottom: 10px; */
}

.baum_most_viewed_widget .post-thumbnail img,
.widget_post_views_counter_list_widget .post-thumbnail img {
  float: right;
  margin-left: 10px;
  overflow: hidden;
  border-radius: var(--border-radius);
}

.baum-widget-home.baum_most_viewed_widget ol,
.baum-widget-home.baum_most_viewed_widget ul,
.baum-widget-home.widget_post_views_counter_list_widget ol,
.baum-widget-home.widget_post_views_counter_list_widget ul {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column-gap: 10px;
  padding: 0;
  margin: 0 5px;
  margin-top: 0;
}

.baum-widget-home.baum_most_viewed_widget ol li,
.baum-widget-home.widget_post_views_counter_list_widget ol li,
.baum-widget-home.baum_most_viewed_widget ul li,
.baum-widget-home.widget_post_views_counter_list_widget ul li {
  margin: 5px 0;
}

.baum-widget-after-story.baum_most_viewed_widget ol,
.baum-widget-after-story.baum_most_viewed_widget ul,
.baum-widget-after-story.widget_post_views_counter_list_widget ol,
.baum-widget-after-story.widget_post_views_counter_list_widget ul {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 10px;
  padding: 0;
  margin: 0px 0px;
  margin-top: 0;
}

.baum-widget-after-story.baum_most_viewed_widget ol,
.baum-widget-after-story.baum_most_viewed_widget ul {
  margin: 0;
}

#leaky-paywall-profile {
  display: grid;
  grid-template-columns: 1fr 1fr;
}


footer .menu-title span {
  margin: 0;
}

.comment-body .baum-heading a {
  font-size: 15px;
}

.menu-title,
#reply-title,
.baum-widget-side-title,
.button.baum-heading,
.baum-heading {
  /* margin-left: 5px; */
  height: 42px;
  line-height: 25px;
  /* font-weight: 900; */
  font-size: 12px;
  text-transform: uppercase;
  /* background: rgb(199, 199, 199); */
  padding: 7.5px 12.5px;
  /* border-radius: var(--border-radius); */
  /* display: flex; */
  display: block;
  /* color: rgb(57, 57, 57); */
  color: var(--color-quaternary);
  /* background: var(--color-navbar-bg); */
  /* background: var(--color-title-bg); */
  background: var(--card-bg);
  /* background: linear-gradient(to bottom, var(--card-bg), var(--card-bg), var(--color-denary)); */
  /* background: linear-gradient(to bottom, #f6f6f6, #f0f0f0); */
  /* background: linear-gradient(to bottom, #ffffff, #e9e9e9); */
  /* display: inline-flex; */
  background: linear-gradient(to bottom, #ffffff, #efefef);
  border-radius: var(--border-radius);
  color: var(--color-tertiary);
  border: var(--card-outline);
  /* border-bottom: 0; */
  /* outline: var(--card-outline); */
  /* border-top: 2.5px solid rgba(255,255,255,0.25); */
  /* font-weight: 1000; */
  font-weight: 800;
  /* letter-spacing: 0.5px; */
  /* letter-spacing: -0.1px; */
}


.baum-heading {
  display: flex;
}



.baum-heading a {
  font-weight: 800;
}

#reply-title {
  background: var(--color-secondary);
  color: var(--color-white);
}

































.wp-element-button,
#baum-main .tnp-widget input.tnp-submit,
#buddypress li.load-more a,
#buddypress li.load-newest a,
button#leaky-paywall-submit,
#leaky-paywall-registration-next,
.button,
button,
.input,
input[type="submit"],
input[type="reset"],
input[type="button"] {
  color: var(--color-button-text-primary);
  /* color: var(--color-button-text);  */
  border: var(--color-button-border);
  background: var(--color-button-bg);
  line-height: 13px;
  display: inline-block;
  padding: 10px 15px;
  font-size: 13px;
  font-weight: 800;
  text-transform: uppercase;
  text-align: center;
  letter-spacing: normal;
  border-radius: var(--border-radius);
  padding: 0 20px;
  height: 22px;
  /* position: relative; */
  /* top: -3px; */
  margin: 0 10px;












  /* color: var(--color-button-text);  */
  border: var(--color-button-border);
  background: var(--color-button-bg);
  line-height: 12px;
  display: inline-block;
  /* padding: 10px 15px; */
  font-size: 12px;
  /* font-weight: 1000; */
  text-transform: uppercase;
  text-align: center;
  letter-spacing: normal;
  border-radius: var(--border-radius);
  padding: 15px 30px;
  height: 42px;
  /* position: relative; */
  /* top: -3px; */
  margin: 0 0px;
  width: 100%;
  width: auto;
}

.shared-counts-wrap .shared-counts-label {
  display: none;
}

/* .single-footer .baum-post-byline-container .fa-link:before { } */

article .single-footer .ccc-favorite-post-toggle {
  display: inline-block;
  margin-right: -5px;
  line-height: 40px;
}

.single-footer .baum-post-byline-container .fa-link,
.single-footer .ccc-favorite-post-toggle a,
.style-icons .shared-counts-icon {
  margin-right: 10px;
  background: var(--color-primary);
  width: 40px;
  height: 40px;
  line-height: 40px;
  display: inline-block;
  border-radius: 50%;
  color: var(--color-white);
  color: white !important;
}

.single-footer .baum-post-byline-container .fa-link:before {
  color: white !important;
}

.fa-circle:before {
  margin-right: 2px !important;
}

img.channel-img {
  display: inline-block;
  background: none;
  position: relative;
  top: 4px;
}

.authors,
.categories,
.channels {
  list-style-type: none;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
}

.authors .button-follow-author,
.categories .button-follow-category,
.channels .button-follow-channel {
  float: right;
}

.authors li,
.categories li,
.channels li {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 15px 15px 0 10px;
  margin: 0;
}

.clear, .clearfix { clear: both; }
.left { float: left; }
.right { float: right; }
.center {
  margin: auto;
  justify-content: center;
  vertical-align: middle;
  text-align: center;
}
.hidden, .hide { display: none !important; }
.align-left, .alignleft { float: left; }
.align-right, .alignright { float: right; }
.text-left, .text-align-left, .textalignleft { text-align: left; }
.text-right, .text-align-right, .textalignright { text-align: right; }
.align-center, .aligncenter { text-align: center; }
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }

/* .baum-post-title { font-size: 42px; line-height: 48px; }  */
.baum-post-title {
  font-size: 36px;
  line-height: 44px;
  word-wrap: normal;
  hyphens: none;
}

figure, .figure { margin: 0; margin-bottom: 0; /* margin-bottom: 2rem; */ }

a, .a { text-decoration-line: none; }
p, .p, li, .li { font-size: 16px; }

h1, .h1 { font-size: 35px; line-height: 56px; font-weight: 1000; }
h2, .h2 { font-size: 28px; line-height: 49px; }
h3, .h3 { font-size: 24.5px; line-height: 35px; }
h4, .h4 { font-size: 21px; line-height: 28px; font-weight: 900 !important; }
h5, .h5 { font-size: 16px; line-height: 24.5px; font-weight: 900 !important; }
h6, .h6 { font-size: 14px; line-height: 22px;
  text-transform: uppercase;
  background: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--border-radius-small);
  display: inline-block;
  padding: 2px 10px;
  font-weight: 800 !important;
}

code, .code { border-radius: var(--border-radius); }
code, kbd, pre, samp { font-size: 13px; }

.bold, .b, .strong { font-weight: 800; }
.bolder, .stronger { font-weight: 1000; }
.anchor {
  position: relative;
  top: -50px;
  visibility: hidden;
}

.baum-post-excerpt p,
.baum-post-excerpt span,
.baum-post-excerpt {
  margin: 18px 0;
  font-size: 21px;
  font-weight: 600;
  /* font-style: italic;  */
  /* text-align: center;  */
}

.baum-video-embed iframe {
  aspect-ratio: 16 / 9;
  width: 100%;
  border-radius: var(--border-radius);
}

/*
 *
 * Elipsis After X Number Of Lines
 *
 */

[data-title-max-lines] {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word !important;
  -webkit-line-clamp: 6;
}

[data-title-max-lines="1"] { -webkit-line-clamp: 1; }
[data-title-max-lines="2"] { -webkit-line-clamp: 2; }
[data-title-max-lines="3"] { -webkit-line-clamp: 3; }
[data-title-max-lines="4"] { -webkit-line-clamp: 4; }
[data-title-max-lines="5"] { -webkit-line-clamp: 5; }
[data-title-max-lines="6"] { -webkit-line-clamp: 6; }

.single-footer .baum-author-box-text p {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word !important;
  -webkit-line-clamp: 2;
}




/*
 *
 * Baum Follow Channel Widget
 *
 */

.baum_channel_widget {
  margin-bottom: 10px;
}

a.btn-follow-author,
a.btn-follow-category,
a.btn-follow-channel {
  font-size: 11px;
  line-height: 24px;
  /* color: black; */
}

.baum_author_widget .button,
.button-follow-author,
.baum_category_widget .button,
.button-follow-category,
/* a.btn-follow-channel,  */
.baum_channel_widget .button,
.button-follow-channel {
  font-size: 11px;
  padding: 5px 15px;
  float: right;
  width: auto;
  height: 36px;
  background: var(--color-octonary);
}

.baum_author_widget .button,
.button-follow-author,
.baum_category_widget .button,
.button-follow-category,
.baum_channel_widget .button,
.button-follow-channel {
  font-size: 11px;
  padding: 0px 15px;
  line-height: 24px;
  float: right;
  width: auto;
  height: 24px;
  /* color: var(--color-white) !important; */
  background: var(--color-tertiary);
  /* background: var(--color-primary); */
  margin: 15px;
  display: block;
  text-align: right;
}

.baum_channel_widget .baum-channel span {
  font-size: 14px;
  font-weight: 800;
  color: var(--color-black);
  /* line-height: 44px; */
  margin-left: 10px;
  display: inline-block;
  /* vertical-align: middle; */
  line-height: 24px;
}

.baum_channel_widget .baum-channel:before {
  background-size: 20px;
  left: 5px;
  width: 20px !important;
  height: 20px;
  top: 5px;
}

.baum_author_widget {
  margin: 5px 0;
}

.baum-story-collection {
  display: grid;
}

.baum-story-collection {
  margin-bottom: 5px;
}

.baum-side-widget .baum-story-collection {
  margin: 5px;
}


.single-footer .baum-story-collection,
.single-footer .baum_collection_widget .baum-story-collection {
  /* margin: 5px;  */
  grid-template-columns: 1fr 1fr;
  /* grid-column-gap: 10px; */
  margin: -5px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.baum-widget-home .baum-story-collection,
.baum-widget-home .baum_collection_widget .baum-story-collection {
  margin: 0px;
  margin-top: 0px;
  grid-template-columns: 1fr 1fr 1fr;
  /* grid-column-gap: 0px; */
}

/*
.baum-widget-home .baum-card-container-mini {
  margin-top: 0px;
  margin-bottom: 0px;
} */

.html, html {
  margin-top: 0 !important;
}

#wpadminbar {
  display: none;
}

/* div.baum-side-widget div:nth-child(2),
div.baum-side-widget div:nth-child(3),
div.baum-side-widget div:nth-child(4) {
  display: none;
} */

/* div.baum-body-width div:nth-child(2)
div.baum-body-width div:nth-child(3),
div.baum-body-width div:nth-child(4) {
  display: none;
} */

/* .baum-post-content { */
  /* margin: 0 10px; */
  /* padding: 0 15px; */
/* } */

.navbar-3 .navbar-top {
  /* grid-template-columns: auto 256px auto; */
  /* grid-template-columns: 148px 1fr 148px;  */
}

/* .navbar-icons {
  text-align: right;
}

.navbar-icons ul.menu {
  display: inline-flex;
}

.navbar-icons ul.menu li a {
  margin: 0 10px;
  text-align: center;
}

.button-back {
  height: 30px;
  padding: 0px;
  line-height: 30px;
  font-weight: 1000;
  width: auto;
  min-width: 30px;
}

#navbar .logo,
#navbar .logo a {
  height: 100%;
}

#navbar .logo img {
  background: none;
  width: auto;
  max-width: 500px;
  max-height: 100%;
  margin: auto;
  vertical-align: middle;
  padding: 5px;
}  */






/* .navbar-icons {
  text-align: right;
}

.navbar-icons ul.menu {
  display: inline-flex;
}

.navbar-icons ul.menu li a {
  margin: 0 10px;
  text-align: center;
}

.button-back {
  height: 30px;
  padding: 0px;
  line-height: 30px;
  font-weight: 1000;
  width: auto;
  min-width: 30px;
}

#navbar .logo,
#navbar .logo a {
  height: 100%;
}

#navbar .logo img {
  background: none;
  width: auto;
  max-width: 500px;
  max-height: 100%;
  margin: auto;
  vertical-align: middle;
  padding: 5px;
}  */




code, .code {
  border-radius: var(--border-radius-small);
}

code, .code {
  padding: 2px;
  margin: 0 2px;
  font-size: 100%;
  white-space: nowrap;
  background: var(--color-nonary);
  border: 1px solid var(--color-senary);
}

