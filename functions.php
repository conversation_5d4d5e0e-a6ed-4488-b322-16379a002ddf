<?php
/**
 * <PERSON>umPress Theme Functions
 *
 * This file contains the main theme functionality including theme setup,
 * custom post types, taxonomies, enqueue scripts, and various theme features.
 * It serves as the central hub for all theme-related functionality.
 *
 * @package BaumPress
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
  exit;
}

/**
 * Theme Configuration Constants and Color Schemes
 *
 * Action Network Color Scheme:
 * Primary: #fd570d
 * Secondary: #cc70eb
 * Tertiary: #1781fa
 * Dark: #140817
 * Background: #211d22
 *
 * USA Patriot Color Scheme:
 * Primary: #bc0c0c
 * Secondary: #042b5b
 * Tertiary: #00397f
 * Quaternary: #2d2d2d
 */

// add_action('init', function () {
//   error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_USER_NOTICE & ~E_USER_DEPRECATED);
//   ini_set('log_errors', '1');
//   ini_set('display_errors', '0');
//   ini_set('error_log', ABSPATH . 'wp-content/debug.log');
// });

// add_filter('wp_debug_log', function ($log_entry) {
//   // Suppress notices and deprecated warnings from being logged
//   if (strpos($log_entry, 'Notice:') !== false || strpos($log_entry, 'Deprecated:') !== false) {
//       return false; // Skip logging this entry
//   }
//   return $log_entry; // Log all other entries
// });

global $YOUR_ASSEMBLYAI_API_KEY;
$YOUR_ASSEMBLYAI_API_KEY = '********************************';
// https://www.assemblyai.com/pricing
//

//
// Summary of the magic:
// Email: From site’s admin email (Settings > General) or builds a
// <NAME_EMAIL>.
// Name: From site title.
// Fallback safety net: <EMAIL> and 'WordPress' if all else fails.
//

/**
 * Sets the "From" email address for all WordPress emails
 *
 * This filter ensures that all emails sent by WordPress use the site's admin email
 * as the sender, or falls back to a noreply@ address using the site's domain if
 * the admin email is not valid.
 *
 * @param string $email The default email address
 * @return string The modified email address
 *
 * @since 1.0.0
 */
add_filter('wp_mail_from', function($email) {
  $admin_email = get_option('admin_email');
  return is_email($admin_email) ? $admin_email : 'noreply@' . parse_url(home_url(), PHP_URL_HOST);
});

/**
 * Sets the "From" name for all WordPress emails
 *
 * This filter ensures that all emails sent by WordPress use the site's name
 * as the sender name, or falls back to 'WordPress' if the site name is not set.
 *
 * @param string $name The default sender name
 * @return string The modified sender name
 *
 * @since 1.0.0
 */
add_filter('wp_mail_from_name', function ($name) {
  $site_name = get_option('blogname');
  return $site_name ?: 'WordPress';
});

/**
 * Fixes PHPMailer configuration for WordPress emails
 *
 * This action ensures that the PHPMailer object is properly configured with
 * valid "From" email and name values. It specifically fixes the common issue
 * of emails being sent from "wordpress@localhost" by replacing it with the
 * site's admin email or a noreply@ address.
 *
 * @param PHPMailer $phpmailer The PHPMailer instance
 * @return void
 *
 * @since 1.0.0
 */
add_action('phpmailer_init', function ($phpmailer) {
  $admin_email = get_option('admin_email');
  $site_name = get_option('blogname');
  $fallback_email = 'noreply@' . parse_url(home_url(), PHP_URL_HOST);

  if (empty($phpmailer->From) || $phpmailer->From === 'wordpress@localhost') {
      error_log('[Bit SMTP Fix] Forcing fallback From address via Site Settings.');
      $phpmailer->From = is_email($admin_email) ? $admin_email : $fallback_email;
      $phpmailer->FromName = $site_name ?: 'WordPress';
  }
});






add_filter('wp_img_tag_add_auto_sizes', '__return_false');

//
// Custom Logo Settings
//

/**
 * Adds logo customization options to the WordPress Customizer
 *
 * This function registers a new section in the WordPress Customizer
 * for logo settings, allowing users to adjust the size of logos
 * in different positions (center, default, home, footer).
 *
 * @param WP_Customize_Manager $wp_customize The WordPress customizer object
 * @return void
 *
 * @since 1.0.0
 */
function baum_customize_logo ($wp_customize) {

  //
  // Add Section for Logo Settings
  //

  $wp_customize->add_section('baum_logo_section', [
    'title'    => __('Logo Settings', 'baum'),
    'priority' => 30,
  ]);

  //
  // Add Setting for Center Logo Size
  //

  $wp_customize->add_setting('baum_center_logo_size', [
    'default'           => 40,
    'sanitize_callback' => 'absint',
    'transport'         => 'postMessage',
  ]);

  //
  // Add Control for Center Logo Size (Range Slider)
  //

  $wp_customize->add_control('baum_center_logo_size_control', [
    'label'       => __('Center Logo Size', 'baum'),
    'section'     => 'baum_logo_section',
    'settings'    => 'baum_center_logo_size',
    'type'        => 'range',
    'input_attrs' => [
      'min'  => 10,
      'max'  => 60,
      'step' => 1,
    ],
  ]);

  //
  // Add Setting for Logo Size
  //

  $wp_customize->add_setting('baum_logo_size', [
    'default'           => 40,
    'sanitize_callback' => 'absint',
    'transport'         => 'postMessage',
  ]);

  //
  // Add Control for Logo Size (Range Slider)
  //

  $wp_customize->add_control('baum_logo_size_control', [
    'label'       => __('Logo Size', 'baum'),
    'section'     => 'baum_logo_section',
    'settings'    => 'baum_logo_size',
    'type'        => 'range',
    'input_attrs' => [
      'min'  => 10,
      'max'  => 60,
      'step' => 1,
    ],
  ]);

  //
  // Add Setting for Home Logo Size
  //

  $wp_customize->add_setting('baum_home_logo_size', [
    'default'           => 40,
    'sanitize_callback' => 'absint',
    'transport'         => 'postMessage',
  ]);

  //
  // Add Control for Home Logo Size (Range Slider)
  //

  $wp_customize->add_control('baum_home_logo_size_control', [
    'label'       => __('Home Logo Size', 'baum'),
    'section'     => 'baum_logo_section',
    'settings'    => 'baum_home_logo_size',
    'type'        => 'range',
    'input_attrs' => [
      'min'  => 10,
      'max'  => 60,
      'step' => 1,
    ],
  ]);

  //
  // Add Setting for Footer Logo Size
  //

  $wp_customize->add_setting('baum_footer_logo_size', [
    'default'           => 40,
    'sanitize_callback' => 'absint',
    'transport'         => 'postMessage',
  ]);

  //
  // Add Control for Footer Logo Size (Range Slider)
  //

  $wp_customize->add_control('baum_footer_logo_size_control', [
    'label'       => __('Footer Logo Size', 'baum'),
    'section'     => 'baum_logo_section',
    'settings'    => 'baum_footer_logo_size',
    'type'        => 'range',
    'input_attrs' => [
      'min'  => 10,
      'max'  => 60,
      'step' => 1,
    ],
  ]);

}

add_action('customize_register', 'baum_customize_logo');

//
// Output the CSS for the Custom Logo Size
//

/**
 * Outputs custom CSS for logo sizes
 *
 * This function generates and outputs inline CSS to control the size
 * of logos in different positions (footer, home, center, left) based
 * on the values set in the WordPress Customizer.
 *
 * @return void Outputs inline CSS
 *
 * @since 1.0.0
 */
function baum_custom_logo_size_css () {
  ?>
  <?php //echo get_theme_mod('baum_footer_logo_size', 40); ?>
  <style>
    .baum-footer-logo img {
      height: <?php echo get_theme_mod('baum_footer_logo_size', 40); ?>px;
    }
    .navbar-home .logo img {
      height: <?php echo get_theme_mod('baum_home_logo_size', 40); ?>px;
    }
    .navbar-center .logo img {
      height: <?php echo get_theme_mod('baum_center_logo_size', 40); ?>px;
    }
    .navbar-left-logo .logo img {
      height: <?php echo get_theme_mod('baum_logo_size', 40); ?>px;
    }
  </style>
  <?php
}

add_action('wp_head', 'baum_custom_logo_size_css');

/**
 * Sets JPEG quality to maximum (100%)
 *
 * This filter ensures that all JPEG images generated by WordPress
 * are saved at maximum quality to preserve image fidelity.
 *
 * @param int $arg The current JPEG quality setting
 * @return int Always returns 100 for maximum quality
 *
 * @since 1.0.0
 */
add_filter('jpeg_quality', function ($arg) { return 100; });

/////////////////////////////////////
// Load Plugins
/////////////////////////////////////

require_once get_template_directory() . '/plugins/class-tgm-plugin-activation.php';

/**
 * Registers required and recommended plugins for the theme
 *
 * This function uses the TGM Plugin Activation library to register
 * plugins that are required or recommended for the theme to function
 * properly. It defines which plugins should be installed and their
 * installation requirements.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_load_plugins () {
  $plugins = [
    [
      'name'              => 'Advanced Custom Fields',
      'slug'              => 'advanced-custom-fields',
      'required'          => true,
      'force_activation'  => false,
    ],
    [
      'name'              => 'Classic Widgets',
      'slug'              => 'classic-widgets',
      'required'          => true,
      'force_activation'  => true,
    ],
    [
      'name'              => 'Iframely',
      'slug'              => 'iframely',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'Post Views Counter',
      'slug'              => 'post-views-counter',
      'required'          => true,
      'force_activation'  => true,
    ],
    [
      'name'              => 'Regenerate Thumbnails',
      'slug'              => 'regenerate-thumbnails',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'Sharpen Resized Images',
      'slug'              => 'sharpen-resized-images',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'Simple History',
      'slug'              => 'simple-history',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'User Menus – Nav Menu Visibility',
      'slug'              => 'user-menus',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'User Switching',
      'slug'              => 'user-switching',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'WordPress Persistent Login',
      'slug'              => 'wp-persistent-login',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'WP Crontrol',
      'slug'              => 'wp-crontrol',
      'required'          => false,
      'force_activation'  => false,
    ],
    [
      'name'              => 'WP SmartCrop',
      'slug'              => 'wp-smartcrop',
      'required'          => false,
      'force_activation'  => false,
    ],
    // array(
    //   'name'              => 'Default Featured Image',
    //   'slug'              => 'default-featured-image',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
    // array(
    //   'name'              => 'Email Templates Customizer',
    //   'slug'              => 'email-templates',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
    // array(
    //   'name'              => 'Git it Write',
    //   'slug'              => 'git-it-write',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
    // array(
    //   'name'              => 'No Category Base',
    //   'slug'              => 'no-category-base-wpml',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
    // array(
    //   'name'              => 'Remove Dashboard Access',
    //   'slug'              => 'remove-dashboard-access-for-non-admins',
    //   'required'          => false,
    //   'force_activation'  => false,
    // ),
  ];
  $config = [
    'id'           => 'baumpress',             // Unique ID for hashing notices
    'menu'         => 'tgmpa-install-plugins', // Menu slug
    'has_notices'  => true,   // Show admin notices or not
    'dismissable'  => true,   // User can dismiss the nag message
    'dismiss_msg'  => '',     // Message at top of nag (if dismissable == false)
    'is_automatic' => false,  // Auto-activate plugins post install
    'message'      => '',     // Message right before plugins table
  ];
  tgmpa($plugins, $config);
}

add_action('tgmpa_register', 'baum_load_plugins'); // Pass plugins to TGMPA

/**
 * Theme font choices configuration
 *
 * This array defines all available font options for the theme's heading fonts.
 * It includes system fonts and custom font files with their display names.
 *
 * @since 1.0.0
 */
$baum_uri = get_template_directory_uri();

/**
 * Array of available font choices for theme headings
 *
 * Maps font file paths to their display names for use in the theme customizer.
 * Includes system fonts and various custom font files.
 *
 * @var array $baum_font_choices Associative array of font paths and names
 * @since 1.0.0
 */
$baum_font_choices = [
  'system' => 'System Font',
  $baum_uri . '/css/font/Aldrich-Regular.ttf' => 'Aldrich',
  $baum_uri . '/css/font/Alexandria-VariableFont_wght.ttf' => 'Alexandria',
  $baum_uri . '/css/font/BakbakOne-Regular.ttf' => 'Bakbak One',
  $baum_uri . '/css/font/ChangaOne-Regular.ttf' => 'Changa One',
  $baum_uri . '/css/font/ChangaOne-Italic.ttf' => 'Changa One Italic',
  $baum_uri . '/css/font/ChelseaMarket-Regular.ttf' => 'Chelsea Market',
  $baum_uri . '/css/font/ClashDisplay-Variable.woff2' => 'ClashDisplay',
  $baum_uri . '/css/font/ClashGrotesk-Variable.woff2' => 'ClashGrotesk',
  $baum_uri . '/css/font/DaysOne-Regular.ttf' => 'Days One',
  $baum_uri . '/css/font/DelaGothicOne-Regular.ttf' => 'Dela Gothic One',
  $baum_uri . '/css/font/EBGaramond-VariableFont_wght.ttf' => 'EB Garamond',
  $baum_uri . '/css/font/erbaum-bold-woff2' => 'Erbaum Bold',
  $baum_uri . '/css/font/erbaum-light-woff2' => 'Erbaum Light',
  $baum_uri . '/css/font/FingerPaint-Regular.ttf' => 'Finger Paint',
  $baum_uri . '/css/font/Freeman-Regular.ttf' => 'Freeman',
  $baum_uri . '/css/font/GermaniaOne-Regular.ttf' => 'Germania One',
  $baum_uri . '/css/font/Goldman-Regular.ttf' => 'Goldman',
  $baum_uri . '/css/font/Goldman-Bold.ttf' => 'Goldman Bold',
  $baum_uri . '/css/font/HammersmithOne-Regular.ttf' => 'Hammersmith One',
  $baum_uri . '/css/font/JockeyOne-Regular.ttf' => 'Jockey One',
  $baum_uri . '/css/font/Lalezar-Regular.ttf' => 'Lalezar',
  $baum_uri . '/css/font/Mitr-Regular.ttf' => 'Mitr',
  $baum_uri . '/css/font/Mitr-Bold.ttf' => 'Mitr Bold',
  $baum_uri . '/css/font/Mitr-Light.ttf' => 'Mitr Light',
  $baum_uri . '/css/font/Mitr-Medium.ttf' => 'Mitr Medium',
  $baum_uri . '/css/font/OdibeeSans-Regular.ttf' => 'Odibee Sans',
  $baum_uri . '/css/font/PassionOne-Regular.ttf' => 'Passion One',
  $baum_uri . '/css/font/RubikDirt-Regular.ttf' => 'Rubik Dirt',
  $baum_uri . '/css/font/RussoOne-Regular.ttf' => 'Russo One',
  $baum_uri . '/css/font/SecularOne-Regular.ttf' => 'Secular One',
  $baum_uri . '/css/font/Signika-VariableFont_GRAD,wght.ttf' => 'Signika',
  $baum_uri . '/css/font/SpaceMono-Bold.woff2' => 'SpaceMono Bold',
  $baum_uri . '/css/font/SpaceMono-Regular.woff2' => 'SpaceMono Regular',
  $baum_uri . '/css/font/SquadaOne-Regular.ttf' => 'Squada One',
  $baum_uri . '/css/font/TiltWarp-Regular.ttf' => 'Tilt Warp',
  $baum_uri . '/css/font/Viga-Regular.ttf' => 'Viga',
  $baum_uri . '/css/font/WorkSans-Italic-VariableFont_wght.ttf' => 'Work Sans Italic',
  $baum_uri . '/css/font/WorkSans-VariableFont_wght.ttf' => 'Work Sans',
];

/////////////////////////////////////
// Baum's Font Selection
/////////////////////////////////////

/**
 * Enqueues the selected font and outputs custom CSS for font styling
 *
 * This function gets the font selection from theme customizer settings
 * and either loads the custom font file or applies system font styling.
 * It also handles text transformation and letter spacing for headings.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enqueue_font_selection () {
  global $baum_font_choices;
  $selected_font = get_theme_mod('heading_font', 'system');
  $text_transform = get_theme_mod('heading_text_transform', 'Uppercase');
  $letter_spacing = get_theme_mod('heading_letter_spacing', 0);
  $font_display_name = $baum_font_choices[$selected_font]; // Value of name

  if ($selected_font === 'system') {
    error_log('$selected_font: system');

    //
    // CSS for system font
    //

    // .tag-listicle .baum-post-content li:before,
    // .baum_most_viewed_widget ol li::before,
    // .widget_post_views_counter_list_widget ol li::before,
    // .widget_categories ul li a,
    // .has-drop-cap:not(:focus):first-letter,
    // table.ics-calendar-month-grid ul.events span.title,
    // .page-title-action,
    // .ui-widget,
    // #adminmenu .wp-submenu-head,
    // #adminmenu a.menu-top,
    // .baum-widget-side-title,
    // .baum-author-box-name a,
    // input[type='submit'],
    // .btn,
    // .button,
    // button,
    // .wp-element-button,
    // label,
    // .wp-calendar-table caption,
    // #reply-title,
      // .baum-heading,

    $custom_css = "
      .wrap h1,
      .wrap h2,
      .menu-title,
      .baum-post-title,
      h1, h2, h3, h4, h5, h6 {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
        text-transform: $text_transform !important;
        letter-spacing: {$letter_spacing}em;
        font-style: normal;
        font-stretch: normal;
      }
    ";
  } else {

    // //
    // // Enqueue the selected custom font
    // //
    // wp_enqueue_style(
    //   'baum-font-selection',
    //   $selected_font,
    //   [],
    //   null,
    //   'all'
    // );

    //
    // Font-face for custom font
    //

    // .tag-listicle .baum-post-content li:before,
    // .baum_most_viewed_widget ol li::before,
    // .widget_post_views_counter_list_widget ol li::before,
    // .widget_categories ul li a,
    // .has-drop-cap:not(:focus):first-letter,
    // table.ics-calendar-month-grid ul.events span.title,
    // .page-title-action,
    // .ui-widget,
    // #adminmenu .wp-submenu-head,
    // #adminmenu a.menu-top,
    // label,
    // .wp-calendar-table caption,
    // .menu-title,
    // #reply-title,
    // .baum-widget-side-title,
    // .baum-author-box-name a,
    // input[type='submit'],
    // .btn,
    // .button,
    // button,
    // .wp-element-button,
    // h2, h3, h4, h5, h6
      // .baum-heading,

    $custom_css = "
      @font-face {
        font-family: '$font_display_name';
        src: url('$selected_font') format('truetype');
      }
      .wrap h1,
      .wrap h2,
      .baum-post-title,
      h1 {
        font-family: '$font_display_name', sans-serif;
        text-transform: $text_transform !important;
        letter-spacing: {$letter_spacing}em;
        font-display: auto;
        font-style: normal;
        font-weight: bold;
        font-stretch: normal;
      }
    ";
  }
  wp_register_style('baum-inline-font-selection', false);
  wp_enqueue_style('baum-inline-font-selection');
  wp_add_inline_style('baum-inline-font-selection', $custom_css);
}

add_action('wp_enqueue_scripts', 'baum_enqueue_font_selection');

/**
 * Gets the path to the selected heading font
 *
 * This function retrieves the path to the font file selected in the theme
 * customizer for headings. If no font is selected or the system font is
 * chosen, it returns the path to the default Noto Sans font.
 *
 * @return string Path to the font file
 *
 * @since 1.0.0
 */
function baum_get_heading_font_path() {
  global $baum_font_choices;

  // Get the selected font from theme mods
  $selected_font = get_theme_mod('heading_font', 'system');

  // Define the default font path
  $default_font_path = $baum_uri . '/css/font/NotoSans-VariableFont_wdth,wght.ttf';

  // If the selected font is empty or 'system', return the default font path
  if (empty($selected_font) || $selected_font === 'system') {
      return $default_font_path;
  }

  return $selected_font;
}

/////////////////////////////////////
// Customizer Additions
/////////////////////////////////////

require get_template_directory() . '/inc/customizer.php';

//
//
//

// echo get_theme_color('red'); // Returns hex,  #9c0404
// echo get_theme_color('primary', 'rgb'); // Returns RGB, "188, 12, 12"
// echo get_theme_color('secondary', 'rgba'); // Returns RGBA, "0, 0, 0, 1.0"

//
//
//

/**
 * Gets a theme color in the specified format
 *
 * This function retrieves colors from the theme's color configuration based on
 * the selected theme and dark mode settings. It supports multiple output formats
 * and automatically handles light/dark mode switching.
 *
 * @param string $color The color name to retrieve (e.g., 'primary', 'secondary')
 * @param string $format The output format: 'hex', 'rgb', or 'rgba'
 * @return string|null The color value in the requested format, or null if not found
 *
 * @since 1.0.0
 */
function get_theme_color ($color, $format = 'hex') {
  // Get the selected theme and dark mode setting
  $theme = get_theme_mod('baum_custom_theme_selector', 'default');
  $dark_mode = get_theme_mod('baum_darkmode', 'auto'); // 'auto', 'on', or 'off'

  // Include the themes configuration
  $color_themes = include get_stylesheet_directory() . '/inc/baum-color-themes.php';

  // Get the theme data, fallback to 'default' theme
  $theme_data = $color_themes['themes'][$theme] ?? $color_themes['themes']['default'];

  // Determine whether to use light or dark colors
  $mode = ($dark_mode === 'on' || ($dark_mode === 'auto' && isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === '1')) ? 'dark' : 'light';

  // Retrieve the color value
  $color_value = $theme_data[$mode][$color] ?? $theme_data[$mode]['color'][$color] ?? null;

  // If the color is not found, return null
  if (!$color_value) {
      return null;
  }

  // Convert to the requested format
  switch ($format) {
      case 'rgba':
          return convert_hex_to_rgba($color_value);
      case 'rgb':
          return convert_hex_to_rgb($color_value);
      case 'hex':
      default:
          return $color_value;
  }
}

/**
 * Converts a hex color to RGBA format
 *
 * This helper function takes a hex color value and converts it to RGBA format
 * with the specified alpha transparency value.
 *
 * @param string $hex The hex color value (with or without #)
 * @param float $alpha The alpha transparency value (0.0 to 1.0)
 * @return string The RGBA color string
 *
 * @since 1.0.0
 */
function convert_hex_to_rgba ($hex, $alpha = 1.0) {
  $rgb = convert_hex_to_rgb($hex);
  return "rgba({$rgb}, {$alpha})";
}

/**
 * Converts a hex color to RGB format
 *
 * This helper function takes a hex color value and converts it to RGB format.
 * It handles both 3-digit and 6-digit hex values.
 *
 * @param string $hex The hex color value (with or without #)
 * @return string The RGB color values as comma-separated string
 *
 * @since 1.0.0
 */
function convert_hex_to_rgb ($hex) {
  $hex = ltrim($hex, '#');

  if (strlen($hex) === 3) {
    $hex = "{$hex[0]}{$hex[0]}{$hex[1]}{$hex[1]}{$hex[2]}{$hex[2]}";
  }

  $r = hexdec(substr($hex, 0, 2));
  $g = hexdec(substr($hex, 2, 2));
  $b = hexdec(substr($hex, 4, 2));

  return "{$r}, {$g}, {$b}";
}


/////////////////////////////////////
// Baum Press Theme Setup
/////////////////////////////////////

/**
 * Sets up theme defaults and registers support for various WordPress features
 *
 * This function is hooked into the 'after_setup_theme' action and is used to
 * initialize various theme features, register support for WordPress functionality,
 * and set up theme defaults like menus and sidebars.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_setup () {

  //
  // Enable Comments on Media File Attachment Pages
  //

  add_post_type_support('attachment', 'comments');
  add_theme_support('editor-styles');
  add_theme_support('post-thumbnails');
  add_theme_support('responsive-embeds');
  add_theme_support('title-tag');
  add_theme_support('html5', [
    'comment-list',
    'comment-form',
    'search-form',
    'gallery',
    'caption',
    'style',
    'script'
  ]);
  add_theme_support('dark-editor-style');
  add_theme_support('menus');
  add_theme_support('editor-styles'); // Styles in Gutenberg

  set_post_thumbnail_size(1000, 1000, true);

  add_image_size('baum-headline-thumb', 1100, 500, true);
  add_image_size('baum-full-thumb', 1100, 500, true);
  add_image_size('baum-full-mobile-thumb', 382, 325, true);
  add_image_size('baum-wide-thumb', 730, 340, true);
  add_image_size('baum-xlarge-thumb', 540, 300, true);
  add_image_size('baum-large-thumb', 357, 238, true);
  add_image_size('baum-medium-thumb', 265, 183, true);
  add_image_size('baum-square-thumb', 175, 175, true);
  add_image_size('baum-small-thumb', 128, 128, true);
  add_image_size('baum-book-thumb', 165, 250, true);
  add_image_size('baum-mini-thumb', 64, 64, true);

  add_image_size('baum-facebook-thumb', 1200, 630, true);
  add_image_size('baum-x-thumb', 1600, 900, true);
  add_image_size('baum-instagram-thumb', 1080, 1080, true);
  add_image_size('baum-linkedin-thumb', 1200, 630, true);

  add_image_size('baum-ico-thumb', 12, 12, true);
  add_image_size('baum-32-thumb', 32, 32, true);
  add_image_size('baum-48-thumb', 48, 48, true);
  add_image_size('baum-64-thumb', 64, 64, true);
  add_image_size('baum-128-thumb', 128, 128, true);
  add_image_size('baum-256-thumb', 256, 256, true);
  add_image_size('baum-512-thumb', 512, 512, true);
  add_image_size('baum-1024-thumb', 1024, 1024, true);

  add_image_size('baum-16-9-thumb', 1100, 619, true);
  add_image_size('baum-5-3-thumb', 1100, 660, true);
  add_image_size('baum-3-2-thumb', 1100, 733, true);
  add_image_size('baum-4-3-thumb', 1100, 825, true);
  add_image_size('baum-5-4-thumb', 1100, 880, true);
  add_image_size('baum-1-1-thumb', 1000, 1000, true);
  add_image_size('baum-4-5-thumb', 880, 1100, true);
  add_image_size('baum-3-4-thumb', 825, 1100, true);
  add_image_size('baum-2-3-thumb', 733, 1100, true);
  add_image_size('baum-3-5-thumb', 660, 1100, true);
  add_image_size('baum-9-16-thumb', 619, 1100, true);

  // The recommended size for Open Graph (OG) images on Facebook is
  // 1200 x 630 pixels, adhering to an aspect ratio of approximately
  // 1.91:1. This ensures that images display optimally across both
  // desktop and mobile devices, providing a clear and engaging visual
  // when your content is shared.
  add_image_size('baum-social-thumb', 1200, 630, true);


  // add_theme_support('yoast-seo-breadcrumbs');
  // add_theme_support('post-formats', ['link', 'gallery', 'video']);
  // Add support for various features
  // add_theme_support('custom-logo');
  // add_theme_support('custom-header');
  // add_theme_support('custom-background');
  // add_theme_support('post-thumbnails'); // Featured images
  // add_theme_support('title-tag'); // Dynamic title tags
  // add_theme_support('automatic-feed-links'); // RSS feed links in head
  // add_theme_support('html5', ['search-form', 'comment-form', 'comment-list', 'gallery', 'caption']); // HTML5 support for specific elements
  // add_theme_support('align-wide'); // Wide alignment option for Gutenberg blocks
  // // add_theme_support('admin-bar');
  // add_theme_support('align-wide');
  // add_theme_support('appearance-tools');
  // // add_theme_support('automatic-feed-links');
  // add_theme_support('block-templates');
  // add_theme_support('block-template-parts');
  // add_theme_support('border');
  // add_theme_support('core-block-patterns');
  // // add_theme_support('custom-background');
  // add_theme_support('custom-header');
  // add_theme_support('custom-line-height');
  // add_theme_support('custom-logo', array(
  //   'height'               => 50,
  //   'width'                => 200,
  //   'flex-height'          => true,
  //   'flex-width'           => true,
  //   'header-text'          => array( 'site-title', 'site-description' ),
  //   'unlink-homepage-logo' => true,
  //   ));
  // add_theme_support('customize-selective-refresh-widgets');
  // add_theme_support('custom-spacing');
  // add_theme_support('custom-units');
  // add_theme_support('dark-editor-style');
  // add_theme_support('disable-custom-colors');
  // add_theme_support('disable-custom-font-sizes');
  // add_theme_support('disable-custom-gradients');
  // add_theme_support('disable-layout-styles');
  // add_theme_support('editor-color-palette');
  // add_theme_support('editor-gradient-presets');
  // // add_theme_support('editor-font-sizes');
  // add_theme_support('editor-spacing-sizes');
  // add_theme_support('featured-content');
  // add_theme_support('html5', [
  //   'comment-list',
  //   'comment-form',
  //   'search-form',
  //   'gallery',
  //   'caption',
  //   'style',
  //   'script'
  // ]);
  // add_theme_support('link-color');
  // add_theme_support('menus');
  // add_theme_support('post-formats');
  // add_theme_support('post-thumbnails');
  // add_theme_support('responsive-embeds');
  // add_theme_support('starter-content');
  // add_theme_support('title-tag');
  // add_theme_support('widgets');
  // add_theme_support('widgets-block-editor');
  // add_theme_support('wp-block-styles');
}

add_action('after_setup_theme', 'baum_setup');

/**
 * Enqueues additional scripts for BaumPress functionality
 *
 * This function is currently used for future script enqueuing and contains
 * commented code for voting functionality that can be enabled as needed.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baumpress_enqueue_scripts() {
  // wp_enqueue_script('baumpress-vote', get_template_directory_uri() . '/js/baumpress-vote.js', array('jquery'), null, true);
  // wp_localize_script('baumpress-vote', 'baumpress_ajax_url', admin_url('admin-ajax.php'));
}

add_action('wp_enqueue_scripts', 'baumpress_enqueue_scripts');

/**
 * Enqueues lightweight frontend editor assets
 *
 * This function loads a lightweight rich text editor for the frontend
 * that's much more resource-efficient than full Gutenberg.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enqueue_gutenberg_frontend() {
  // Only load on style guide page and for users with edit_posts capability
  if (!is_page('style-guide') || !current_user_can('edit_posts')) {
    return;
  }

  // Enqueue our lightweight frontend editor script (no heavy dependencies)
  wp_enqueue_script(
    'baum-frontend-gutenberg',
    get_template_directory_uri() . '/js/frontend-gutenberg.js',
    ['jquery'], // Only jQuery dependency
    wp_get_theme()->get('Version'),
    true
  );

  // Localize script with necessary data
  wp_localize_script('baum-frontend-gutenberg', 'baumGutenberg', [
    'apiUrl' => home_url('/wp-json/wp/v2/'),
    'nonce' => wp_create_nonce('wp_rest'),
    'currentUser' => get_current_user_id(),
    'canPublish' => current_user_can('publish_posts'),
    'restNonce' => wp_create_nonce('wp_rest'),
    'allowedBlocks' => baum_get_allowed_frontend_blocks(),
  ]);
}
add_action('wp_enqueue_scripts', 'baum_enqueue_gutenberg_frontend');

/**
 * Get allowed blocks for frontend Gutenberg editor
 *
 * This function returns an array of block types that are allowed
 * in the frontend editor. This provides control over which blocks
 * users can use when creating content from the frontend.
 *
 * @return array Array of allowed block type names
 *
 * @since 1.0.0
 */
function baum_get_allowed_frontend_blocks() {
  $allowed_blocks = [
    // Text blocks
    'core/paragraph',
    'core/heading',
    'core/list',
    'core/quote',
    'core/pullquote',
    'core/verse',
    'core/preformatted',

    // Media blocks
    'core/image',
    'core/gallery',
    'core/video',
    'core/audio',
    'core/file',

    // Design blocks
    'core/separator',
    'core/spacer',
    'core/columns',
    'core/column',
    'core/group',
    'core/cover',

    // Widgets
    'core/shortcode',
    'core/html',
    'core/code',
    'core/table',

    // Embeds
    'core/embed',
    'core-embed/youtube',
    'core-embed/twitter',
    'core-embed/instagram',
    'core-embed/vimeo',
    'core-embed/spotify',

    // Theme blocks (if any custom blocks exist)
    'baum/cards',
    'baum/inline-embed',
  ];

  // Allow filtering of allowed blocks
  return apply_filters('baum_frontend_allowed_blocks', $allowed_blocks);
}


// //
// // Setup Activity for Bomber Timelines
// //
// function register_activity_post_type () {

//   //
//   // Register the Activity Post Type
//   //
//   register_post_type('activity', [
//     'labels' => [
//       'name' => 'Activities',
//       'singular_name' => 'Activity',
//     ],
//     'public' => true,
//     'hierarchical' => false,
//     'has_archive' => true,
//     'supports' => [
//       'title', 'editor', 'comments', 'author', 'thumbnail', 'custom-fields'
//     ],
//     'rewrite' => ['slug' => 'activity'],
//   ]);

//   //
//   // Register the Content Type Taxonomy
//   //
//   register_taxonomy('content_type', 'activity', [
//     'labels' => [
//       'name' => 'Content Types',
//       'singular_name' => 'Content Type',
//     ],
//     'public' => true,
//     'hierarchical' => true,
//     'rewrite' => ['slug' => 'content-type'],
//   ]);
// }

// add_action('init', 'register_activity_post_type');

// //
// // Add terms like video, image, live_video, and life_event
// // to the content_type taxonomy
// //
// function add_activity_content_types () {
//   $content_types = ['live_video', 'image', 'video', 'life_event' ];
//   foreach ($content_types as $type) {
//     if (!term_exists($type, 'content_type')) {
//       wp_insert_term($type, 'content_type');
//     }
//   }
// }

// add_action('init', 'add_activity_content_types');

/**
 * Uses custom multiple featured images as the post thumbnail
 *
 * This filter intercepts requests for the post thumbnail ID and returns
 * the first image from the 'multiple_featured_images' custom field instead.
 * This allows posts to have multiple featured images while maintaining
 * compatibility with WordPress's featured image system.
 *
 * @param mixed $value The current meta value
 * @param int $object_id The post ID
 * @param string $meta_key The meta key being requested
 * @param bool $single Whether to return a single value
 * @return mixed The first image ID from multiple_featured_images or original value
 *
 * @since 1.0.0
 */
add_filter('get_post_metadata', function ($value, $object_id, $meta_key, $single) {
  if ($meta_key === '_thumbnail_id') {
    // Replace this with your custom logic
    $image_ids = get_post_meta($object_id, 'multiple_featured_images', true);

    if (is_array($image_ids) && !empty($image_ids)) {
      return $image_ids[0]; // Use the first image as the "featured image"
    }

    return null; // No thumbnail available
  }

  return $value; // Default behavior for other meta keys
}, 10, 4);


//   function get_post_thumbnail_id($post_id = null) {
//       $post_id = $post_id ?: get_the_ID();
//       $image_ids = get_post_meta($post_id, 'multiple_featured_images', true);

//       // Return a random image if available
//       if (is_array($image_ids) && !empty($image_ids)) {
//           return $image_ids[array_rand($image_ids)];
//       }

//       // Fallback to a default image (optional)
//       // $default_image_id = 123; // Replace with your default image ID
//       return null; // $default_image_id;
//   }

// //
// // Override Featured Image with Random Image
// //
// function load_random_featured_image($html, $post_id) {
//   $image_ids = get_post_meta($post_id, 'multiple_featured_images', true);
//   if ($image_ids && is_array($image_ids)) {
//       $random_image_id = $image_ids[array_rand($image_ids)];
//       $html = wp_get_attachment_image($random_image_id, 'full', false, ['style' => 'border-radius: 8px;']);
//   }
//   return $html;
// }
// add_filter('post_thumbnail_html', 'load_random_featured_image', 10, 2);

/**
 * Loads JSON data from the theme's data directory
 *
 * This function loads and decodes JSON files from the theme's `/data/` folder.
 * It provides a centralized way to access structured data files for the theme.
 *
 * @param string $filename The name of the JSON file (without extension)
 * @return array The decoded JSON data or empty array if file doesn't exist
 *
 * @since 1.0.0
 *
 * @example load_json_data('phones') // Loads data/phones.json
 */
function load_json_data ($filename) {
  // Sanitize filename to prevent directory traversal
  $filename = sanitize_file_name($filename);

  // Path to the JSON file
  $json_file = get_template_directory() . "/data/" . $filename . ".json";

  if (file_exists($json_file) && is_readable($json_file)) { // Check if the file exists and is readable
    $json_content = file_get_contents($json_file);
    $json_data = json_decode($json_content, true);

    // Return data if valid JSON, otherwise empty array
    return (json_last_error() === JSON_ERROR_NONE) ? $json_data : [];
  }
  return []; // Return empty array if file is missing or unreadable
}

/**
 * Filters the wp_die handler to use a custom error page
 *
 * This filter replaces the default WordPress error handler with a custom
 * one that provides better styling and user experience for error pages.
 *
 * @param callable $handler The current wp_die handler
 * @return string The custom handler function name
 *
 * @since 1.0.0
 */
add_filter('wp_die_handler', function ($handler) {
	// return ! is_admin() ? 'baum_wp_die_handler' : $handler;
	return 'baum_wp_die_handler';
}, 10);

/**
 * Custom wp_die handler for better error display
 *
 * This function provides a custom error handler that displays errors
 * using the theme's styling and layout instead of the default WordPress
 * error page. It handles both WP_Error objects and string messages.
 *
 * @param string|WP_Error $message The error message or WP_Error object
 * @param string $title The error title
 * @param array $args Additional arguments for the error handler
 * @return void This function terminates execution
 *
 * @since 1.0.0
 */
function baum_wp_die_handler ($message, $title = '', $args = []) {
	$defaults = [ 'response' => 500 ];
	$r = wp_parse_args($args, $defaults);

	if (function_exists('is_wp_error') && is_wp_error($message)) {
		$errors = $message->get_error_messages();
		switch (count($errors)) {
			case 0 :
				$message = '';
				break;
			case 1 :
				$message = $errors[0];
				break;
			default :
				$message = "<ul>\n\t\t<li>"
          . join("</li>\n\t\t<li>", $errors)
          . "</li>\n\t</ul>";
				break;
		}
	} else {
		$message = strip_tags($message);
	}

	require_once get_template_directory() . '/wp-die.php';
	die();
}

// add_filter('wp_robots', function ($ret) {
//   global $pagenow;
//   if ('wp-comments-post.php' !== $pagenow) return $ret;
//   $url = get_template_directory_uri() . '/style.css';
//   echo '<link rel="stylesheet" href="' . $url . '">';
//   return $ret;
// });

/**
 * Extends WordPress upload MIME types to support additional file formats
 *
 * This function adds support for a wide variety of file types including
 * text files, programming files, documents, images, audio, video, 3D models,
 * CAD files, e-books, GIS files, and archives. It also removes potentially
 * harmful executable file types for security.
 *
 * @param array $mimes The existing array of allowed MIME types
 * @return array The modified array with additional MIME types
 *
 * @since 1.0.0
 */
function baum_upload_mimes ($mimes) {

  //
  // Text and Logs
  //

  $mimes['txt'] = 'text/plain'; // Plain text
  $mimes['log'] = 'text/plain'; // Log files
  $mimes['md'] = 'text/markdown'; // Markdown
  $mimes['markdown'] = 'text/markdown'; // Markdown (alternative)
  // $mimes['csv'] = 'text/csv'; // CSV files
  $mimes['json'] = 'application/json'; // JSON files
  $mimes['xml'] = 'application/xml'; // XML files
  $mimes['yaml'] = 'application/x-yaml'; // YAML files
  $mimes['yml'] = 'application/x-yaml'; // YAML (alternative)
  $mimes['graphql'] = 'application/graphql'; // GraphQL schema
  $mimes['sql'] = 'application/sql'; // SQL files

  //
  // Programming Files
  //

  $mimes['html'] = 'text/html'; // HTML
  $mimes['htm'] = 'text/html'; // HTML (alternative)
  $mimes['css'] = 'text/css'; // CSS
  $mimes['php'] = 'application/x-httpd-php'; // PHP
  $mimes['sql'] = 'application/sql'; // SQL (repeated)
  $mimes['js'] = 'application/javascript'; // JavaScript
  $mimes['ts'] = 'application/typescript'; // TypeScript
  $mimes['sh'] = 'application/x-sh'; // Shell script
  $mimes['bash'] = 'application/x-sh'; // Bash script
  $mimes['java'] = 'text/x-java-source'; // Java
  $mimes['c'] = 'text/x-c'; // C
  $mimes['cpp'] = 'text/x-c++src'; // C++
  $mimes['py'] = 'text/x-python'; // Python

  //
  // Spreadsheet / Tabular Files
  //

  $mimes['xls'] = 'application/vnd.ms-excel'; // Excel 97-2003
  $mimes['xlsx'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; // Excel Modern
  $mimes['ods'] = 'application/vnd.oasis.opendocument.spreadsheet'; // OD Sheet
  $mimes['numbers'] = 'application/x-iwork-numbers-sffnumbers'; // Apple Numbers
  $mimes['csv'] = 'text/csv'; // CSV files (repeated from text)

  //
  // Document Files
  //

  $mimes['doc'] = 'application/msword'; // Word 97-2003
  $mimes['docx'] = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'; // Word Modern
  $mimes['odt'] = 'application/vnd.oasis.opendocument.text'; // Open Doc Text
  $mimes['rtf'] = 'application/rtf'; // Rich Text Format
  $mimes['pdf'] = 'application/pdf'; // PDF
  $mimes['pages'] = 'application/x-iwork-pages-sffpages'; // Apple Pages

  //
  // Podium Presentation Files
  //

  $mimes['ppt'] = 'application/vnd.ms-powerpoint'; // PowerPoint 97-2003
  $mimes['pptx'] = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'; // PowerPoint Modern
  $mimes['key'] = 'application/x-iwork-keynote-sffkey'; // Apple Keynote
  $mimes['odp'] = 'application/vnd.oasis.opendocument.presentation';
    // OpenDocument Presentation

  //
  // Image Files
  //

  $mimes['jpg'] = 'image/jpeg'; // JPEG
  $mimes['jpeg'] = 'image/jpeg'; // JPEG (alternative)
  $mimes['png'] = 'image/png'; // PNG
  $mimes['gif'] = 'image/gif'; // GIF
  $mimes['bmp'] = 'image/bmp'; // BMP
  $mimes['svg'] = 'image/svg+xml'; // SVG
  $mimes['webp'] = 'image/webp'; // WebP

  //
  // Adobe Creative Suite File Types
  //

  $mimes['psd'] = 'image/vnd.adobe.photoshop'; // Adobe Photoshop
  $mimes['ai'] = 'application/postscript'; // Adobe Illustrator
  $mimes['indd'] = 'application/x-indesign'; // Adobe InDesign
  $mimes['xd'] = 'application/vnd.adobe.xd'; // Adobe XD
  $mimes['pdf'] = 'application/pdf'; // Adobe Acrobat
  $mimes['ae'] = 'application/octet-stream'; // After Effects
  $mimes['prproj'] = 'application/octet-stream'; // Premiere Pro Project
  $mimes['enc'] = 'application/octet-stream'; // Adobe Encore Project
  $mimes['otf'] = 'application/x-font-otf'; // Adobe Fonts (OpenType)
  $mimes['ttf'] = 'application/x-font-ttf'; // Adobe Fonts (TrueType)
  $mimes['dng'] = 'image/x-adobe-dng'; // Adobe Digital Negative
  $mimes['abr'] = 'application/octet-stream'; // Photoshop Brushes
  $mimes['asl'] = 'application/octet-stream'; // Photoshop Layer Styles
  $mimes['aep'] = 'application/octet-stream'; // After Effects Project
  $mimes['mogr'] = 'application/octet-stream'; // Motion Graphics Template
  $mimes['lrcat'] = 'application/octet-stream'; // Lightroom Catalog
  $mimes['lrtemplate'] = 'application/octet-stream'; // Lightroom Preset
  $mimes['swf'] = 'application/x-shockwave-flash'; // Flash (SWF)

  //
  // 3D Printing and Modeling Files
  //

  $mimes['stl'] = 'application/sla'; // Stereolithography (3D printing)
  $mimes['obj'] = 'application/octet-stream'; // Wavefront OBJ (3D model)
  $mimes['ply'] = 'application/octet-stream'; // Polygon File Format
  $mimes['fbx'] = 'application/octet-stream'; // Autodesk FBX
  $mimes['glb'] = 'model/gltf-binary'; // GL Transmission Format (binary)
  $mimes['gltf'] = 'model/gltf+json'; // GL Transmission Format (JSON)
  $mimes['3ds'] = 'application/x-3ds'; // 3D Studio Mesh
  $mimes['blend'] = 'application/x-blender'; // Blender file
  $mimes['dae'] = 'model/vnd.collada+xml'; // COLLADA
  $mimes['x3d'] = 'model/x3d+xml'; // X3D
  $mimes['vrml'] = 'model/vrml'; // Virtual Reality Modeling Language
  $mimes['iges'] = 'model/iges'; // IGES
  $mimes['step'] = 'model/step'; // STEP
  $mimes['prt'] = 'application/octet-stream'; // NX/Creo Part File
  $mimes['3mf'] = 'application/vnd.ms-package.3dmanufacturing-3dmodel+xml';
    // 3MF (3D Manufacturing Format)

  //
  // CAD Files
  //

  $mimes['dwg'] = 'application/acad'; // AutoCAD Drawing
  $mimes['dxf'] = 'application/dxf'; // AutoCAD DXF
  $mimes['igs'] = 'model/iges'; // IGES (Initial Graphics Exchange)
  $mimes['stp'] = 'model/step'; // STEP (Standard Exchange of Product Data)
  $mimes['catpart'] = 'application/octet-stream'; // CATIA Part
  $mimes['catdrawing'] = 'application/octet-stream'; // CATIA Drawing
  $mimes['sat'] = 'application/octet-stream'; // ACIS File
  $mimes['sldprt'] = 'application/octet-stream'; // SolidWorks Part
  $mimes['sldasm'] = 'application/octet-stream'; // SolidWorks Assembly
  $mimes['ipt'] = 'application/octet-stream'; // Autodesk Inventor Part
  $mimes['iam'] = 'application/octet-stream'; // Autodesk Inventor Assembly
  $mimes['asm'] = 'application/octet-stream'; // Pro/ENGINEER Assembly
  $mimes['prt'] = 'application/octet-stream'; // Pro/ENGINEER Part

  //
  // Video Files
  //

  $mimes['mp4'] = 'video/mp4'; // MP4
  $mimes['m4v'] = 'video/x-m4v'; // M4V
  $mimes['mov'] = 'video/quicktime'; // QuickTime
  $mimes['wmv'] = 'video/x-ms-wmv'; // Windows Media Video
  $mimes['avi'] = 'video/x-msvideo'; // AVI
  $mimes['webm'] = 'video/webm'; // WebM
  $mimes['mkv'] = 'video/x-matroska'; // Matroska

  //
  // Video Production Files
  //

  $mimes['prproj'] = 'application/octet-stream'; // Premiere Pro Project
  $mimes['ae'] = 'application/octet-stream'; // After Effects Project
  $mimes['aep'] = 'application/octet-stream'; // After Effects Project (alt.)
  $mimes['dav'] = 'video/dav'; // DAV (DVR Video)
  $mimes['ogv'] = 'video/ogg'; // OGG Video
  $mimes['mts'] = 'video/MP2T'; // MPEG Transport Stream
  $mimes['drp'] = 'application/octet-stream'; // DaVinci Resolve Project
  $mimes['mgp'] = 'application/octet-stream'; // Magic Bullet Suite Project

  //
  // Audio Files
  //

  $mimes['mp3'] = 'audio/mpeg'; // MP3
  $mimes['wav'] = 'audio/wav'; // WAV
  $mimes['ogg'] = 'audio/ogg'; // OGG
  $mimes['m4a'] = 'audio/x-m4a'; // M4A
  $mimes['flac'] = 'audio/x-flac'; // FLAC

  //
  // Audio Production Files
  //

  $mimes['aif'] = 'audio/x-aiff'; // Audio Interchange File Format (AIFF)
  $mimes['aiff'] = 'audio/x-aiff'; // AIFF (alternative)
  $mimes['als'] = 'application/octet-stream'; // Ableton Live Set
  $mimes['flp'] = 'application/octet-stream'; // FL Studio Project
  $mimes['logicx'] = 'application/octet-stream'; // Logic Pro Project
  $mimes['cubase'] = 'application/octet-stream'; // Cubase Project
  $mimes['midi'] = 'audio/midi'; // MIDI File
  $mimes['mid'] = 'audio/midi'; // MIDI File (alternative)
  $mimes['omf'] = 'application/octet-stream'; // Open Media Framework (A/V)
  $mimes['audacity'] = 'application/octet-stream'; // Audacity Project

  //
  // E-Book Files
  //

  $mimes['epub'] = 'application/epub+zip'; // EPUB (Open standard)
  $mimes['mobi'] = 'application/x-mobipocket-ebook'; // Mobipocket (Kindle)
  $mimes['azw'] = 'application/vnd.amazon.ebook'; // Amazon Kindle AZW
  $mimes['azw3'] = 'application/vnd.amazon.mobi8-ebook'; // Amazon Kindle AZW3
  $mimes['pdf'] = 'application/pdf'; // PDF (Portable Document Format)
  $mimes['ibooks'] = 'application/x-ibooks+zip'; // Apple iBooks
  $mimes['lit'] = 'application/x-ms-reader'; // Microsoft Reader
  $mimes['cbr'] = 'application/x-cbr'; // Comic Book Archive (RAR-based)
  $mimes['cbz'] = 'application/x-cbz'; // Comic Book Archive (ZIP-based)

  //
  // GIS Files
  //

  $mimes['shp'] = 'application/octet-stream'; // ESRI Shapefile
  $mimes['dbf'] = 'application/octet-stream'; // Shapefile Attribute Database
  $mimes['geojson'] = 'application/geo+json'; // GeoJSON
  $mimes['kml'] = 'application/vnd.google-earth.kml+xml'; // Keyhole ML
  $mimes['kmz'] = 'application/vnd.google-earth.kmz'; // Keyhole ML (compressed)
  $mimes['gpx'] = 'application/gpx+xml'; // GPS Exchange Format
  $mimes['grib'] = 'application/octet-stream'; // GRIB (Weather Data)
  $mimes['tif'] = 'image/tiff'; // GeoTIFF
  $mimes['tiff'] = 'image/tiff'; // GeoTIFF (alternative)
  $mimes['qgs'] = 'application/octet-stream'; // QGIS Project File
  $mimes['osm'] = 'application/octet-stream'; // OpenStreetMap Data
  $mimes['asc'] = 'application/octet-stream'; // ASCII Grid
  $mimes['xyz'] = 'application/octet-stream'; // XYZ (Point Cloud)

  //
  // Archive Files
  //

  $mimes['zip'] = 'application/zip'; // ZIP
  $mimes['rar'] = 'application/x-rar-compressed'; // RAR
  $mimes['7z'] = 'application/x-7z-compressed'; // 7-Zip
  $mimes['tar'] = 'application/x-tar'; // TAR
  $mimes['gz'] = 'application/gzip'; // GZip

  //
  // Remove potentially harmful types (e.g., executables)
  //

  unset($mimes['exe']);
  unset($mimes['bat']);
  unset($mimes['bin']);
  return $mimes;
}

add_filter('upload_mimes', 'baum_upload_mimes');

/////////////////////////////////////
// Enqueue Assets
/////////////////////////////////////

/**
 * Enqueues scripts and styles for the front-end
 *
 * This function loads various CSS and JavaScript files needed for the theme,
 * including Font Awesome, jQuery plugins, syntax highlighting, and custom
 * theme styles. It also handles localization of scripts for AJAX functionality.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enqueue_scripts () {
  global $wp_styles;
  $version = wp_get_theme()->get('Version');

  //
  // Font Awesome 6
  //
  wp_enqueue_style(
    'font-awesome-6',
    get_template_directory_uri() . '/css/font-awesome-6.css',
    [],
    '6.0.0',
    'all',
  );

  //
  // Font Awesome 5
  //
  // wp_enqueue_style(
  //   'font-awesome-5',
  //   get_template_directory_uri() . '/css/font-awesome-5.css',
  //   [],
  //   '5.12.1',
  //   'all'
  // );

  //
  // Font Awesome 6
  //
  // TODO: Save the font files locally and link to them locally
  // wp_enqueue_style(
  //   'font-awesome-6',
  //   'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css',
  //   [],
  //   '6.0.0',
  //   'all',
  // );


  //
  // jQuery Infinite Scroll
  //
  wp_enqueue_script(
    'baum-infinite',
    get_template_directory_uri() . '/js/infinite-scroll.min.js',
    ['jquery'],
    '1.0',
    true
  );

  //
  // Tooltipster JS
  //
  wp_enqueue_script(
    'baum-tooltips-js',
    get_template_directory_uri() . '/js/tooltipster.bundle.min.js',
    ['jquery'],
    '1.0',
    true
  );

  //
  // Tooltipster CSS
  //
  wp_enqueue_style(
    'tooltipster-css',
    get_template_directory_uri() . '/css/tooltipster.bundle.min.css',
    [],
    $version,
    'all'
  );

  //
  // Tooltipster CSS - Borderless Theme
  //
  wp_enqueue_style(
    'tooltipster-theme-css',
    get_template_directory_uri() . '/css/tooltipster-borderless.min.css',
    ['tooltipster-css'],
    $version,
    'all'
  );

  //
  // Syntax Highlighting - Atom One (dark)
  //
  wp_enqueue_style(
    'baum-atom-one-dark',
    get_template_directory_uri() . '/css/atom-one-dark.css',
    [],
    $version,
    'all'
  );

  // //
  // // Syntax Highlighting - Atom One (light)
  // //
  // wp_enqueue_style(
  //   'baum-atom-one-light',
  //   get_template_directory_uri() . '/css/atom-one-light.css',
  //   [],
  //   $version,
  //   'all'
  // );

  //
  // Highlight.js
  //
  wp_enqueue_script(
    'baum-highlight-js',
    get_template_directory_uri() . '/js/highlight.js',
    ['jquery'],
    '1.0',
    true
  );

  //
  // Syntax Highlighting: list desired programming languages
  //
  $comp_lang = [
    'apache',
    'bash',
    'css',
    'dockerfile',
    'graphql',
    'http',
    'javascript',
    'json',
    'makefile',
    'markdown',
    'nginx',
    'php',
    'plaintext',
    'python',
    'shell',
    'sql',
    'typescript',
    'yaml'
  ];

  //
  // Syntax Highlighting: enqueque each language in the array
  //
  foreach ($comp_lang as $lang) {
    wp_enqueue_script(
      'baum-highlight-language-' . $lang,
      get_template_directory_uri() . '/js/languages/' . $lang . '.js',
      ['baum-highlight-js'],
      '1.0',
      true
    );
  }

  //
  // Main Index JS
  //
  wp_enqueue_script(
    'baum-index-js',
    get_template_directory_uri() . '/js/index.js',
    ['baum-tooltips-js'],
    '1.0',
    true
  );

  $cat_ids = [];
  $cats = get_the_category();
  foreach ($cats as $cat) {
    $cat_ids[] = get_cat_ID($cat->cat_name);
  }

  //
  // Send data to frontend file
  //
  wp_localize_script(
    'baum-index-js',
    'wp_object',
    [
      'cat_ids'          => $cat_ids,
      'categories'       => $cats,
      'ajaxurl'          => admin_url('admin-ajax.php'),
      'is_logged_in'     => is_user_logged_in(),
      'tooltipster'      => [
        'animation'      => 'grow',
        'theme'          => 'tooltipster-borderless',
        'interactive'    => true,
        'contentAsHTML'  => true,
      ]
    ]
  );


  //
  // Comments JS
  //
  wp_enqueue_script(
    'baum-comments-js',
    get_template_directory_uri() . '/js/comments.js',
    ['baum-index-js'],
    '1.0',
    true
  );

  wp_enqueue_script('comment-reply');

  //
  // Baum's Apple Menu JS
  //
  wp_enqueue_script(
    'baum-apple-menu-script',
    get_template_directory_uri() . '/js/apple-menu.js',
    ['jquery'],
    null,
    true
  );

  //
  // Baum's Header Categories JS
  //
  wp_enqueue_script(
    'baum-header-categories-js',
    get_template_directory_uri() . '/js/baum-header-categories.js',
    ['jquery'],
    $version,
    true
  );

  //
  // Apple Menu Dropdowns JS (Notifications & Developer Tools)
  // Disabled - Apple menu system handles dropdown toggling
  //
  // wp_enqueue_script(
  //   'baum-menu-dropdowns-js',
  //   get_template_directory_uri() . '/js/baum-menu-dropdowns.js',
  //   ['jquery'],
  //   '1.0',
  //   true
  // );

  //
  // Notifications CSS
  //
  wp_enqueue_style(
    'baum-notifications-css',
    get_template_directory_uri() . '/css/style-notifications.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Developer Tools CSS
  //
  wp_enqueue_style(
    'baum-developer-tools-css',
    get_template_directory_uri() . '/css/style-developer-tools.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Header Categories Navigation JS
  //
  wp_enqueue_script(
    'baum-header-categories',
    get_template_directory_uri() . '/js/baum-header-categories.js',
    [],
    $version,
    true
  );

  //
  // Date Display JS
  //
  wp_enqueue_script(
    'baum-date-display',
    get_template_directory_uri() . '/js/baum-date-display.js',
    [],
    $version,
    true
  );

  //
  // Time Capsule Component JS
  //
  wp_enqueue_script(
    'baum-time-capsule',
    get_template_directory_uri() . '/js/baum-time-capsule.js',
    [],
    $version,
    true
  );
  //
  // Step 3 - enqueque the script
  //
  // wp_enqueue_script('baum-index-js');


  //
  // GitHub Syntax Highlighting (Dark)
  //
  // wp_enqueue_style(
  //   'baum-github-dark-css',
  //   get_template_directory_uri() . '/css/github-dark.css',
  //   array(),
  //   [],
  //   'all'
  // );

  //
  // GitHub Syntax Highlighting (Light)
  //
  // wp_enqueue_style(
  //   'baum-github-css',
  //   get_template_directory_uri() . '/css/github.css',
  //   [],
  //   $version,
  //   'all'
  // );

  //
  // Normalize CSS
  //
  wp_enqueue_style(
    'normalize-css',
    get_template_directory_uri() . '/css/normalize.css',
    [],
    $version,
    'all'
  );

  //
  // Skeleton CSS
  //
  wp_enqueue_style(
    'skeleton-css',
    get_template_directory_uri() . '/css/skeleton.css',
    ['normalize-css'],
    $version,
    'all'
  );

  //
  // Style Variables CSS
  //
  wp_enqueue_style(
    'style-vars-css',
    get_template_directory_uri() . '/style-baum-vars.css',
    ['skeleton-css'],
    $version,
    'all'
  );

  //
  // Style CSS
  //
  wp_enqueue_style(
    'style-css',
    get_template_directory_uri() . '/style.css',
    ['skeleton-css'],
    $version,
    'all'
  );

  //
  // WP Elements Stylesheet
  //
  wp_enqueue_style(
    'style-wp-css',
    get_template_directory_uri() . '/style-wp.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Third Party Integrations Stylesheet
  //
  wp_enqueue_style(
    'style-third-party-css',
    get_template_directory_uri() . '/style-third-party.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // AP News Inspired Stylesheet
  //
  if (get_theme_mod('baum_theme') == 'ap-news') {
    wp_enqueue_style(
      'style-theme-ap-news-css',
      get_template_directory_uri() . '/style-theme-ap-news.css',
      ['style-css'],
      $version,
      'all'
    );
  }

  //
  // GitHub Inspired Stylesheet
  //
  if (get_theme_mod('baum_theme') == 'github') {
    wp_enqueue_style(
      'style-theme-github-css',
      get_template_directory_uri() . '/style-theme-github.css',
      ['style-css'],
      $version,
      'all'
    );
  }

  //
  // Mobile Stylesheet
  //
  wp_enqueue_style(
    'style-mobile-css',
    get_template_directory_uri() . '/style-mobile.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Font Stylesheet
  //
  wp_enqueue_style(
    'style-font-css',
    get_template_directory_uri() . '/style-font.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Dark Mode Stylesheet
  //
  $darkmode = get_theme_mod('baum_dark_mode');
  if ($darkmode !== 'off') {
    wp_enqueue_style(
      'style-dark-css',
      get_template_directory_uri() . '/style-dark.css',
      ['style-css'],
      $version,
      'all'
    );
  }

  //
  // Baum's Main Stylesheet
  //
  wp_enqueue_style(
    'style-baum-css',
    get_template_directory_uri() . '/style-baum.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Video UI Stylesheet
  //
  wp_enqueue_style(
    'style-baum-video-css',
    get_template_directory_uri() . '/style-baum-video.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Colors Stylesheet
  //
  wp_enqueue_style(
    'style-baum-colors-css',
    get_template_directory_uri() . '/style-baum-colors.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's News Cards Stylesheet
  //
  wp_enqueue_style(
    'style-baum-cards-css',
    get_template_directory_uri() . '/style-baum-cards.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Skeleton Loaders Stylesheet
  //
  wp_enqueue_style(
    'baum-skeleton-loaders-css',
    get_template_directory_uri() . '/css/skeleton-loaders.css',
    ['style-css'],
    $version,
    'all'
  );

  //
  // Baum's Apple Menu Stylesheet
  //
  // wp_enqueue_style(
  //   'baum-apple-menu-style',
  //   get_template_directory_uri() . '/css/apple-menu.css'
  // );

  /////////////////////////////////////
  // Baum's Query Vars
  /////////////////////////////////////

  function baum_query_vars ($qvars) {
    $qvars[] = 'autoplay';
    return $qvars;
  }

  add_filter('query_vars', 'baum_query_vars');

  /////////////////////////////////////
  // CSS Color Variables
  /////////////////////////////////////


  $ico_style_str = '';
  $cat_array = get_categories();

  foreach ($cat_array as $cat) {
    $cat_id = $cat->term_id;
    $cat_ico = get_field('ico', 'category_' . $cat_id);

    if ($cat_ico) {
      $ico_style_str .= '.menu-item-' . $cat->term_id . '::before {';
      $ico_style_str .= 'background-image: url("' . $cat_ico . '");';
      $ico_style_str .= 'background-size: 12px;';
      $ico_style_str .= 'background-repeat: no-repeat;';
      $ico_style_str .= 'position: relative;';
      $ico_style_str .= 'z-index: 25;';
      $ico_style_str .= 'left: -4px;';
      $ico_style_str .= 'top: 0px;';
      $ico_style_str .= 'content: "";';
      $ico_style_str .= 'width: 20px !important;';
      $ico_style_str .= 'height: 12px;';
      $ico_style_str .= '}';
      $ico_style_str .= '.menu-item-' . $cat->term_id . ' {';
      $ico_style_str .= 'height: 26px;';
      $ico_style_str .= '}';
      $ico_style_str .= '.menu-item-' . $cat->term_id . ' a {';
      $ico_style_str .= 'line-height: 16px; position: relative; left: -8px; top: 0px;';
      $ico_style_str .= '}';
    }
  }

  $terms_array = get_terms([ 'taxonomy' => 'channel' ]);

  foreach ($terms_array as $channel) {
    $channel_id = $channel->term_id;
    $channel_ico = get_field('favicon', $channel);


    $url = get_field('url', $channel);
    $channel_ico = 'https://www.google.com/s2/favicons?domain='.$url.'&sz=128';

    if ($channel_ico) {
      $ico_style_str .= '.baum-channel-' . $channel->term_id . '::before,';
      $ico_style_str .= '.menu-item-' . $channel->term_id . '::before {';
      $ico_style_str .= 'background-image: url("' . $channel_ico . '");';
      $ico_style_str .= 'background-size: 12px;';
      $ico_style_str .= 'background-repeat: no-repeat;';
      $ico_style_str .= 'position: relative;';
      $ico_style_str .= 'z-index: 25;';
      $ico_style_str .= 'left: 0px;';
      $ico_style_str .= 'top: 0px;';
      $ico_style_str .= 'content: "";';
      $ico_style_str .= 'width: 18px !important;';
      $ico_style_str .= 'height: 11px;';
      $ico_style_str .= 'display: inline-block;';
      $ico_style_str .= '}';
      $ico_style_str .= '.menu-item-' . $channel->term_id . ' {';
      $ico_style_str .= 'height: 26px;';
      $ico_style_str .= '}';
      $ico_style_str .= '.menu-item-' . $channel->term_id . ' a {';
      $ico_style_str .= 'line-height: 16px; position: relative; left: -8px; top: -2px;';
      $ico_style_str .= '}';
    }
  }

  wp_register_style('baum-inline-ico-css', false);
  wp_enqueue_style('baum-inline-ico-css');
  wp_add_inline_style('baum-inline-ico-css', $ico_style_str);

  /////////////////////////////////////
  // Baum's Sign Out Redirect
  /////////////////////////////////////

  // function baum_redirect_signout () {
  //   wp_safe_redirect(home_url());
  //   exit;
  // }
  // add_action('wp_logout', 'baum_redirect_signout');

}

add_action('wp_enqueue_scripts', 'baum_enqueue_scripts');

/**
 * Add notifications dropdown to main menu
 *
 * This function adds a notifications dropdown with badge to the main menu
 * in a way that's compatible with the existing Apple menu system.
 *
 * @since 1.0.0
 */
function add_notifications_with_dropdown_to_menu() {
  // Get notifications and unread count
  $notifications = baum_get_notifications();
  $unread_count = baum_get_unread_notification_count();

  // Build notifications menu item HTML
  $notifications_item = '<li class="menu-item menu-item-notifications">';
  $notifications_item .= '<a href="#" class="notifications-trigger">';
  $notifications_item .= '<i class="fas fa-bell"></i>';
  // $notifications_item .= '<span>Notifications</span>';
  if ($unread_count > 0) {
    $notifications_item .= '<span class="notification-badge">' . esc_html($unread_count) . '</span>';
  }
  $notifications_item .= '</a>';

  $notifications_item .= '<ul class="sub-menu notifications-dropdown">';
  $notifications_item .= '<li class="notifications-header"><h4>Notifications</h4></li>';
  $notifications_item .= '<li class="notifications-list-container"><div class="notifications-list">';

  if (!empty($notifications)) {
    foreach ($notifications as $notification) {
      $notifications_item .= '<div class="notification-item" data-id="' . esc_attr($notification['id']) . '"';
      if ($notification['link']) {
        $notifications_item .= ' data-link="' . esc_attr($notification['link']) . '"';
      }
      $notifications_item .= '>';
      $notifications_item .= '<div class="notification-icon" style="background: ' . esc_attr($notification['icon_color']) . ';">';
      $notifications_item .= '<i class="' . esc_attr($notification['icon']) . '"></i></div>';
      $notifications_item .= '<div class="notification-content">';
      $notifications_item .= '<div class="notification-text">' . wp_kses_post($notification['text']) . '</div>';
      $notifications_item .= '<div class="notification-time">' . esc_html($notification['time']) . '</div>';
      $notifications_item .= '</div>';
      $notifications_item .= '<button class="notification-delete" onclick="this.parentElement.style.display=\'none\'; event.stopPropagation(); return false;" aria-label="Delete notification">';
      $notifications_item .= '<i class="fas fa-times"></i></button></div>';
    }
  } else {
    $notifications_item .= '<div class="no-notifications">No notifications</div>';
  }

  $notifications_item .= '</div></li>';
  $notifications_item .= '<li class="notifications-footer">';
  $notifications_item .= '<button onclick="var items = document.querySelectorAll(\'.notification-item\'); items.forEach(function(item) { item.style.display = \'none\'; }); event.stopPropagation(); return false;" class="clear-all-btn">Clear all notifications</button>';
  $notifications_item .= '</li></ul></li>';

  return $notifications_item;
}

/**
 * Add developer tools dropdown to main menu
 *
 * This function adds a developer tools dropdown to the main menu
 * in a way that's compatible with the existing Apple menu system.
 *
 * @since 1.0.0
 */
function add_developer_tools_with_dropdown_to_menu() {
  // Build developer tools menu item HTML
  $developer_item = '<li class="menu-item menu-item-developer-tools">';
  $developer_item .= '<a href="#" class="developer-trigger">';
  $developer_item .= '<i class="fas fa-code"></i>';
  // $developer_item .= '<span>Developers</span>';
  // $developer_item .= '<i class="fas fa-chevron-down"></i>';
  $developer_item .= '</a>';
  $developer_item .= '<ul class="sub-menu developer-dropdown">';
  $developer_item .= '<li class="developer-dropdown-content">';
  $developer_item .= baum_get_developer_tools_html();
  $developer_item .= '</li></ul></li>';

  return $developer_item;
}

/**
 * Add custom dropdowns to the right menu
 *
 * This function adds both notifications and developer tools dropdowns
 * to the main-menu-right location using a filter approach that's
 * compatible with the existing Apple menu system.
 *
 * @param string $items The menu items HTML
 * @param object $args The menu arguments
 * @return string Modified menu items HTML
 *
 * @since 1.0.0
 */
function baum_add_custom_dropdowns_to_menu($items, $args) {
  // Only add to the main-menu-right location
  if ($args->theme_location !== 'main-menu-right') {
    return $items;
  }

  // If no items exist, create an empty string to append to
  if (empty($items)) {
    $items = '';
  }

  // Add notifications dropdown
  $items .= add_notifications_with_dropdown_to_menu();

  // Add developer tools dropdown
  $items .= add_developer_tools_with_dropdown_to_menu();

  return $items;
}

add_filter('wp_nav_menu_items', 'baum_add_custom_dropdowns_to_menu', 10, 2);

/**
 * Get user notifications
 *
 * @return array Array of notification objects
 */
function baum_get_notifications() {
  // For demo purposes, return sample notifications
  // In a real implementation, this would fetch from database
  return [
    [
      'id' => 1,
      'icon' => 'fas fa-file-alt',
      'icon_color' => '#007cba',
      'text' => '<strong>New post published:</strong> "Advanced WordPress Development Techniques" is now live',
      'time' => '2 minutes ago',
      'link' => '/new-post',
      'read' => false
    ],
    [
      'id' => 2,
      'icon' => 'fas fa-check',
      'icon_color' => '#46b450',
      'text' => '<strong>Backup completed:</strong> Your site backup has been successfully created',
      'time' => '1 hour ago',
      'link' => null,
      'read' => false
    ],
    [
      'id' => 3,
      'icon' => 'fas fa-comment',
      'icon_color' => '#ffb900',
      'text' => '<strong>New comment:</strong> John Doe commented on "WordPress Security Best Practices"',
      'time' => '3 hours ago',
      'link' => '/comments',
      'read' => false
    ]
  ];
}

/**
 * Get unread notification count
 *
 * @return int Number of unread notifications
 */
function baum_get_unread_notification_count() {
  $notifications = baum_get_notifications();
  return count(array_filter($notifications, function($notification) {
    return !$notification['read'];
  }));
}

/**
 * Get developer tools HTML content
 *
 * @return string HTML content for developer tools dropdown
 */
function baum_get_developer_tools_html() {
  $dev_tools = baum_get_developer_tools();

  $html = '<div class="developer-grid">';

  // Left Column - Main Tools
  $html .= '<div class="developer-main-tools">';
  foreach ($dev_tools['main_tools'] as $tool) {
    $html .= '<div class="developer-tool-item">';
    $html .= '<a href="' . esc_url($tool['link']) . '" class="developer-tool-link">';
    $html .= '<div class="developer-tool-icon"><i class="' . esc_attr($tool['icon']) . '"></i></div>';
    $html .= '<div class="developer-tool-content">';
    $html .= '<div class="developer-tool-title">' . esc_html($tool['title']) . '</div>';
    $html .= '<div class="developer-tool-description">' . esc_html($tool['description']) . '</div>';
    $html .= '</div></a></div>';
  }
  $html .= '</div>';

  // Right Column - Modules
  $html .= '<div class="developer-modules">';
  foreach ($dev_tools['modules'] as $module) {
    $html .= '<div class="developer-module-item">';
    $html .= '<a href="' . esc_url($module['link']) . '" class="developer-module-link">';
    $html .= '<div class="developer-module-icon"><i class="' . esc_attr($module['icon']) . '"></i></div>';
    $html .= '<div class="developer-module-content">';
    $html .= '<div class="developer-module-title">' . esc_html($module['title']) . '</div>';
    $html .= '<div class="developer-module-description">' . esc_html($module['description']) . '</div>';
    $html .= '</div></a></div>';
  }
  $html .= '</div>';
  $html .= '</div>';

  return $html;
}

/**
 * Get developer tools configuration
 *
 * @return array Array of tool categories and items
 */
function baum_get_developer_tools() {
  return [
    'main_tools' => [
      [
        'title' => 'Database',
        'description' => 'Fully portable Postgres database',
        'icon' => 'fas fa-database',
        'link' => '/wp-admin/admin.php?page=database-tools'
      ],
      [
        'title' => 'Authentication',
        'description' => 'User Management out of the box',
        'icon' => 'fas fa-lock',
        'link' => '/wp-admin/users.php'
      ],
      [
        'title' => 'Storage',
        'description' => 'Serverless storage for any media',
        'icon' => 'fas fa-folder',
        'link' => '/wp-admin/upload.php'
      ],
      [
        'title' => 'Edge Functions',
        'description' => 'Deploy code globally on the edge',
        'icon' => 'fas fa-cog',
        'link' => '/wp-admin/admin.php?page=edge-functions'
      ],
      [
        'title' => 'Realtime',
        'description' => 'Synchronize and broadcast events',
        'icon' => 'fas fa-bolt',
        'link' => '/wp-admin/admin.php?page=realtime-tools'
      ]
    ],
    'modules' => [
      [
        'title' => 'Vector',
        'description' => 'AI toolkit to manage embeddings',
        'icon' => 'fas fa-vector-square',
        'link' => '/wp-admin/admin.php?page=vector-tools'
      ],
      [
        'title' => 'Cron',
        'description' => 'Schedule and manage recurring Jobs',
        'icon' => 'fas fa-clock',
        'link' => '/wp-admin/admin.php?page=cron-jobs'
      ],
      [
        'title' => 'Queues',
        'description' => 'Durable Message Queues with guaranteed delivery',
        'icon' => 'fas fa-list',
        'link' => '/wp-admin/admin.php?page=queue-management'
      ],
      [
        'title' => 'Features',
        'description' => 'Explore everything you can do with Supabase.',
        'icon' => 'fas fa-star',
        'link' => '/wp-admin/admin.php?page=feature-explorer'
      ]
    ],
    'customer_story' => [
      'company' => 'kayhan.space',
      'description' => 'Kayhan Space saw 8x improvement in developer speed when moving to Supabase'
    ],
    'compare_links' => [
      'Supabase vs Firebase',
      'Supabase vs Heroku Postgres',
      'Supabase vs Auth0'
    ],
    'solutions' => [
      'AI Builders'
    ]
  ];
}


add_filter('widget_update_callback', function($instance, $new_instance, $old_instance, $widget) {
  if (isset($_POST['acf'])) {
      foreach ($_POST['acf'] as $key => $value) {
          $field = acf_get_field($key);
          if ($field && isset($field['name'])) {
              $instance[$field['name']] = $value;
          }
      }
  }
  return $instance;
}, 10, 4);


add_filter('acf/load_value', function($value, $post_id, $field) {
  if (strpos($post_id, 'widget_') === 0 && is_array($GLOBALS['acf_widget_instance'] ?? null)) {
      $field_name = $field['name'];
      if (isset($GLOBALS['acf_widget_instance'][$field_name])) {
          return $GLOBALS['acf_widget_instance'][$field_name];
      }
  }
  return $value;
}, 10, 3);





// add_filter( 'heartbeat_send', 'custom_heartbeat_send_filter', 10, 2 );

// function custom_heartbeat_send_filter( $response, $data ) {
//     // Optional: Modify or monitor heartbeat data
//     return $response;
// }

// add_filter( 'heartbeat_settings', 'custom_heartbeat_settings' );

// function custom_heartbeat_settings( $settings ) {
//     // Change interval in seconds (default is 15)
//     $settings['interval'] = 60; // One ping per minute
//     return $settings;
// }


// function remove_bbp_keymaster_from_all_users() {
//   global $wpdb;
//   $capabilities_meta_key = $wpdb->prefix . 'capabilities';

//   // Get all users
//   $users = get_users();

//   foreach ( $users as $user ) {
//       // Get the user's capabilities array
//       $caps = get_user_meta( $user->ID, $capabilities_meta_key, true );

//       // If it's an array and has the bbp_keymaster key, remove it
//       if ( is_array( $caps ) && isset( $caps['bbp_keymaster'] ) ) {
//           unset( $caps['bbp_keymaster'] );
//           update_user_meta( $user->ID, $capabilities_meta_key, $caps );
//       }
//   }
// }
// // Hook the function to run in the admin area. Once you have run it and verified the changes, remove or comment out this code.
// add_action( 'admin_init', 'remove_bbp_keymaster_from_all_users' );


/////////////////////////////////////
// Apple / Baum Menu
/////////////////////////////////////

function baum_enqueue_apple_menu_styles () {
  // wp_enqueue_style(
  //   'baum-apple-menu-style',
  //   get_template_directory_uri() . '/css/apple-menu.css'
  // );
  wp_enqueue_script(
    'baum-apple-menu-script',
    get_template_directory_uri() . '/js/apple-menu.js',
    ['jquery'],
    null,
    true
  );
}

// add_action('wp_enqueue_scripts', 'baum_enqueue_apple_menu_styles');

/////////////////////////////////////
// Baum Test Cron Functionality
/////////////////////////////////////

// if (!wp_next_scheduled('baum_test_cron_hook')) {
//   wp_schedule_event(time(), 'hourly', 'baum_test_cron_hook');
// }

// add_action('baum_test_cron_hook', function () {
//   error_log('Baum Test Cron Hook Executed.');
// });

/////////////////////////////////////
// Baum's Sign Out localization triggers deletion of localStorage data
/////////////////////////////////////

// function baum_set_logout_flag ($arg) {
//   error_log('Logging out...' . $arg);
//   $user_id = get_current_user_id();
//   if ($user_id) {
//     update_user_meta($user_id, 'baum_logged_out', '1');
//     error_log('Logged Out flag set: ' . $user_id);
//   }
// }

// add_action('wp_logout', 'baum_set_logout_flag', 0, 1);

// function baum_clear_all_cache_and_transients() {
//   global $wpdb;

//   // Delete all transients
//   $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%'" );

//   // Delete all site transients (for multisite setups)
//   $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_site_transient_%'" );

//   // Optionally delete object cache keys (if you're using an external object cache like Redis or Memcached)
//   if ( function_exists( 'wp_cache_flush' ) ) {
//       wp_cache_flush(); // Clear object cache
//   }

//   // Delete any caching plugin leftovers
//   delete_option( 'wp_cache' ); // WP Super Cache
//   delete_option( 'w3tc_config' ); // W3 Total Cache
//   delete_option( 'advanced_cache' );
//   delete_option( 'autoptimize_css' );
//   delete_option( 'autoptimize_js' );
//   delete_option( 'autoptimize_html' );

//   echo "All transients, caches, and related options have been flushed!";
// }

// Run it once
// baum_clear_all_cache_and_transients();


function baum_enqueue_and_localize_script () {
  //
  // wp_script_is(): Checks the status of the script. The key statuses to check are:
  //
  // 'enqueued' (true when the script is enqueued)
  // 'registered' (true when the script is registered but not necessarily enqueued)
  // 'done' (true when the script has already been printed to the page)
  // 'to_do' (true when the script is queued for output)
  //
  // Check if 'baum-index-js' has been enqueued successfully



  // if (wp_script_is('baum-index-js', 'enqueued') || wp_script_is('baum-index-js', 'to_do')) {
  //   // Get the current user ID
  //   $user_id = get_current_user_id();
  //   $logged_out = get_user_meta($user_id, 'baum_logged_out', true);

  //   if ($logged_out) {
  //     error_log('Logged out: ' . $user_id);
  //     // Localize the script with logout true
  //     wp_localize_script('baum-index-js', 'wp_object', [
  //       'wp_logout'      => true,
  //       'ajaxurl'        => admin_url('admin-ajax.php'),
  //       'tooltipster'    => [
  //         'animation'      => 'grow',
  //         'theme'          => 'tooltipster-borderless',
  //         'interactive'    => true,
  //         'contentAsHTML'  => true,
  //       ],
  //     ]);
  //     delete_user_meta($user_id, 'baum_logged_out'); // Clear the flag after use
  //   } else {
  //     error_log('Not logged out: ' . $user_id);
  //     // Localize the script normally with logout false
  //     wp_localize_script('baum-index-js', 'wp_object', [
  //       'wp_logout'      => false,
  //       'ajaxurl'        => admin_url('admin-ajax.php'),
  //       'tooltipster'    => [
  //         'animation'      => 'grow',
  //         'theme'          => 'tooltipster-borderless',
  //         'interactive'    => true,
  //         'contentAsHTML'  => true,
  //       ],
  //     ]);
  //   }
  // }


  // else if (wp_script_is('baum-index-js', 'registered')) {
  //   error_log('Enqueque script: baum-index-js');
  //   wp_enqueue_script('baum-index-js');
  // }
  // else if (wp_script_is('baum-index-js', 'done')) {
  //   error_log('Error! the baum-index-js script got done before we could localize wp_logout');
  // }

  // wp_enqueue_script(
  //   'baum-index-js',
  //   get_template_directory_uri() . '/js/index.js',
  //   ['baum-tooltips-js'],
  //   '1.0',
  //   true,
  //   // true
  // );
  // Always enqueue the script as it might be used independently of logout action
  // wp_enqueue_script('baum-index-js', 'path/to/baum-index.js', array('jquery'), null, true);

  // Check if the current user has the logout flag set
  // $user_id = get_current_user_id();
  // $logged_out = get_user_meta($user_id, 'baum_logged_out', true);

  // if ($logged_out) {
  //   // Localize the script with logout true
  //   wp_localize_script('baum-index-js', 'wp_object', ['wp_logout' => true]);
  //   delete_user_meta($user_id, 'baum_logged_out'); // Clear the flag after use
  // } else {
  //   // Localize normally
  //   wp_localize_script('baum-index-js', 'wp_object', ['wp_logout' => false]);
  // }
}

add_action('wp_enqueue_scripts', 'baum_enqueue_and_localize_script');


  // function baum_localize_wp_object ($arg) {
  //   error_log('localize baum-index-js');
  //   print_r($arg);
  //   wp_localize_script('baum-index', 'wp_object', ['wp_logout' => true]);
  // }

  // add_action('wp_logout', 'baum_localize_wp_object', 10, 1);


  // error_log('add_action wp_logout');



// if (is_admin()) {
  require_once get_template_directory() . '/functions-admin.php';
// }

require_once get_template_directory() . '/functions-ajax.php';
require_once get_template_directory() . '/functions-blocks.php';
require_once get_template_directory() . '/functions-comments.php';
require_once get_template_directory() . '/functions-gamipress.php';
require_once get_template_directory() . '/functions-images.php';
require_once get_template_directory() . '/functions-notifications.php';
require_once get_template_directory() . '/functions-pages.php';
require_once get_template_directory() . '/functions-shortcodes.php';
require_once get_template_directory() . '/functions-widgets.php';

/**
 * Enqueue WordPress block editor packages for frontend editor page
 *
 * This function loads all the necessary WordPress packages to make
 * the block editor work on the frontend editor page template.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enqueue_frontend_gutenberg_packages() {
  // Only load on the editor page template
  if (!is_page_template('page-editor-gutenberg.php')) {
    return;
  }

  // Core WordPress packages needed for block editor
  $packages = array(
    'wp-element',
    'wp-components',
    'wp-blocks',
    'wp-block-editor',
    'wp-data',
    'wp-dom-ready',
    'wp-block-library',
    'wp-format-library',
    'wp-editor',
    'wp-api-fetch',
    'wp-url',
    'wp-html-entities',
    'wp-i18n',
    'wp-hooks',
    'wp-media-utils',
    'wp-notices',
    'wp-keycodes',
    'wp-rich-text',
    'wp-compose'
  );

  // Enqueue all packages
  foreach ($packages as $package) {
    wp_enqueue_script($package);
  }

  // Enqueue WordPress media scripts (needed for image uploads)
  wp_enqueue_media();
  wp_enqueue_script('media-upload');
  wp_enqueue_script('media-views');
  wp_enqueue_script('media-editor');
  wp_enqueue_script('media-audiovideo');

  // Enqueue block editor styles
  wp_enqueue_style('wp-edit-blocks');
  wp_enqueue_style('wp-block-library');
  wp_enqueue_style('wp-block-library-theme');
  wp_enqueue_style('wp-components');
  wp_enqueue_style('wp-editor');

  // Editor settings with media support
  $settings = array(
    'alignWide' => true,
    'allowedMimeTypes' => get_allowed_mime_types(),
    'bodyPlaceholder' => __('Start writing or type / to choose a block'),
    'titlePlaceholder' => __('Add title'),
    'isRTL' => is_rtl(),
    'maxWidth' => 580,
    'styles' => array(
      array(
        'css' => 'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }'
      )
    ),
    'defaultBlock' => array(
      'name' => 'core/paragraph',
      'attributes' => array()
    ),
    'supportsLayout' => true,
    'supportsTemplateMode' => false,
    // Media upload settings
    'mediaUpload' => true,
    'allowedBlockTypes' => true,
    'hasFixedToolbar' => false,
    'focusMode' => false,
    'hasPermissionsToManageWidgets' => current_user_can('edit_theme_options'),
    'imageSizes' => wp_get_additional_image_sizes(),
    'maxUploadFileSize' => wp_max_upload_size(),
    // REST API settings for media
    'restNonce' => wp_create_nonce('wp_rest'),
    'restUrl' => rest_url(),
  );

  // Inline editor settings and block registration
  wp_add_inline_script(
    'wp-dom-ready',
    'document.addEventListener("DOMContentLoaded", function() {
      window.baumEditorSettings = ' . wp_json_encode($settings) . ';

      // Wait for WordPress packages to be available
      function initializeBlocks() {
        if (typeof wp === "undefined" || !wp.blockLibrary || !wp.domReady) {
          console.log("Waiting for WordPress packages...");
          setTimeout(initializeBlocks, 100);
          return;
        }

        wp.domReady(function() {
          if (wp.blockLibrary && wp.blockLibrary.registerCoreBlocks) {
            wp.blockLibrary.registerCoreBlocks();
            console.log("WordPress core blocks registered for frontend editor");
          } else {
            console.error("wp.blockLibrary.registerCoreBlocks not available");
          }
        });
      }

      initializeBlocks();
    });',
    'after'
  );
}
add_action('wp_enqueue_scripts', 'baum_enqueue_frontend_gutenberg_packages');


function baum_add_roles () {
  // Administrator role is built into WordPress by default
  // Editor role is built into WordPress by default
  // Author role is built into WordPress by default
  // Contributor role is built into WordPress by default
  // Subscriber role is built into WordPress by default
  // Influencer must be created
  // Marketer role must be created
  // Reader role must be created

  //
  // Add Custom Baum Press Roles
  //

  if (get_option('baum_roles_version') < 2) {

    //
    // `influencer` role is a custom role by Baum Press
    //

    add_role(
      'influencer',
      __('Influencer'),
      [
        'read'         => true,
        'comment'      => true,
        'edit_posts'   => false,
        'upload_files' => true,
      ]
    );

    //
    // `marketer` role is a custom role by Baum Press
    //

    add_role(
      'marketer',
      __('Marketer'),
      [
        'read'         => true,
        'comment'      => true,
        'edit_posts'   => false,
        'upload_files' => true,
      ]
    );

    //
    // `reader` role is a custom role by Baum Press
    //

    add_role(
      'reader',
      __('Reader'),
      [
        'read'         => true,
        'comment'      => true,
        'edit_posts'   => false,
        'upload_files' => true,
      ]
    );
    update_option('baum_roles_version', 2);
  }
}

add_action('init', 'baum_add_roles');

/**
 * Renames WordPress default user roles to more publication-appropriate names
 *
 * This function customizes the display names of WordPress user roles to better
 * fit a publishing or news site context. It changes 'Administrator' to 'Chief Editor'
 * and 'Author' to 'Staff Writer', while keeping other roles with their original names.
 *
 * @return void
 *
 * @since 1.0.0
 */
function rename_wordpress_roles() {
  global $wp_roles;

  // Rename roles
  if (isset($wp_roles->roles['administrator'])) {
      $wp_roles->roles['administrator']['name'] = 'Chief Editor';
      $wp_roles->role_names['administrator'] = 'Chief Editor';
  }
  if (isset($wp_roles->roles['editor'])) {
      $wp_roles->roles['editor']['name'] = 'Editor';
      $wp_roles->role_names['editor'] = 'Editor';
  }
  if (isset($wp_roles->roles['author'])) {
      $wp_roles->roles['author']['name'] = 'Staff Writer';
      $wp_roles->role_names['author'] = 'Staff Writer';
  }
  if (isset($wp_roles->roles['contributor'])) {
      $wp_roles->roles['contributor']['name'] = 'Contributor';
      $wp_roles->role_names['contributor'] = 'Contributor';
  }
  if (isset($wp_roles->roles['subscriber'])) {
      $wp_roles->roles['subscriber']['name'] = 'Site Member';
      $wp_roles->role_names['subscriber'] = 'Site Member';
  }
}

add_action('init', 'rename_wordpress_roles');


/**
 * Adds the site icon to the end of the last paragraph in the content.
 *
 * This searches for the last occurrence of a closing paragraph tag `</p>`
 * within the content and appends the site icon just before it. If the content
 * does not contain any paragraph tags, the content is returned unchanged.
 *
 * The site icon is displayed with inline styles for alignment and spacing.
 * The function is hooked into the 'the_content' filter and is applied to
 * singular posts/pages during the main query.
 *
 * @param string $the_content The original content of the post.
 * @return string Modified content with site icon appended to last paragraph.
 *
 * @hook the_content
 */

function add_site_icon_to_the_content ($the_content) {
    global $page, $numpages;

    // Check if we're on the last page of a paginated post
    if ($page !== $numpages) {
      return $the_content; // If not the last page, return content unaltered
    }

  $css = '
    background:none;
    display:inline-block;
    height:20px;
    width:auto;
    vertical-align:text-bottom;
    margin-left: 2.5px;
    margin-right: 2.5px;
  ';

  if (is_singular() && is_main_query()) {
    // Search for the last occurrence of </p> in the content
    $p_end_pos = strrpos($the_content, '</p>');

    if ($p_end_pos !== false) {
      // Split the content at the position of the last </p>
      $before_last_p = substr($the_content, 0, $p_end_pos);
      $after_last_p = substr($the_content, $p_end_pos);

      // Add the site icon before the final </p>
      $site_icon = '<img style="' . $css
        . '" src="' . get_site_icon_url()
        . '" alt="' . esc_attr(get_bloginfo('name', 'display'))
        . '" title="' . esc_attr(get_bloginfo('name', 'display')) . '">';

      // Rebuild the content with the icon inside the last paragraph
      $the_content = $before_last_p . $site_icon . $after_last_p;
    }
  }

  return $the_content;
}

add_filter('the_content', 'add_site_icon_to_the_content');






// function add_site_icon_to_the_content ($the_content) {
//   $css = 'background:none;display:inline-block;height:22px;width:auto;vertical-align:text-bottom;margin-left:5px;margin-right:5px;';
//   if (is_singular() && is_main_query()) {
//     $str = rtrim($the_content); // trimn white space from end of the content
//     $p_end = substr($str, -4); // grab the last 4 chars of the content
//     if ($p_end == '</p>') { // if the chars are </p>
//       $str = substr($str, 0, -4); // remove the </p> at the end of the content

//       // add the site icon inline inside the final paragraph
//       $str = $str . '<img style="' . $css . '" src="' . get_site_icon_url()
//         . '" alt="' . esc_attr(get_bloginfo('name', 'display'))
//         . '" title="' . esc_attr(get_bloginfo('name', 'display')) . '">';
//       $str = $str . '</p>'; // add the final </p> back to the content
//       return $str;
//     }
//   }
//   return $the_content;
// }

// add_filter('the_content', 'add_site_icon_to_the_content');







// function baum_remove_default_comment_textarea() {
//   remove_meta_box('commentstatusdiv', 'comment', 'normal');
// }
// add_action('add_meta_boxes_comment', 'baum_remove_default_comment_textarea');



// function debug_edit_comment_form_hooks() {
//   global $wp_filter;
//   if (isset($wp_filter['edit_comment_form'])) {
//       var_dump($wp_filter['edit_comment_form']);
//   }
// }
// add_action('admin_footer', 'debug_edit_comment_form_hooks');


// /////////////////////////////////////
// // Order Pages By Date in Admin
// /////////////////////////////////////

// function set_post_order_in_admin ($wp_query) {
//   global $pagenow;
//   if (is_admin() && 'edit.php' == $pagenow && !isset($_GET['orderby'])) {
//     $wp_query->set('orderby', 'date');
//     $wp_query->set('order', 'DESC');
//   }
// }

// add_filter('pre_get_posts', 'set_post_order_in_admin');

// apply_filters('activate_tinymce_for_media_description', true);

// //
// // Add Datetime and Screen ID to Adminbar
// //

// function baum_add_datetime_to_adminbar (WP_Admin_Bar $wp_admin_bar) {
//   $screen = get_current_screen();
//   $parent_slug = 'adminbar-date-time';
//   $local_time  = date('F j, Y g:i a', current_time('timestamp', 0));
//   $wp_admin_bar->add_menu([
//     'id'     => $parent_slug,
//     'parent' => 'top-secondary',
//     'group'  => null,
//     'title'  => $screen->id . ' | ' . $local_time,
//     'href'   => admin_url('/options-general.php'),
//   ]);
// }

// add_action('admin_bar_menu', 'baum_add_datetime_to_adminbar');


//
// Notify the editor of admin color scheme
//

// $current_user_id = get_current_user_id();
// if ($current_user_id) {
//   $admin_color_scheme = get_user_option('admin_color', $current_user_id);
//   baum_notify_admin('Current Admin Color Scheme:', $admin_color_scheme);
// }

function baum_register_playlist_taxonomy() {
    register_taxonomy('playlist', 'attachment', [
        'label'        => 'Playlists',
        'rewrite'      => ['slug' => 'playlist'],
        'hierarchical' => false,
        'show_admin_column' => true, // Shows count in admin
        'update_count_callback' => '_update_generic_term_count', // Important for media
    ]);
}
// add_action('init', 'baum_register_playlist_taxonomy');


/////////////////////////////////////
// Add Tags to Media Library
/////////////////////////////////////

function add_tags_to_media_library () {
  register_taxonomy_for_object_type('post_tag', 'attachment');
}

add_action('init', 'add_tags_to_media_library');

/////////////////////////////////////
// Change the base URL of the author archives to /profile
/////////////////////////////////////

function baum_author_base () {
  global $wp_rewrite;
  $wp_rewrite->author_base = 'profile';
  $wp_rewrite->flush_rules();
}

add_action('init', 'baum_author_base');

/////////////////////////////////////
// Add Author Comment type
/////////////////////////////////////

// function register_author_comment_type () {
//   // Register a custom comment type
//   register_meta('comment', 'author_comment', [
//     'type' => 'string',
//     'description' => 'Comment about an author',
//     'single' => true,
//     'show_in_rest' => true,
//   ]);
// }

// add_action('init', 'register_author_comment_type');

// function handle_author_comment_submission () {
//   if (!is_user_logged_in() || empty($_POST['comment_content']) || empty($_POST['author_id'])) {
//       wp_die('Invalid comment submission.');
//   }

//   $author_id = absint($_POST['author_id']);
//   $comment_content = sanitize_textarea_field($_POST['comment_content']);
//   $user = wp_get_current_user();

//   // Insert comment
//   $comment_id = wp_insert_comment([
//       'comment_post_ID' => 0, // Not tied to any specific post
//       'comment_content' => $comment_content,
//       'user_id' => $user->ID,
//       'comment_author' => $user->display_name,
//       'comment_author_email' => $user->user_email,
//       'comment_approved' => 1,
//       'comment_type' => 'comment',
//   ]);

//   if ($comment_id) {
//       // Add author ID metadata to associate comment with the author
//       add_comment_meta($comment_id, 'author_comment', $author_id);
//   }

//   wp_redirect(get_author_posts_url($author_id) . '?comment_success=true');
//   exit;
// }

// add_action('admin_post_submit_author_comment', 'handle_author_comment_submission');

// add_action('admin_post_nopriv_submit_author_comment', 'handle_author_comment_submission');


// function register_wall_post_type() {
//   $args = array(
//       'label' => 'Wall',
//       'public' => true,
//       'supports' => array('title', 'editor', 'author', 'comments'),
//       'rewrite' => array('slug' => 'wall'),
//   );
//   register_post_type('wall', $args);
// }

// add_action('init', 'register_wall_post_type');







































// function assign_existing_authors_to_taxonomy() {
//   $users = get_users();

//   foreach ($users as $user) {
//       $user_id = $user->ID;
//       $username = $user->user_login;

//       // Check if the term exists
//       $term = get_term_by('name', $username, 'contributor');
//       if (!$term) {
//           wp_insert_term($username, 'contributor', [
//               'slug' => 'author-' . $user_id,
//           ]);
//       }
//   }
// }

// add_action('init', 'assign_existing_authors_to_taxonomy');

/**
 * Repairs existing posts by assigning them to the correct Home Timeline term
 *
 * This function queries all published posts of specific types and ensures they
 * are properly assigned to the Home Timeline taxonomy term corresponding to
 * their author. It's useful for fixing posts created before the Home Timeline
 * feature was implemented or after data migration.
 *
 * @return void
 *
 * @since 1.0.0
 */
function repair_home_timelines_existing_posts () {
  $post_types = ['story', 'activity'];

  $query = new WP_Query([
      'post_type'      => $post_types,
      'post_status'    => 'publish',
      'posts_per_page' => -1, // Fetch all posts
  ]);

  if ($query->have_posts()) {
      while ($query->have_posts()) {
          $query->the_post();

          $post_id = get_the_ID();
          $post = get_post($post_id);
          $author_id = $post->post_author;

          // Trigger the automatic assignment function
          assign_to_home_timeline($post_id, $post, false);
      }
  }

  wp_reset_postdata();
}

/**
 * Repairs Home Timelines taxonomy by creating terms for all users
 *
 * This function ensures that every registered user has a corresponding term
 * in the Home Timeline taxonomy. It checks for existing terms and creates
 * new ones as needed, which is essential for the proper functioning of
 * the timeline feature.
 *
 * @return void
 *
 * @since 1.0.0
 */
function repair_home_timelines_taxonomy() {
  $users = get_users();

  foreach ($users as $user) {
      $user_id = $user->ID;
      $username = $user->user_login;

      $taxonomy = 'home-timeline';

      // Check if the term already exists for this user
      $term = get_term_by('name', $username, $taxonomy);

      if (!$term) {
          // Create the term if it doesn't exist
          wp_insert_term($username, $taxonomy, [
              'slug' => 'user-' . $user_id,
          ]);
      }
  }
}

//
// Repair Timeline setup
//

// add_action('init', 'repair_home_timelines_existing_posts');
// add_action('init', 'repair_home_timelines_taxonomy');


// /**
//  * Schedule a repair event if not already scheduled.
//  */
// function schedule_home_timelines_repair() {
//   if (!wp_next_scheduled('repair_home_timelines_event')) {
//       wp_schedule_event(time(), 'daily', 'repair_home_timelines_event');
//   }
// }
// add_action('init', 'schedule_home_timelines_repair');

// /**
// * Hook for the repair event to ensure taxonomy accuracy.
// */
// add_action('repair_home_timelines_event', 'repair_home_timelines_taxonomy');


/**
 * Automatically assigns posts to the author's Home Timeline term
 *
 * This function is triggered when a post is saved and assigns it to a taxonomy
 * term corresponding to the post author's username. It creates the term if it
 * doesn't exist, which allows for filtering content by author in the Home Timeline
 * feature.
 *
 * @param int     $post_id The ID of the post being saved
 * @param WP_Post $post The post object
 * @param bool    $update Whether this is an existing post being updated
 * @return void
 *
 * @since 1.0.0
 */

function assign_to_home_timeline ($post_id, $post, $update) {
  // Avoid autosave and unnecessary hooks
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
      return;
  }

  // Only proceed for specific post types
  $post_types = ['story', 'activity'];
  if (!in_array($post->post_type, $post_types)) {
      return;
  }

  // Get the post author's user info
  $user_id = $post->post_author;
  $user = get_user_by('ID', $user_id);

  if (!$user) {
      return;
  }

  $taxonomy = 'home-timeline';
  $username = $user->user_login;

  // Find or create the term for the author
  $term = get_term_by('name', $username, $taxonomy);
  if (!$term) {
      $term_data = wp_insert_term($username, $taxonomy, [
          'slug' => 'user-' . $user_id,
      ]);
      $term_id = $term_data['term_id'];
  } else {
      $term_id = $term->term_id;
  }

  // Assign the term to the post
  wp_set_post_terms($post_id, [$term_id], $taxonomy, false);
}

add_action('save_post', 'assign_to_home_timeline', 10, 3);

/**
 * Removes Home Timelines taxonomy meta box from the post editor screen
 *
 * This function hides the Home Timeline taxonomy meta box from the post editor
 * to prevent users from manually assigning posts to timeline terms. This ensures
 * that posts are only assigned to the correct author's timeline automatically.
 *
 * @return void
 *
 * @since 1.0.0
 */
function remove_home_timelines_meta_box () {
  $post_types = ['story', 'activity'];

  foreach ($post_types as $post_type) {
      remove_meta_box('tagsdiv-home-timeline', $post_type, 'side');
  }
}

add_action('admin_menu', 'remove_home_timelines_meta_box');


/**
 * Create a term in the "Home Timelines" taxonomy for new users.
 *
 * @param int $user_id The ID of the newly registered user.
 */
add_action('user_register', function ($user_id) {
  // Retrieve the user data
  $user = get_user_by('ID', $user_id);
  $username = $user->user_login;

  $taxonomy = 'home-timeline';

  // Check if a term with the user's name already exists
  $term = get_term_by('name', $username, $taxonomy);

  // Create a new term if it doesn't already exist
  if (!$term) {
      wp_insert_term($username, $taxonomy, [
          'slug' => 'user-' . $user_id, // Use a unique slug based on the user ID
      ]);
  }
});


/**
 * Generate the Home Timelines query.
 *
 * @param int $user_id The user ID.
 * @return array Array of feed items sorted by date.
 */

function get_home_timelines_feed($user_id) {
  $following_ids = get_user_following_ids($user_id);

  if (empty($following_ids)) {
      return []; // No content to show if the user isn't following anything.
  }
  $paged = get_query_var('paged');
  $paged = ($paged) ? $paged : 1;
  global $do_not_duplicate;

  $query_args = [
    'post__not_in'  => $do_not_duplicate,
    'paged'          => $paged,
    'post_type'      => ['post', 'story'], // Add your post types
    'posts_per_page' => 10, // Limit the number of posts
    'orderby'        => 'date', // Order by latest
    'order'          => 'DESC',
    // 'author__in'     => $following_ids,
  ];

  $query_args['tax_query'] = [
    'relation' => "OR",
    [
      'taxonomy' => 'home-timeline',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'story-timeline',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'story-collection',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'person',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'contributor',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'category',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ],
    [
      'taxonomy' => 'channel',
      'field' => 'term_id',
      'terms' => $following_ids,
      'operator' => 'IN'
    ]
  ];

  $posts_query = new WP_Query($query_args);

  // // Add comments the user is following.
  // $comments_query = get_comments([
  //     'post_id__in'    => $following_ids, // Comments on followed threads.
  //     'status'         => 'approve',
  //     'number'         => 20,
  //     'orderby'        => 'comment_date',
  //     'order'          => 'DESC',
  // ]);

  // Prepare a unified feed.
  $feed_items = [];

  // Add posts to the feed.
  if ($posts_query->have_posts()) {
      foreach ($posts_query->posts as $post) {
          $feed_items[] = [
              'type'  => 'post',
              'data'  => $post,
              'date'  => strtotime($post->post_date),
          ];
      }
  }

  // // Add comments to the feed.
  // if (!empty($comments_query)) {
  //     foreach ($comments_query as $comment) {
  //         $feed_items[] = [
  //             'type'  => 'comment',
  //             'data'  => $comment,
  //             'date'  => strtotime($comment->comment_date),
  //         ];
  //     }
  // }

  // // Sort feed items by date (descending).
  // usort($feed_items, function ($a, $b) {
  //     return $b['date'] <=> $a['date'];
  // });

  return $feed_items;
}

// /**
//  * Assign posts of type "Story" or "Activity" to the author's "Home Timelines" term.
//  *
//  * @param int     $post_id The ID of the post being saved.
//  * @param WP_Post $post The post object being saved.
//  * @param bool    $update Whether this is an update (true) or a new post (false).
//  */
// add_action('save_post', function ($post_id, $post, $update) {
//   // Avoid autosave actions
//   if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
//       return;
//   }

//   // Define the post types to process
//   $post_types = ['story', 'activity'];
//   if (!in_array($post->post_type, $post_types)) {
//       return;
//   }

//   // Get the post author
//   $user_id = $post->post_author;
//   $user = get_user_by('ID', $user_id);

//   if (!$user) {
//       return; // Bail out if the author cannot be found
//   }

//   $taxonomy = 'home_timelines';
//   $username = $user->user_login;

//   // Find or create the user's term in the taxonomy
//   $term = get_term_by('name', $username, $taxonomy);
//   if (!$term) {
//       $term_data = wp_insert_term($username, $taxonomy, [
//           'slug' => 'user-' . $user_id, // Use a unique slug
//       ]);
//       $term_id = $term_data['term_id'];
//   } else {
//       $term_id = $term->term_id;
//   }

//   // Assign the term to the post
//   wp_set_post_terms($post_id, [$term_id], $taxonomy, false);
// }, 10, 3);

// /**
//  * Retrieve posts associated with a specific user's "Home Timelines" term.
//  *
//  * @param int $user_id The ID of the user whose timeline posts should be retrieved.
//  * @return WP_Post[] Array of post objects associated with the user's timeline term.
//  */
// function get_user_timeline_posts($user_id) {
//   $taxonomy = 'home_timelines';
//   $username = get_user_by('ID', $user_id)->user_login;

//   // Find the term for the user
//   $term = get_term_by('name', $username, $taxonomy);

//   if (!$term) {
//       return []; // Return an empty array if the term doesn't exist
//   }

//   // Query posts associated with the user's term
//   $query = new WP_Query([
//       'post_type' => ['story', 'activity'], // Post types to include
//       'tax_query' => [
//           [
//               'taxonomy' => $taxonomy,
//               'field'    => 'slug',
//               'terms'    => $term->slug, // Filter by the user's term
//           ],
//       ],
//   ]);

//   return $query->posts;
// }











// AJAX functions have been moved to functions-ajax.php










// function notify_my_mail( $comment_id, $comment_approved ) {
// 	if ( ! $comment_approved ) {
// 		$comment = get_comment( $comment_id );
// 		$mail = '<EMAIL>';
// 		$subject = sprintf( 'New Comment by: %s', $comment->comment_author );
// 		$message = $comment->comment_content;

// 		wp_mail( $mail, $subject, $message );
// 	}
// }
// add_action( 'comment_post', 'notify_my_mail', 10, 2 );


// function handle_wall_comment_submission() {
//   // Check if it's our custom comment submission
//   if (isset($_POST['baum_wall_comment_submission']) && $_POST['baum_wall_comment_submission'] === '1') {
//       // Sanitize and validate comment data
//       $comment_content = sanitize_text_field($_POST['comment']);
//       $comment_post_ID = intval($_POST['comment_post_ID']);
//       $user_id = get_current_user_id();

//       if (empty($comment_content) || !$user_id) {
//           wp_die('Error: Comment content or user ID is missing.');
//       }

//       // Set up the comment data
//       $commentdata = array(
//           'comment_post_ID' => $comment_post_ID, // This should be set to the author wall post ID
//           'comment_content' => $comment_content,
//           'user_id'         => $user_id,
//           'comment_author'  => wp_get_current_user()->display_name,
//           'comment_author_email' => wp_get_current_user()->user_email,
//       );

//       // Insert the comment into the database
//       $comment_id = wp_insert_comment($commentdata);

//       if (is_wp_error($comment_id) || !$comment_id) {
//           wp_die('Failed to post comment.');
//       } else {
//           wp_redirect(get_permalink($user_id)); // Redirect back to the wall post or another page
//           exit;
//       }
//   }
// }

// add_action('init', 'handle_wall_comment_submission');

/**
 * Detect if a comment has nested replies and count them.
 *
 * @param WP_Comment $comment The comment object.
 * @return int The number of nested replies. Returns 0 if none exist.
 */

// function count_nested_comment_replies ($comment) {
//   if (!($comment instanceof WP_Comment)) {
//       return 0; // Ensure $comment is a valid WP_Comment object
//   }

//   // Fetch child comments for this comment
//   $replies = get_comments([
//       'parent'       => $comment->comment_ID,
//       'post_id'      => $comment->comment_post_ID,
//       'status'       => 'approve', // Only count approved comments
//       'count'        => true, // Return the count instead of the full comments array
//   ]);

//   return (int) $replies; // Cast to integer for clarity
// }



//
// Create an Iframely Embed at the end of the comment for each URL
//

// function iframely_comment_embed ($comment_content) {
//   preg_match('/\bhttps?:\/\/\S+/i', $comment_content, $urls);
//   // error_log(print_r($urls));
//   if ($urls) {
//     $embed_url = $urls[0];
//     $embed_html = '<div class="iframely-embed" data-url="' . esc_url($embed_url) . '"></div>';
//     $comment_content .= $embed_html;
//     error_log('iframely_comment_embed: ' . $embed_url);
//   }
//   return $comment_content;
// }

// add_filter('comment_text', 'iframely_comment_embed');

// function iframely_comment_embed($comment_content) {
//   // Match URLs that are not within HTML tags
//   preg_match_all('~(?<!\w)(https?:\/\/[^\s<]+)(?![^<>]*>)~i', $comment_content, $matches);

//   if (!empty($matches[1])) {
//       foreach ($matches[1] as $embed_url) {
//           $embed_html = '<div class="iframely-embed" data-url="' . esc_url($embed_url) . '"></div>';
//           // Replace the plain URL with the embed HTML
//           $comment_content = str_replace($embed_url, $embed_html, $comment_content);
//           error_log('iframely_comment_embed: ' . $embed_url);
//       }
//   }

//   return $comment_content;
// }


// function iframely_comment_embed($comment_content) {
//   // Temporarily disable wpautop to prevent interference
//   remove_filter('comment_text', 'wpautop');

//   // Replace plain URLs with Iframely embed
//   $processed_content = preg_replace_callback(
//       '~(?<!\w)(https?:\/\/[^\s<]+)(?![^<>]*>)~i',
//       function ($matches) {
//           $url = $matches[1];
//           return '<div class="iframely-embed" data-url="' . esc_url($url) . '"></div>';
//       },
//       $comment_content
//   );

//   // Re-enable wpautop for subsequent filters
//   add_filter('comment_text', 'wpautop');

//   // Return the processed content
//   return $processed_content;
// }

// add_filter('comment_text', 'iframely_comment_embed');







// add_filter('comment_text', 'iframely_comment_embed');


// function baum_load_nested_comments() {
//     $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
//     $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

//     if (!$parent_id || !$post_id) {
//         wp_send_json_error(['message' => 'Invalid parent ID or post ID']);
//     }

//     $comments = get_comments([
//         'parent' => $parent_id,
//         'post_id' => $post_id,
//         'status' => 'approve',
//     ]);

//     if (empty($comments)) {
//         wp_send_json_success('<p>No replies yet.</p>');
//     }

//     ob_start();
//     wp_list_comments([
//         'walker' => new Baum_Comment_Walker(),
//         'style' => 'div',
//         'max_depth' => 3, // Adjust as necessary
//     ], $comments);
//     $html = ob_get_clean();

//     wp_send_json_success($html);
// }


// function baum_load_nested_comments() {
//     $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
//     $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
//     $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;
//     $number = 2; // The number of replies to load per request

//     if (!$parent_id || !$post_id) {
//         wp_send_json_error(['message' => 'Invalid parent ID or post ID']);
//     }

//     $comments = get_comments([
//         'parent' => $parent_id,
//         'post_id' => $post_id,
//         'status' => 'approve',
//         'number' => $number,
//         'offset' => $offset,
//     ]);

//     if (empty($comments)) {
//         wp_send_json_error(['message' => 'No more replies']);
//     }

//     ob_start();
//     wp_list_comments([
//         'walker' => new Baum_Comment_Walker(),
//         'style' => 'div',
//     ], $comments);
//     $html = ob_get_clean();

//     wp_send_json_success(['html' => $html, 'next_offset' => $offset + $number]);
// }


// function baum_load_nested_comments() {
//   $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
//   $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

//   if (!$parent_id || !$post_id) {
//       wp_send_json_error(['message' => 'Invalid parent ID or post ID']);
//   }

//   // Get only the child comments that haven't been loaded yet
//   $comments = get_comments([
//       'parent' => $parent_id,
//       'post_id' => $post_id,
//       'status' => 'approve',
//   ]);

//   if (empty($comments)) {
//       wp_send_json_success('<p>No more replies.</p>');
//   }

//   ob_start();
//   wp_list_comments([
//       'walker' => new Baum_Comment_Walker(),
//       'style' => 'div',
//   ], $comments);
//   $html = ob_get_clean();

//   wp_send_json_success($html);
// }



/////////////////////////////////////
// Get all commenters for a post
/////////////////////////////////////

// function load_commenters() {
//   if (!is_user_logged_in()) {
//     wp_send_json_error('Not authorized');
//   }

//   $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
//   $commenters = get_comments([
//     'post_id' => $post_id,
//     'status' => 'approve',
//     'type' => 'comment',
//   ]);

//   $usernames = [];
//   foreach ($commenters as $comment) {
//     $user = get_userdata($comment->user_id);
//     if ($user && !in_array($user->display_name, $usernames)) {
//       $usernames[] = [
//         'display_name' => $user->display_name,
//         'user_id' => $user->ID,
//       ];
//     }
//   }

//   wp_send_json($usernames);
// }

// add_action('wp_ajax_load_commenters', 'load_commenters');





/**
 * Gets the display name of a user's highest privilege role
 *
 * This function determines which role a user has with the most capabilities
 * and returns its display name. This is useful for showing a user's primary
 * role when they have multiple roles assigned.
 *
 * @param int $user_id The ID of the user
 * @return string|null The display name of the highest role, or null if user not found
 *
 * @since 1.0.0
 */
function get_highest_role_display_name ($user_id) {
  // Get the user object
  $user = get_user_by('ID', absint($user_id));

  // Check if the user exists and has roles
  if (!$user || empty($user->roles)) {
    return null;
  }

  // Get all registered roles
  global $wp_roles;
  $all_roles = $wp_roles->roles;

  // Sort user roles by the number of capabilities they provide, descending
  usort($user->roles, function ($a, $b) use ($all_roles) {
      return count($all_roles[$b]['capabilities']) <=> count($all_roles[$a]['capabilities']);
  });

  // Get the display name of the role with the most capabilities
  $highest_role = $user->roles[0];
  return isset($all_roles[$highest_role]['name']) ? $all_roles[$highest_role]['name'] : null;
}

/**
 * Shortcode to display a user's highest role
 *
 * This shortcode allows displaying a user's highest privilege role name
 * anywhere in post content or widgets.
 *
 * @param array $atts Shortcode attributes containing user_id
 * @return string The display name of the user's highest role
 *
 * @since 1.0.0
 *
 * @example [baum_role user_id="123"]
 */
function get_highest_role_shortcode ($atts) {
  $atts = shortcode_atts([
    'user_id' => '0',
  ], $atts, 'baum_role');

  return get_highest_role_display_name($atts['user_id']);
}

add_shortcode('baum_role', 'get_highest_role_shortcode');

/**
 * Removes SearchWP plugin's default CSS styles
 *
 * This function dequeues the SearchWP live search CSS to prevent conflicts
 * with the theme's custom search styling. It runs with priority 20 to ensure
 * it executes after the plugin enqueues its styles.
 *
 * @return void
 *
 * @since 1.0.0
 */
function remove_searchwp_css () {
	wp_dequeue_style('searchwp-live-search');
}

add_action('wp_enqueue_scripts', 'remove_searchwp_css', 20);

/////////////////////////////////////
// Enqueque Custom Admin Area CSS
/////////////////////////////////////

// function load_custom_wp_admin_style () {
//   wp_register_style(
//     'custom_wp_admin_css',
//     get_bloginfo('stylesheet_directory') . '/css/spectre.css',
//     false,
//     '1.0.0'
//   );
//   wp_enqueue_style( 'custom_wp_admin_css' );
// }

// add_action('admin_enqueue_scripts', 'load_custom_wp_admin_style');

//
// Increase search queries posts per page to 100
//

/**
 * Increases the number of posts per page for search results
 *
 * This function modifies the main WordPress query to show 100 posts per page
 * on search result pages, allowing users to see more results at once without
 * having to navigate through multiple pages.
 *
 * @param WP_Query $query The WordPress query object
 * @return void
 *
 * @since 1.0.0
 */
function baum_increase_posts_per_page ($query) {
  if (!is_admin() && $query->is_main_query()) {
    if ($query->is_search) {
      $query->set('posts_per_page', 100);
    }
  }
}

add_action('pre_get_posts', 'baum_increase_posts_per_page');

/**
 * Orders search results by post type and date
 *
 * This function modifies the search query ordering to prioritize results
 * by post type first (descending), then by publication date (newest first).
 * This ensures that certain post types appear before others in search results.
 *
 * @param string $orderby The current ORDER BY clause
 * @return string Modified ORDER BY clause for search queries
 *
 * @since 1.0.0
 */
function baum_search_orderby ($orderby) {
  if (!is_admin() && is_main_query() && is_search()) {
    $orderby = 'post_type DESC, post_date DESC';
  }
  return $orderby;
}

add_filter('posts_orderby', 'baum_search_orderby');

/**
 * Instantly redirects link format posts based on ACF settings
 *
 * This function checks if a post has the 'link' format and instant redirection
 * is enabled via ACF fields. If so, it immediately redirects the user to the
 * specified link URL using JavaScript.
 *
 * @return void Either redirects and dies, or returns normally
 *
 * @since 1.0.0
 */
function baum_instant_redirect_content_type_link () {
  global $post;

  // Check if instant redirection is enabled
  $instant_redirection = get_field('instant_redirection', $post);
  if ($instant_redirection !== 'true') return;

  // Only redirect on single link format posts, not in admin
  if (get_post_format($post->ID) == 'link' && !is_admin() && is_single($post->ID)) {
    $link = get_field('link', $post);
    if (!$link) return;

    // Use JavaScript redirect and terminate execution
    echo '<script>window.location="' . esc_url($link) . '";</script>';
    die();
  }
}

add_action('the_post', 'baum_instant_redirect_content_type_link');

//
// Yoast SEO - "An error occurred loading the Yoast SEO primary taxonomy picker"
//
// https://stackoverflow.com/questions/57106206/an-error-occurred-loading-the-yoast-seo-primary-taxonomy-picker
//

// add_filter( 'wpseo_primary_term_taxonomies', '__return_empty_array' );

//
// Add ID of story to the content so Browsing History works
//
// TODO: This hack should be handled with the function built-in to wordpress
//

/**
 * Adds JavaScript to track post views for browsing history
 *
 * This function appends a script to the post content that sets the current
 * post ID in a JavaScript variable. This allows the browsing history
 * functionality to track which posts the user has viewed.
 *
 * @param string $content The post content
 * @return string Modified content with the history tracking script
 *
 * @since 1.0.0
 */
function baum_history_add_story_id ($content) {
  $custom_content = '<script>'
  . 'window.baum = window.baum || {};'
  . 'window.baum.story_id = ' . get_the_ID() . ';'
  .'</script>';
  $content .= $custom_content;
  return $content;
}

add_filter('the_content', 'baum_history_add_story_id');

//
// Pass variables to frontend script for Browsing History
//

// function baum_enqueue_history_script () {
//   $action_get = 'baum_history_get';
//   wp_localize_script('baum-history', 'baum_history',
//     [
//       'api' => admin_url('admin-ajax.php'),
//       'action' => $action_get,
//       'nonce' => wp_create_nonce($action_get)
//     ]
//   );
// }

// add_action('wp_enqueue_scripts', 'baum_enqueue_history_script');


//
// Get the last 100 items from Browsing History
//

// get_baum_history function has been moved to functions-ajax.php

// function enqueue_baum_bookmarks_script () {
//   wp_enqueue_script(
//     'baum-bookmarks-js',
//     get_template_directory_uri() . '/js/baum-bookmarks.js',
//     ['jquery'],
//     null,
//     true
//   );

//   wp_localize_script('baum-bookmarks-js', 'baum_bookmarks', [
//     'ajax_url' => admin_url('admin-ajax.php'),
//     'nonce' => wp_create_nonce('get_baum_bookmarks'),
//     'story_id' => get_the_ID(),
//     'is_page' => is_page_template('page-starred.php'),
//     'icon' => get_theme_mod('baum_bookmark_icon', 'star')
//   ]);
// }

// add_action('wp_enqueue_scripts', 'enqueue_baum_bookmarks_script');

//
// Bookmarks
//
// Get the last 100 items bookmarked
//

// function add_baum_bookmarks_nonce () {
//   // This will output the nonce field in the footer
//   echo '<input id="baum_bookmarks_nonce" type="hidden" name="baum_bookmarks_nonce" value="' . wp_create_nonce('get_baum_bookmarks') . '">';
// }

// add_action('wp_footer', 'add_baum_bookmarks_nonce');

// function get_baum_bookmarks () {
//   if (isset($_POST['nonce'])) {
//     $verify = wp_verify_nonce($_POST['nonce'], 'get_baum_bookmarks');
//   } else {
//     $verify = 0;
//   }

//   // print_r($verify);

//   // Check if the nonce is set and valid
//   if (!$verify) {
//     wp_die(__('Nonce verification ' . $verify, 'baum'), '', [
//       'response' => 403
//     ]);
//   }

//   // Retrieve and sanitize the bookmarks from POST
//   $bookmarks = isset($_POST['bookmarks'])
//     ? sanitize_text_field($_POST['bookmarks']) : '';

//   if (!empty($bookmarks)) {
//     // Convert to an array of integers
//     $bookmarks = array_map('intval', explode(',', $bookmarks));
//     $atts = [
//       'size' => 'small',
//       'txtcolor' => 'standard',
//       'bgcolor' => 'standard',
//       'fav_btn' => true,
//     ];
//     echo "<div id='baum-bookmarks' "
//       . "class='baum-cards baum-cards-" . esc_attr($atts['size']) . "'>";
//     $the_query = new WP_Query([
//       'post_type' => 'post',
//       'posts_per_page' => 100,
//       'post__in' => $bookmarks,
//       'orderby' => 'post__in',
//     ]);
//     while ($the_query->have_posts()) {
//       $the_query->the_post();
//       get_template_part('parts/baum', 'card', $atts);
//     }
//     wp_reset_postdata();
//     echo '</div>';
//   }
//   wp_die(); // this is required to return a proper result
// }

// add_action('wp_ajax_action_get_baum_bookmarks', 'get_baum_bookmarks');
// add_action('wp_ajax_nopriv_action_get_baum_bookmarks', 'get_baum_bookmarks');


//
// Get the YouTube thumbnail and set it as the featured image
//

// function baum_get_youtube_thumbnail ($post_id) {

//   // Check if post has featured image
//   if (has_post_thumbnail($post_id)) {
//     error_log('Post ID {$post_id}: Already has a featured image.');
//     return;
//   }

//   // Check if a featured image is set manually
//   if (get_post_meta($post_id, '_thumbnail_id', true)) return;

//   // Check if this is an autosave or a revision
//   if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
//   if (wp_is_post_revision($post_id)) return;

//   // Check if the YouTube Video ID ACF field is set
//   $youtube_id = get_field('youtube_id', $post_id);
//   if (!$youtube_id) return;

//   // Add Featured Image to Post
//   $image_url = 'https://img.youtube.com/vi/' . $youtube_id . '/maxresdefault.jpg';
//   $image_name = $youtube_id . '.jpg';
//   $upload_dir = wp_upload_dir(); // Set upload folder
//   $image_data = file_get_contents($image_url); // Get image data
//   $unique_file_name = wp_unique_filename($upload_dir['path'], $image_name);
//   // Generate unique name
//   $filename = basename( $unique_file_name ); // Create image file name

//   // Check folder permission and define file location
//   if (wp_mkdir_p($upload_dir['path'])) {
//     $file = $upload_dir['path'] . '/' . $filename;
//   } else {
//     $file = $upload_dir['basedir'] . '/' . $filename;
//   }

//   // Create the image  file on the server
//   file_put_contents($file, $image_data);

//   // Check image file type
//   $wp_filetype = wp_check_filetype($filename, null);

//   // Set attachment data
//   $attachment = [
//     'post_mime_type' => $wp_filetype['type'],
//     'post_title' => sanitize_file_name($filename),
//     'post_content' => '',
//     'post_status' => 'inherit'
//   ];

//   // Create the attachment
//   $attach_id = wp_insert_attachment($attachment, $file, $post_id);

//   // Include image.php
//   require_once(ABSPATH . 'wp-admin/includes/image.php');

//   // Define attachment metadata
//   $attach_data = wp_generate_attachment_metadata($attach_id, $file);

//   // Assign metadata to attachment
//   wp_update_attachment_metadata($attach_id, $attach_data);

//   // And finally assign featured image to post
//   set_post_thumbnail($post_id, $attach_id);
// }

// add_filter('save_post', 'baum_get_youtube_thumbnail', 20);

//
// Download Upload Youtube Audio
//

/**
 * Processes YouTube URLs to extract audio
 *
 * This function takes a YouTube URL from an ACF field, converts the video
 * to audio using an external script, and saves the resulting audio URL
 * to another ACF field. It's useful for creating podcast or audio versions
 * of YouTube content.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 */
function process_youtube_url ($post_id) {
  // Check if this is an autosave or a revision
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
  if (wp_is_post_revision($post_id)) return;

  // Check if the YouTube URL ACF field is set
  $youtube_url = get_field('youtube_url', $post_id);
  if (!$youtube_url) return;

  // Convert YouTube video to audio and upload to S3
  $audio_url = convert_youtube_to_audio($youtube_url);

  if ($audio_url) {
      // Save the audio URL to a custom field
      update_field('audio_file_url', $audio_url, $post_id);

      // Optionally, use the audio file URL for further processing (e.g., transcription)
  }
}

// add_action('save_post', 'process_youtube_url');

//
// Use external PHP script to convert YouTube video link to audio
//

/**
 * Converts a YouTube video to audio using an external script
 *
 * This function calls an external PHP script that downloads a YouTube video
 * and extracts its audio track. The script is expected to return the URL
 * of the extracted audio file, which this function parses and returns.
 *
 * @param string $youtube_url The URL of the YouTube video
 * @return string The URL of the extracted audio file, or empty string on failure
 *
 * @since 1.0.0
 */
function convert_youtube_to_audio ($youtube_url) {
  $output = shell_exec("php /path/to/your/script.php '$youtube_url'");
  if (strpos($output, 'Audio URL:') !== false) {
      return trim(str_replace('Audio URL:', '', $output));
  }
  return '';
}

//
// ACF field for the ID of a youtube video
// query an API to dictate the youtube video into plaintext
// and insert it into the content of the post
//

// Create an ACF (Advanced Custom Fields) field for the YouTube video ID.
// Add custom code to query an API that converts the YouTube video to text.
// Insert the text into the post content.
// Here’s a step-by-step guide, including the necessary code snippets:

// Step 1: Create an ACF Field
// Install and activate the ACF plugin if you haven't already.
// Create a new field group (e.g., "YouTube Video ID") and add a field:
// Field Label: YouTube Video ID
// Field Name: youtube_video_id
// Field Type: Text
// Step 2: Add Custom Code to Your Theme’s Functions.php
// WordPress hooks process the YouTube video ID,
// query the transcription API, and update the post content

/**
 * Processes YouTube video ID to add transcription to post content
 *
 * This function checks if a post has a YouTube video ID and transcription
 * is enabled, then automatically transcribes the video and appends the
 * transcription to the post content. Only runs if the post has no existing content.
 *
 * @param int $post_id The ID of the post being processed
 * @return void
 *
 * @since 1.0.0
 */
function process_youtube_video_id ($post_id) {

  // Check if post has content - don't overwrite existing content
  $post_content = get_post_field('post_content', $post_id);
  if (strlen($post_content)) return;

  // Check if this is an autosave or a revision
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
  if (wp_is_post_revision($post_id)) return;

  // Check if the YouTube Video ID ACF field is set
  $youtube_id = get_field('youtube_id', $post_id);
  if (!$youtube_id) return;

  // Only transcribe if explicitly enabled
  if (get_field('transcribe_video_on_save', $post_id) == 'true') {

    // Transcribe the YouTube video using an external API (e.g., AssemblyAI, Google Cloud Speech-to-Text, etc.)
    $transcription = get_youtube_transcription($youtube_id);

    if ($transcription) {
      // Get the current post content
      $post_content = get_post_field('post_content', $post_id);

      // Append the transcription to the post content
      $post_content .= "\n\n" . '<h2>Video Transcription</h2>' . "\n" . wp_kses_post($transcription);

      // Update the post content
      wp_update_post(array(
        'ID' => $post_id,
        'post_content' => $post_content
      ));
    }
  }
}

// Hook into the save_post action to process the YouTube video ID when the post is saved
// add_action('save_post', 'process_youtube_video_id');

/**
 * Transcribes a YouTube video using AssemblyAI API
 *
 * This function takes a YouTube video ID, submits it to AssemblyAI for transcription,
 * and polls the API until the transcription is complete. It handles the full workflow
 * from submission to completion, including error handling and status polling.
 *
 * @param string $video_id The YouTube video ID to transcribe
 * @return string The transcribed text, or an error message if transcription fails
 *
 * @since 1.0.0
 */
function get_youtube_transcription($video_id) {
  global $YOUR_ASSEMBLYAI_API_KEY;
  $api_key = $YOUR_ASSEMBLYAI_API_KEY;

  // Validate video ID
  if (empty($video_id)) {
    return 'Invalid video ID provided.';
  }

  $video_url = 'https://www.youtube.com/watch?v=' . sanitize_text_field($video_id);

  // Submit the video URL to AssemblyAI for transcription
  $upload_url = 'https://api.assemblyai.com/v2/transcript';
  $response = wp_remote_post($upload_url, array(
    'body' => json_encode(array('audio_url' => $video_url)),
    'headers' => array(
      'Authorization' => $api_key,
      'Content-Type' => 'application/json'
    ),
    'timeout' => 30,
  ));

  if (is_wp_error($response)) {
    return 'Transcription not available.';
  }

  $body = wp_remote_retrieve_body($response);
  $data = json_decode($body, true);

  if (!isset($data['id'])) {
    return 'Failed to submit transcription request.';
  }

  $transcript_id = sanitize_text_field($data['id']);

  // Poll the AssemblyAI API to get the transcription result
  $transcript_url = 'https://api.assemblyai.com/v2/transcript/' . $transcript_id;
  $max_attempts = 120; // Maximum 10 minutes of polling (120 * 5 seconds)
  $attempts = 0;

  while ($attempts < $max_attempts) {
    $response = wp_remote_get($transcript_url, array(
      'headers' => array(
        'Authorization' => $api_key
      ),
      'timeout' => 30,
    ));

    if (is_wp_error($response)) {
      return 'Failed to retrieve transcript.';
    }

    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);

    if ($data['status'] === 'completed') {
      return sanitize_textarea_field($data['text']);
    }

    if ($data['status'] === 'failed') {
      return 'Transcription failed.';
    }

    // Wait for a few seconds before checking again
    sleep(5);
    $attempts++;
  }

  return 'Transcription timed out.';
}

/**
 * Gets the MIME type of a file from its URL
 *
 * This function determines the MIME type of a file based on its URL extension.
 * It uses WordPress's built-in wp_check_filetype function and provides a
 * fallback MIME type if the file type cannot be determined.
 *
 * @param string $url The URL of the file to check
 * @param string $fallback The fallback MIME type to use if detection fails
 * @return string The MIME type of the file or the fallback value
 *
 * @since 1.0.0
 */
function get_mime_type ($url, $fallback = 'video/mp4') {
  $file_info = wp_check_filetype(esc_url_raw($url));
  return $file_info['type'] ?: $fallback; // Fallback to 'video/mp4'
}

/**
 * Renders a menu of items the user is following (deprecated)
 *
 * This function was used to generate a dynamic menu of categories or terms
 * that a user is following. The function is mostly commented out and appears
 * to be deprecated in favor of newer following functionality.
 *
 * @return void Outputs HTML for the following menu
 *
 * @since 1.0.0
 * @deprecated This function is deprecated and may be removed in future versions
 */
function get_following_menu () {
  // 	$baum_follows = $_POST['baum_follows'];
  //   if (isset($baum_follows) && strlen($baum_follows)) {
  //     $user_following = explode(',', $baum_follows);
    // }

  //   // $domain = get_option('siteurl');
  //   // $domain = str_replace('http://', '', $domain);
  //   // $domain = str_replace('www', '', $domain);
  //   // if (isset($_COOKIE["baum_follows"]) && strlen($_COOKIE["baum_follows"])) {
  //   //   $user_following = explode(',', $_COOKIE["baum_follows"]);
  //   // }

  // foreach ($user_following as $term_id) {
  //     $cat = get_term($term_id);
  //     $following_menu_obj[] = array(
  //       'ID' => $cat->term_id,
  //       'title' => $cat->name,
  //       'url' => $cat->slug,
  //       'object' => 'category',
  //       'object_id' => $cat->term_id,
  //       'type' => 'taxonomy',
  //       'class' => ''
  //     );
  //     $term = get_queried_object(); ?>
    <li id="menu-item-<?php echo $cat->term_id; ?>" class="<?php echo ($term->slug == $cat->slug) ? 'current-menu-item' : ''; ?> <?php echo get_field('icon', 'category_' . $term_id); ?> menu-item menu-item-type-taxonomy menu-item-object-post_cat menu-item-<?php echo $cat->term_id; ?>">
      <a href="<?php echo get_category_link($cat); ?>">
        <?php echo $cat->name; ?>
      </a>
    </li>
  <?php // }
  // die();
}

// add_action('wp_ajax_action_get_following_menu', 'get_following_menu');
// add_action('wp_ajax_nopriv_action_get_following_menu', 'get_following_menu');

/**
 * Gets the IDs of items the user is following
 *
 * This function retrieves the list of IDs that a user is following,
 * either from user meta or from a cookie. It's used for the "following"
 * functionality that allows users to track specific content.
 *
 * @param int $user_id The user ID
 * @return array The array of IDs the user is following
 *
 * @since 1.0.0
 */
function get_user_following_ids ($user_id) {
  // This is a placeholder. Replace this with your actual logic for fetching followed IDs.
  // $following_ids = get_user_meta($user_id, 'following_ids', true);

  if (isset($_COOKIE['baum_follows']) && strlen($_COOKIE['baum_follows']))
    $following_ids = explode(',', $_COOKIE['baum_follows']);
  else
    $following_ids = [];

  return is_array($following_ids) ? $following_ids : [];
}

/////////////////////////////////////
// Return the User's Dashboard URL
/////////////////////////////////////

// function dashboard_url () {
//   $current_user = wp_get_current_user();
//   if ($current_user->ID == 0) return '/';
//   return '/dashboard';
// }

/////////////////////////////////////
// Return the User's Profile URL
/////////////////////////////////////

// function get_profile_url ($id) {
//   return get_the_author_meta('url', $id);
// }

/////////////////////////////////////
// Return the User's Edit Profile URL
/////////////////////////////////////

// get_edit_profile_url($user_id)
// function edit_profile_url () {
//   $current_user = wp_get_current_user();
//   if ($current_user->ID == 0) return '/';
//   return '/wp-admin/profile.php';
// }

/**
 * Adds the selected font class to the body tag
 *
 * This function retrieves the font selection from the theme customizer
 * and adds it as a CSS class to the body tag. This allows the theme
 * to apply different font families based on user selection.
 *
 * @param array $classes Array of existing body classes
 * @return array Modified array of body classes with font class added
 *
 * @since 1.0.0
 */
function baum_body_class ($classes) {
  $baum_font = get_theme_mod('baum_font');
  if ($baum_font) {
    $classes[] = sanitize_html_class($baum_font);
  }
  return $classes;
}

add_filter('body_class', 'baum_body_class');

/**
 * Registers post tags for use with pages
 *
 * This function enables the post_tag taxonomy for pages, allowing pages
 * to be tagged just like posts. This extends WordPress's default behavior
 * where tags are only available for posts.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_register_tags_for_pages () {
  // register_taxonomy_for_object_type('category', 'page');
  register_taxonomy_for_object_type('post_tag', 'page');
}

add_action('init', 'baum_register_tags_for_pages');

/////////////////////////////////////
// Query Additional Post Types in Archives of Tags and Categories
/////////////////////////////////////

/**
 * Modifies tag archive queries to include pages
 *
 * This function extends the default WordPress tag archives to include
 * pages in addition to posts. This allows pages to be tagged and displayed
 * in tag archives alongside regular posts.
 *
 * @param WP_Query $query The WordPress query object
 * @return void
 *
 * @since 1.0.0
 */
function baum_query_pages_for_tags ($query) {
  if (is_admin() || ! $query->is_main_query()) {
    return;
  }
  // if ($query->is_category && $query->is_main_query()) {
  //   $query->set('post_type', [ 'post', 'page' ]);
  // }
  if ($query->is_tag && $query->is_main_query()) {
    $query->set('post_type', [ 'post', 'page' ]);
  }
}

add_action('pre_get_posts', 'baum_query_pages_for_tags');

/**
 * Returns an array of active categories for the current context
 *
 * This function determines which categories are relevant to the current page.
 * On category archive pages, it returns the current category. On single posts,
 * it returns all categories assigned to the post.
 *
 * @return array Array of category objects, or empty array if none found
 *
 * @since 1.0.0
 */
function get_current_categories() {
  if (is_category()) {
    return [get_queried_object()];
  } elseif (is_single()) {
    return get_the_category();
  }
  return [];
}

/**
 * Fixes pagination query string issues
 *
 * This function ensures that the 'paged' query variable is properly set
 * for the main query, which helps resolve pagination issues that can
 * occur with custom queries or theme modifications.
 *
 * @param WP_Query $query The WordPress query object
 * @return void
 *
 * @since 1.0.0
 */
function baum_fix_pagination_query_string ($query) {
  if ($query->is_main_query() && !$query->is_feed() && !is_admin()) {
    $query->set('paged', get_query_var('paged'));
  }
}

add_action('pre_get_posts', 'baum_fix_pagination_query_string');

/////////////////////////////////////
// Update default media icons
/////////////////////////////////////

//
// Get the path to the icon directory
//

// function wpdocs_theme_icon_directory ($icon_dir) {
// 	return get_stylesheet_directory() . '/images';
// }

// add_filter('icon_dir', 'wpdocs_theme_icon_directory');

//
// Get the URI of the icon directory
//

// function wpdocs_theme_icon_uri ($icon_dir) {
// 	return get_stylesheet_directory_uri() . '/images';
// }

// add_filter('icon_dir_uri', 'wpdocs_theme_icon_uri');

/**
 * Generates custom pagination links
 *
 * This function creates a custom pagination interface with numbered pages,
 * previous/next links, and first/last page links. It provides more control
 * over pagination display than WordPress's default pagination.
 *
 * @param string $pages Total number of pages (auto-detected if empty)
 * @param int $range Number of page links to show on each side of current page
 * @return void Outputs HTML pagination links
 *
 * @since 1.0.0
 */
function pagination ($pages = '', $range = 4) {
  global $paged;
  $items = ($range * 2) + 1;
  if (empty($paged)) $paged = 1;

  if ($pages == '') {
    global $wp_query;
    $pages = $wp_query->max_num_pages;
    if (!$pages) $pages = 1;
  }

  if (1 != $pages) {
    echo '<div class="pagination" style="display:none;">';
    echo '<span> ' . absint($paged) . ' of ' . absint($pages) . '</span>';

    if ($paged > 2 && $paged > $range + 1 && $items < $pages)
      echo '<a href="' . esc_url(get_pagenum_link(1)) . '">&laquo;</a>';

    if ($paged > 1 && $items < $pages)
      echo '<a href="' . esc_url(get_pagenum_link($paged - 1)) . '">&lsaquo;</a>';

    for ($i=1; $i <= $pages; $i++) {
      if (1 != $pages && (!($i >= $paged + $range+1 || $i <= $paged - $range - 1) || $pages <= $items)) {
        if ($paged == $i) {
          echo '<span class="current">' . absint($i) . '</span>';
        } else {
          echo '<a href="' . esc_url(get_pagenum_link($i)) . '" class="inactive">' . absint($i) . '</a>';
        }
      }
    }

    if ($paged < $pages && $items < $pages)
      echo '<a href="' . esc_url(get_pagenum_link($paged + 1)) . '">&rsaquo;</a>';

    if ($paged < $pages - 1 && $paged + $range - 1 < $pages && $items < $pages)
      echo '<a href="' . esc_url(get_pagenum_link($pages)) . '">&raquo;</a>';

    echo '</div>';
  }
}

/**
 * Fixes the document title for home pages
 *
 * This function customizes the document title for home pages by combining
 * the site name and description in a specific format. It helps ensure
 * consistent and SEO-friendly titles for the home page.
 *
 * @param string $title The current document title
 * @return string|null Modified title for home pages, or null for other pages
 *
 * @since 1.0.0
 */
function baum_fix_home_title ($title) {
  if ((is_home() && !is_front_page()) || (!is_home() && is_front_page()) || !isset($title)) {
    $baum_home_title = get_bloginfo('name', 'display');
    $baum_home_desc = get_bloginfo('description', 'display');
    return '[' . esc_html($baum_home_title) . '] ' . esc_html($baum_home_desc);
  }
  return null;
}

add_filter('pre_get_document_title', 'baum_fix_home_title');

//
//
//

/**
 * Checks if the current page is the first page of content
 *
 * This function determines whether the current page being viewed is the first
 * page, either of a paginated post or an archive/category/tag page. It's useful
 * for conditionally displaying content only on the first page.
 *
 * @return bool True if it's the first page, false otherwise
 *
 * @since 1.0.0
 */
function is_first_page () {
  global $page, $numpages;

  // Check if it's a paginated single post (multipage post)
  if (is_singular() && ($page == 0 || $page == 1)) {
    return true;
  }

  // Check if it's an archive or paginated page and if it's on page 1
  if (!is_singular() && (get_query_var('paged') == 0 || get_query_var('paged') == 1)) {
    return true;
  }

  return false;
}

/**
 * Checks if the current page is the last page of content
 *
 * This function determines whether the current page being viewed is the last
 * page, either of a paginated post or an archive/category/tag page. It's useful
 * for conditionally displaying content only on the last page.
 *
 * @return bool True if it's the last page, false otherwise
 *
 * @since 1.0.0
 */
function is_last_page () {
  global $wp_query, $page, $numpages;

  // Check if it's a paginated single post
  if (is_singular()) {
    return $page >= $numpages;
  }

  // Check if it's a paginated archive (categories, search results, etc.)
  if ($wp_query->max_num_pages > 1) {
    $current_page = max(1, get_query_var('paged', 1));
    return $current_page >= $wp_query->max_num_pages;
  }

  return true; // If no pagination, it's always the last page.
}

/**
 * Gets the current page number
 *
 * This function returns the current page number for both paginated posts
 * and paginated archives. It handles the different query variables used
 * for different types of pagination.
 *
 * @return int The current page number (minimum 1)
 *
 * @since 1.0.0
 */
function get_current_page () {
  if (is_singular()) {
    return max(1, get_query_var('page', 1)); // Handles paginated posts
  }

  return max(1, get_query_var('paged', 1)); // Handles paginated archives
}

/**
 * Displays a random call-to-action message at the end of content
 *
 * This function shows a randomly selected message at the end of posts or pages
 * to encourage user engagement. It includes different message types that can
 * trigger different actions like sharing, commenting, or viewing related content.
 *
 * @return void Outputs HTML for the end message and related content
 *
 * @since 1.0.0
 */
function display_random_end_message () {
  $messages = [
      "Want more? Check out related posts below!" => 'related',
      "Like what you read? Share this with friends!" => 'share',
      // "Subscribe to get more content like this!" => 'subscribe',
      "Leave a comment and let us know what you think!" => 'comment',
      // "Help us keep the lights on—your support means everything!" => 'donate'
  ];

  // Pick a random message
  $random_key = array_rand($messages);
  $random_message = $random_key;
  $message_type = $messages[$random_key];

  echo "<div style='width:100%; height:20px;'></div>";
  echo "<div class='baum-page-end'>";

  // Display the message
  echo '<h5 class="baum-last-page-message center">' . esc_html($random_message) . '</h5>';

  echo "<div style='width:100%; height:40px;'></div>";

  // Conditionally display extra content based on message type
  switch ($message_type) {

    case 'related':
      // Related posts would be handled elsewhere
      echo '';
      break;

    case 'share':
      echo "<div class='center'>";
      get_template_part('parts/baum-social', 'story');
      echo "</div>";
      break;

    // case 'subscribe':
    //   echo "<a href='/subscribe' class='button baum-button' style=''>Join the Email List &nbsp; <i class='fa-solid fa-paper-plane'></i></a>";
    //   break;

    case 'comment':
      // Comments would be handled elsewhere
      echo '';
      break;

    // case 'donate':
    //   echo "<a href='/donate' class='button baum-button' style=''><i class='fa-solid fa-dollar'></i> &nbsp; Donate</a>";
    //   break;
  }

  echo "</div>";
  echo "<div style='width:100%; height:40px;'></div>";
}

/**
 * Generates styled pagination links for multi-page posts
 *
 * This function creates custom pagination links for posts that are split
 * across multiple pages using the <!--nextpage--> tag. It includes
 * contextual messages and styled buttons for navigation.
 *
 * @param string $numb_or_next Whether to show numbers or next/previous links
 * @return string HTML output for the pagination links
 *
 * @since 1.0.0
 */
function get_paginated_links ($numb_or_next = 'number') {
  ob_start();

  // Display contextual message based on current page
  if (is_last_page()) {
    $numb_or_next = 'number';
    echo '<h5 class="baum-page-message">You are on the last page</h5>';
  } else if (is_first_page() == false) {
    echo '<h5 class="baum-page-message">You are on page ' . absint(get_current_page()) . '</h5>';
  } else {
    echo '<h5 class="baum-page-message">Continue to Page 2</h5>';
  }

  // Generate the pagination links
  wp_link_pages([
    'before'            => '<div class="baum-page-links" style="margin-bottom:50px;">',
    'after'             => '</div>',
    'link_before'       => '<span class="button baum-button baum-button-small">',
    'link_after'        => '</span>',
    'next_or_number'    => $numb_or_next, // 'number' or 'next'
    'nextpagelink'      => 'Next Page &nbsp; <i class="fa-fw fa-solid fa-caret-right"></i>',
    'previouspagelink'  => '<i class="fa-fw fa-solid fa-caret-left"></i> &nbsp; Previous',
  ]);

  return ob_get_clean();
}

/////////////////////////////////////
// Register Baum Menus
/////////////////////////////////////

/**
 * Registers navigation menus for the theme
 *
 * This function registers multiple navigation menu locations that can be
 * managed through the WordPress admin. These menus are used in different
 * parts of the theme for navigation and user interface elements.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_register_menus () {
  register_nav_menus(
    [
      'apple-style-menu'          => 'Apple Style Menu',
      'main-menu-left'            => 'Main Menu Left',
      'main-menu-right'           => 'Main Menu Right',
      'minimal-menu-right'        => 'Minimal Menu Right',
      'main-user-menu'            => 'Main User Menu',
      'main-notifications-menu'   => 'Main Notifications Menu',
    ]
  );
}

add_action('init', 'baum_register_menus');

//
//
//

/**
 * Adds a class to keep submenus open for active menu items
 *
 * This function adds the 'menu-item-open' class to menu items that are
 * either the current page or an ancestor of the current page. This helps
 * to keep submenus expanded when a child page is active.
 *
 * @param array    $classes Array of CSS classes for the menu item
 * @param WP_Post  $item    The current menu item
 * @return array   Modified array of CSS classes
 *
 * @since 1.0.0
 */
function keep_open_submenu_class ($classes, $item) {
  if (in_array('current-menu-item', $classes) || in_array('current-menu-ancestor', $classes)) {
      $classes[] = 'menu-item-open'; // Add the class to open the parent
  }
  return $classes;
}

add_filter('nav_menu_css_class', 'keep_open_submenu_class', 10, 2);

/**
 * Removes Font Awesome classes from menu item CSS classes
 *
 * This function filters out Font Awesome classes from the menu item's CSS classes
 * to prevent them from being applied to the <li> element. The Font Awesome classes
 * are handled separately in the menu item title filter.
 *
 * @param array $classes Array of CSS classes for the menu item
 * @param WP_Post $item The current menu item object
 * @param stdClass $args Menu arguments
 * @return array Filtered array of CSS classes without Font Awesome classes
 *
 * @since 1.0.0
 */
function baum_clean_menu_classes ($classes, $item, $args) {
  // Filter out Font Awesome classes
  $non_fa_classes = array_filter($classes, function ($class) {
      return !str_starts_with($class, 'fa-');
  });

  return $non_fa_classes; // Return cleaned classes for the <li> element
}

add_filter('nav_menu_css_class', 'baum_clean_menu_classes', 10, 3);

/**
 * Adds Font Awesome icons to menu items
 *
 * This function processes menu items to add Font Awesome icons either from
 * CSS classes or ACF fields. It supports showing icons with or without labels
 * and handles the icon display logic.
 *
 * @param string $title The menu item title
 * @param WP_Post $item The current menu item object
 * @param stdClass $args Menu arguments
 * @param int $depth The depth of the menu item
 * @return string Modified menu item title with icon markup
 *
 * @since 1.0.0
 */
function baum_menu_item_fontawesome ($title, $item, $args, $depth) {
  // Check for Font Awesome classes in the menu item's CSS classes
  $fa_classes = array_filter($item->classes, function ($class) {
    return str_starts_with($class, 'fa-');
  });

  // Check ACF fields for icon settings
  $use_icon = get_field('use_icon', $item);

  if ($use_icon == true) {
    $menu_item_icon = get_field('menu_item_icon', $item);
    if ($menu_item_icon) {
      $fa_classes[] = sanitize_html_class($menu_item_icon);
    }
    $use_label_with_icon = get_field('display_label_with_icon', $item);
  }

  if (!empty($fa_classes)) {
    // If Font Awesome classes exist, inject them into the icon span
    $return_value = '<span class="menu-icon fa-fw ' . esc_attr(implode(' ', $fa_classes)) . '"></span>';

    // Check if we should show only the icon or icon with label
    if (isset($use_label_with_icon) && $use_label_with_icon == false) {
      return $return_value;
    }
    return $return_value . '<span class="menu-text">' . $title . '</span>';
  }

  // Return title without an icon if no Font Awesome classes are found
  return '<span class="menu-text">' . $title . '</span>';
}

add_filter('nav_menu_item_title', 'baum_menu_item_fontawesome', 10, 4);

// /**
//  * Adds notifications dropdown to main menu for logged-in users
//  *
//  * This function adds a notifications bell icon with a dropdown menu to the
//  * main menu for logged-in users. The dropdown contains the notifications
//  * menu defined in the WordPress admin.
//  *
//  * @param string $items The existing menu items HTML
//  * @param stdClass $args The menu arguments object
//  * @return string Modified menu items HTML with notifications dropdown
//  *
//  * @since 1.0.0
//  */
// function add_notifications_with_dropdown_to_menu ($items, $args) {
//   // Check if the user is logged in and if it's the primary menu
//   if (is_user_logged_in()
//     && ($args->theme_location === 'main-menu-right'
//     || $args->theme_location === 'minimal-menu-right')) {
//     $current_user = wp_get_current_user();

//     // Add the notifications icon with dropdown menu
//     $items .= '<li class="menu-item user-notifications-menu-item">
//                 <a href="#" id="user-notifications-trigger"><i class="fa-fw fa-solid fa-bell"></i></a>
//                 <ul id="main-notifications-menu" class="sub-menu" style="display:none;">'
//                   . wp_nav_menu([
//                     'theme_location'  => 'main-notifications-menu',
//                     'container'       => false,
//                     'items_wrap'      => '%3$s',
//                     'echo'            => false,
//                     'menu_class'      => 'baum-notifications-menu',
//                   ]) . '
//                 </ul>
//               </li>';
//   }
//   return $items;
// }

// add_filter('wp_nav_menu_items', 'add_notifications_with_dropdown_to_menu', 10, 2);

/**
 * Adds user avatar dropdown to main menu for logged-in users
 *
 * This function adds the current user's avatar with a dropdown menu to the
 * main menu for logged-in users. The dropdown contains the user menu
 * defined in the WordPress admin.
 *
 * @param string $items The existing menu items HTML
 * @param stdClass $args The menu arguments object
 * @return string Modified menu items HTML with avatar dropdown
 *
 * @since 1.0.0
 */
function add_avatar_with_dropdown_to_menu($items, $args) {
  // Check if the user is logged in and if it's the primary menu
  if (is_user_logged_in()
    && ($args->theme_location === 'main-menu-right'
    || $args->theme_location === 'minimal-menu-right')) {
    $current_user = wp_get_current_user();
    $avatar = get_avatar($current_user->ID, 28);

    // Add the avatar with dropdown menu
    $items .= '<li class="menu-item user-avatar-menu-item">
                <a href="#" id="user-avatar-trigger">' . $avatar . '</a>
                <ul id="main-user-menu" class="sub-menu" style="display: none;">'
                  . wp_nav_menu([
                    'theme_location' => 'main-user-menu',
                    'container' => false,
                    'items_wrap' => '%3$s',
                    'echo' => false,
                    'menu_class'     => 'baum-avatar-menu',
                  ]) . '
                </ul>
              </li>';
  }
  return $items;
}

add_filter('wp_nav_menu_items', 'add_avatar_with_dropdown_to_menu', 10, 2);

//
// <a href='#' data-tooltip-id='#id-guide-step-1'></a>
//
// <div id='id-guide-step-1'>
//   <h5>Guide</h5>
//  <span>Check this feature out.</span>
// </div>
//

// add_action('wp', function () {
//   echo "
//     <script>
//       $('[data-tooltip-id]').forEach(function (element, index) {
//         var id = $(this).data('tooltip-id');
//         var html = $(id).html();
//         $(this).tooltipster({
//           content: $(html),
//           contentAsHTML: true,
//           animation: 'grow',
//           theme: 'tooltipster-borderless',
//           trigger: 'click, tap',
//           delay: 0,
//         });
//       });
//     </script>";
// });

/////////////////////////////////////
// Calculate Reading Time
/////////////////////////////////////

/**
 * Calculates the estimated reading time for a post
 *
 * This function counts the number of words in a post's content and
 * calculates how long it would take to read based on an average reading
 * speed of 200 words per minute. The result is rounded up to the nearest
 * minute.
 *
 * @param int $post_id The ID of the post
 * @return int The estimated reading time in minutes
 *
 * @since 1.0.0
 */
function calculate_reading_time ($post_id) {
  // Get the content of the post
  $content = get_post_field('post_content', $post_id);

  // Strip tags and count the words
  $word_count = str_word_count(strip_tags($content));

  // Define average reading speed (words per minute)
  $reading_speed = 200;

  // Calculate the reading time in minutes
  $reading_time = ceil($word_count / $reading_speed);

  return $reading_time;
}

/////////////////////////////////////
// Add color to widget titles
/////////////////////////////////////

/**
 * Applies custom styling to widget titles
 *
 * This function modifies widget titles by adding custom background colors,
 * text colors, and CSS classes based on ACF fields set for each widget.
 * It wraps the widget title in div elements with the appropriate classes
 * to achieve the desired styling.
 *
 * @param array $params The widget parameters
 * @return array Modified widget parameters
 *
 * @since 1.0.0
 */
function baum_filter_widget_title ($params) {
  $term = 'widget_' . $params[0]['widget_id'];

  $heading_bg_color = get_field('heading_background_color', $term) ?? '';
  // 'baum-bg-secondary';
  $heading_text_color = get_field('heading_text_color', $term) ?? '';
  // 'baum-text-white';
  $heading_css = get_field('heading_css', $term) ?? '';

  // $t_icon = $heading_css;
  // $t_bg_c = $background;
  // $t_text_c = 'baum-text-' .
  // $t_text_c = $heading_text_color;
  // baum-bg baum-width-100
  // . '<i class="' . $title_icon . '"></i>'

  $params[0]['before_title'] = '<div class="baum-title-width"><div class="' . $heading_bg_color . ' ' . $heading_text_color . ' ' . $heading_css . '">' . $params[0]['before_title'];
  $params[0]['after_title'] = $params[0]['after_title'] . '</div></div>';
  return $params;
}

add_filter('dynamic_sidebar_params', 'baum_filter_widget_title');

/////////////////////////////////////
//
/////////////////////////////////////

// function display_all_month_calendars_from_date_url () {
//   global $wp_query;
//   // Get year and month from the URL or fallback to current date
//   $current_year = get_query_var('year') ? intval(get_query_var('year')) : date('Y');
//   $current_month = get_query_var('monthnum') ? intval(get_query_var('monthnum')) : date('n');
//   // Loop through all 12 months
//   for ($month = 1; $month <= 12; $month++) {
//       // Determine if the current month should be active
//       $is_active = ($month == $current_month) ? ' active' : '';
//       // Start a container for each calendar
//       echo '<div class="calendar-container center' . $is_active . '" style="margin-bottom:0px;text-align:left;">';
//       echo '<strong class="center">' . date('F', mktime(0, 0, 0, $month, 1)) . ' ' . $current_year . '</strong>';
//       // Display the calendar for the given month
//       echo get_calendar(false, true, array('monthnum' => $month, 'year' => $current_year));
//       // End container
//       echo '</div>';
//   }
// }

/**
 * Displays a custom calendar for a specific year and month
 *
 * This function generates a WordPress calendar widget for a specific month
 * and year while preserving the global calendar state. It temporarily
 * modifies global variables to generate the calendar and then restores them.
 *
 * @param int $year The year for the calendar
 * @param int $month The month for the calendar (1-12)
 * @return string The HTML output of the calendar
 *
 * @since 1.0.0
 */
function display_custom_calendar ($year, $month) {
  global $wpdb, $m, $monthnum, $year, $wp_locale;

  // Backup global variables to avoid conflicts
  $original_monthnum = $monthnum;
  $original_year = $year;

  // Set year and month for this calendar
  $monthnum = absint($month);
  $year = absint($year);

  // Start capturing calendar HTML
  ob_start();

  // Generate the calendar
  get_calendar(false);

  // Restore global variables
  $monthnum = $original_monthnum;
  $year = $original_year;

  // Return the calendar HTML
  return ob_get_clean();
}

/**
 * Displays all 12 months of calendars for the current year
 *
 * This function renders a full year view with all 12 monthly calendars.
 * It highlights the current month and provides navigation links for each month.
 * The year and month are determined from URL parameters or current date.
 *
 * @return void Outputs HTML for all 12 monthly calendars
 *
 * @since 1.0.0
 */
function display_all_calendars_with_links () {
  // Get current year and month for defaults
  $current_year = get_query_var('year') ? absint(get_query_var('year')) : absint(date('Y'));
  $current_month = get_query_var('monthnum') ? absint(get_query_var('monthnum')) : absint(date('n'));

  // Loop through all 12 months
  for ($month = 1; $month <= 12; $month++) {
    // Determine if this month is the active one
    $is_active = ($month == $current_month) ? ' active' : '';

    // Start a container for each calendar
    echo '<div class="calendar-container center' . esc_attr($is_active) . '" style="margin:0 auto;text-align:left;vertical-align:top;">';
    echo '<strong class="center">' . esc_html(date('F', mktime(0, 0, 0, $month, 1))) . ' ' . absint($current_year) . '</strong>';

    // Display the calendar for the given month
    echo display_custom_calendar($current_year, $month);
    echo '</div>';
  }
}

/**
 * Displays a five-month calendar view centered on the current month
 *
 * This function shows a range of 5 calendars: 2 months before the current month,
 * the current month (highlighted), and 2 months after. It handles year boundaries
 * correctly and provides navigation links for each month.
 *
 * @return void Outputs HTML for five monthly calendars
 *
 * @since 1.0.0
 */
function display_five_month_calendars () {
  global $wpdb, $m, $monthnum, $year, $wp_locale;

  // Get year and month from the URL or fallback to current date
  $current_year = get_query_var('year') ? absint(get_query_var('year')) : absint(date('Y'));
  $current_month = get_query_var('monthnum') ? absint(get_query_var('monthnum')) : absint(date('n'));

  // Calculate the range of months to display (-2, current, +2)
  $months_to_display = range($current_month - 2, $current_month + 2);

  // Start rendering calendars
  foreach ($months_to_display as $month_offset) {
    // Normalize the year and month (handles cases like month=0 or month=13)
    $adjusted_date = mktime(0, 0, 0, $month_offset, 1, $current_year);
    $display_month = absint(date('n', $adjusted_date));
    $display_year = absint(date('Y', $adjusted_date));

    // Determine if this month is active
    $is_active = ($display_month == $current_month && $display_year == $current_year) ? ' active' : '';

    // Backup global variables to avoid conflicts
    $original_monthnum = $monthnum;
    $original_year = $year;

    // Set global variables for the calendar instance
    $monthnum = $display_month;
    $year = $display_year;

    // Capture calendar output
    ob_start();
    get_calendar(false);
    $calendar_html = ob_get_clean();

    // Restore global variables
    $monthnum = $original_monthnum;
    $year = $original_year;

    // Start a container for each calendar
    echo '<div style="margin:5px;">';
    echo '<div class="baum-card calendar-container center' . esc_attr($is_active) . '" style="margin:0 auto;text-align:left;vertical-align:top;">';
    echo '<a href="/' . absint($display_year) . '/' . absint($display_month) . '">' . esc_html(date('F Y', $adjusted_date)) . '</a>';
    echo $calendar_html;
    echo '</div>';
    echo '</div>';
  }
}

// //
// // Register Places Taxonomy
// // - taxonomy is (a hack) used by both `user` and `story`
// //

// function register_shared_places_taxonomy() {
//   $args = array(
//       'labels'            => array(
//           'name'          => 'Places',
//           'singular_name' => 'Place',
//       ),
//       'public'            => true,
//       'hierarchical'      => true,
//       'show_admin_column' => true,
//       'show_in_rest'      => true, // Enable for Gutenberg
//       'rewrite'           => [ 'slug' => 'places' ],
//   );

//   // Register "Places" for both posts and users
//   register_taxonomy('places', ['post', 'user'], $args);
// }

// add_action('init', 'register_shared_places_taxonomy');

/**
 * Links book people to WordPress users when a post is saved
 *
 * This function automatically links people mentioned in book ACF fields
 * (book_subject_people and book_author) to actual WordPress users based on
 * display name and user nicename matching. The linked user IDs are stored
 * in post meta for later use.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 *
 * @todo Handle cases where multiple users have the same display name or nicename
 */
function link_book_people_to_post ($post_id) {
  // Only process posts
  if (get_post_type($post_id) !== 'post') {
      return;
  }

  $linked_users = [];

  // Gather names from both ACF fields
  $people_names = get_field('book_subject_people', $post_id);
  $author_names = get_field('book_author', $post_id);

  $all_names = [];

  // Split multiple subject people (comma-separated)
  if ($people_names) {
      $all_names = array_merge($all_names, array_map('trim', explode(',', $people_names)));
  }

  // Split multiple authors (comma-separated)
  if ($author_names) {
      $all_names = array_merge($all_names, array_map('trim', explode(',', $author_names)));
  }

  // Remove empty values and sanitize
  $all_names = array_filter(array_map('sanitize_text_field', $all_names));

  // Search for each person name in the WordPress users table
  foreach ($all_names as $person_name) {
      $user_query = new WP_User_Query([
          'search'         => '*' . esc_attr($person_name) . '*',
          'search_columns' => ['display_name', 'user_nicename'],
      ]);

      $users = $user_query->get_results();

      if (!empty($users)) {
          foreach ($users as $user) {
              $linked_users[] = absint($user->ID);
          }
      }
  }

  // Remove duplicates and update meta field
  $linked_users = array_unique($linked_users);
  update_post_meta($post_id, '_linked_users', $linked_users);
}

add_action('save_post', 'link_book_people_to_post');

/**
 * Links book places to the places taxonomy when a post is saved
 *
 * This function automatically creates taxonomy terms for places mentioned
 * in the book_subject_places ACF field and associates them with the post.
 * If a place term doesn't exist, it creates it automatically.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 */
function link_book_places_to_taxonomy($post_id) {
  // Only process posts
  if (get_post_type($post_id) !== 'post') {
      return;
  }

  // Get places from ACF field
  $places = get_field('book_subject_places', $post_id);
  if (!$places) {
      return;
  }

  // Split comma-separated places and sanitize
  $places_array = array_map('trim', explode(', ', $places));
  $places_array = array_filter(array_map('sanitize_text_field', $places_array));
  $linked_terms = [];

  foreach ($places_array as $place_name) {
      // Check if the term already exists
      $term = get_term_by('name', $place_name, 'places');

      // Create the term if it doesn't exist
      if (!$term) {
          $term = wp_insert_term($place_name, 'places');
      }

      // Add term ID to linked terms if successful
      if (!is_wp_error($term)) {
          $term_id = is_array($term) ? $term['term_id'] : $term->term_id;
          $linked_terms[] = absint($term_id);
      }
  }

  // Associate the terms with the post
  if (!empty($linked_terms)) {
      wp_set_object_terms($post_id, $linked_terms, 'places', false);
  }
}

add_action('save_post', 'link_book_places_to_taxonomy');

/**
 * Converts ISBN-10 to ISBN-13 format
 *
 * This function takes a 10-digit ISBN and converts it to the 13-digit format
 * by adding the "978" prefix and calculating the new check digit according
 * to the ISBN-13 algorithm.
 *
 * @param string $isbn10 The 10-digit ISBN to convert
 * @return string|false The 13-digit ISBN or false if invalid input
 *
 * @since 1.0.0
 */
function convert_isbn10_to_isbn13 ($isbn10) {
  // Validate input length
  if (strlen($isbn10) !== 10) {
      return false; // Not a valid ISBN-10
  }

  // Create ISBN-13 base by adding "978" prefix and removing check digit
  $isbn13_base = '978' . substr($isbn10, 0, 9);
  $sum = 0;

  // Compute new ISBN-13 check digit using the algorithm
  for ($i = 0; $i < 12; $i++) {
      $digit = (int) $isbn13_base[$i];
      $sum += ($i % 2 === 0) ? $digit : $digit * 3;
  }

  // Calculate and append the new check digit
  $check_digit = (10 - ($sum % 10)) % 10;
  return $isbn13_base . $check_digit;
}

/**
 * Fetches book data from Open Library API using ISBN
 *
 * This function automatically retrieves book information from the Open Library API
 * when a post is saved that has book-related tags/categories and an ISBN field.
 * It populates ACF fields with book metadata including title, author, subjects,
 * and downloads the cover image as a featured image.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 */
function fetch_book_data_from_isbn ($post_id) {

  // Only process posts
  if (get_post_type($post_id) !== 'post') {
      return;
  }

  // Only process posts tagged or categorized as books
  if (!has_tag('Book')
  && !has_tag('Books')
  && !has_category('Book')
  && !has_category('Books')) return;

  $isbn = get_field('isbn_number', $post_id);
  $isbn10 = get_field('isbn_10', $post_id);
  $isbn13 = get_field('isbn_13', $post_id);
  $isbn10 = $isbn10 ? $isbn10 : $isbn;
  $isbn = $isbn ? $isbn : $isbn10;

  // error_log('$isbn: ' . $isbn);

  // Convert ISBN-10 to ISBN-13 if ISBN-13 is missing
  if (!$isbn13 && $isbn10) {
      $isbn13 = convert_isbn10_to_isbn13($isbn10);
  }

  // Use ISBN-13 if available, otherwise fallback to ISBN-10
  $isbn = $isbn13 ? $isbn13 : $isbn10;

  if (!$isbn) {
      return; // No valid ISBN available
  }

  // Now we are guaranteed to be using ISBN-13 if possible
  $api_url = "https://openlibrary.org/api/books?bibkeys=ISBN:{$isbn}&format=json&jscmd=data";
  $response = wp_remote_get($api_url);

  // error_log('$response' . print_r($response, true));

  if (is_wp_error($response)) {
      return;
  }

  $body = wp_remote_retrieve_body($response);
  $data = json_decode($body, true);

  error_log('$data' . print_r($data, true));

  if (empty($data["ISBN:$isbn"])) {
      return;
  }

  $book = $data["ISBN:$isbn"];

  // Extracting book details
  $title = $book['title'] ?? '';
  $authors = isset($book['authors']) ? implode(', ', array_column($book['authors'], 'name')) : '';
  $publish_date = $book['publish_date'] ?? '';
  $publisher = isset($book['publishers']) ? implode(', ', array_column($book['publishers'], 'name')) : '';

  error_log('$book[cover]: ' . print_r($book['cover'], true));

  $cover_url = $book['cover']['large'] ?? '';
  $weight = $book['weight'] ?? '';
  $page_count = $book['number_of_pages'] ?? $book['pagination'] ? $book['pagination'] : '';

  // Extracting identifiers
  $identifiers = $book['identifiers'] ?? [];
  $isbn_10 = isset($identifiers['isbn_10']) ? implode(', ', $identifiers['isbn_10']) : '';
  $isbn_13 = isset($identifiers['isbn_13']) ? implode(', ', $identifiers['isbn_13']) : '';
  $lccn = isset($identifiers['lccn']) ? implode(', ', $identifiers['lccn']) : '';
  $oclc = isset($identifiers['oclc']) ? implode(', ', $identifiers['oclc']) : '';

  // Expanded and refined categorization
  $topics = [];
  $people = [];
  $places = [];
  $times = [];

  if (!empty($book['subjects'])) {
    foreach ($book['subjects'] as $subject) {
      $name = $subject['name'] ?? '';

      // Expanded regex for time periods (historical events, centuries, eras)
      if (preg_match('/\b(century|era|revolution|age|war|period|dynasty|renaissance|millennium|empire|epoch|antiquity|golden age|cold war|great depression|middle ages|dark ages|industrial revolution|atomic age|space race|roaring twenties)\b/i', $name)) {
        $times[] = $name;
      }
      // Expanded regex for people (titles, professions, famous figures)
      elseif (preg_match('/\b(king|queen|prince|princess|duke|duchess|emperor|empress|czar|pharaoh|shah|sultan|chieftain|warlord|knight|bishop|pope|cardinal|monk|priest|rabbi|imam|shaman|prophet|philosopher|scientist|astronomer|mathematician|engineer|inventor|explorer|pioneer|artist|poet|author|playwright|musician|composer|conductor|actor|filmmaker|president|prime minister|chancellor|governor|mayor|senator|congressman|dictator|general|commander|soldier|revolutionary|martyr|activist|civil rights leader|spy|assassin|criminal|outlaw|gangster|pirate|samurai|ninja|knight templar|templar|businessman|entrepreneur|mogul|tycoon|CEO|founder|athlete|sportsman|coach|manager|doctor|surgeon|physician|nurse|healer|psychologist|psychiatrist|lawyer|judge|detective|policeman|sheriff|deputy|cowboy|gunslinger)\b/i', $name)) {
        $people[] = $name;
      }
      // Expanded regex for places (geographical locations, landmarks)
      elseif (preg_match('/\b(city|town|village|metropolis|municipality|kingdom|empire|state|nation|country|province|region|territory|colony|district|zone|area|island|archipelago|peninsula|mountain|valley|ridge|canyon|plateau|forest|rainforest|desert|savanna|tundra|prairie|river|lake|ocean|sea|bay|gulf|strait|channel|fjord|lagoon|wetland|marsh|swamp|cave|glacier|volcano|crater|highland|lowland|grassland|tundra|basin|tropics|equator|antarctic|arctic|tropics|earth|moon|mars|venus|jupiter|saturn|uranus|neptune|pluto|solar system|galaxy|milky way|universe)\b/i', $name)) {
        $places[] = $name;
      }
      // Everything else is classified as a general topic
      else {
        $topics[] = $name;
      }
    }
  }

  // Update ACF fields
  update_field('book_title', $title, $post_id);
  update_field('book_author', $authors, $post_id);
  update_field('book_publish_date', $publish_date, $post_id);
  update_field('book_publisher', $publisher, $post_id);
  update_field('book_weight', $weight, $post_id);
  update_field('book_page_count', $page_count, $post_id);
  update_field('book_subject_topics', implode(', ', $topics), $post_id);
  update_field('book_subject_people', implode(', ', $people), $post_id);
  update_field('book_subject_places', implode(', ', $places), $post_id);
  update_field('book_subject_times', implode(', ', $times), $post_id);

  if (!empty($cover_url)) {
    error_log('download_and_attach_image');
    $attachment_id = download_and_attach_image($cover_url, $post_id);
    if ($attachment_id) {

      error_log('attachment_id: ' . $attachment_id);

      $max_images = get_theme_mod('max_featured_images', 5);
      if (isset($_POST['multiple_featured_images'])) {
        $images = array_slice($_POST['multiple_featured_images'], 0, $max_images);
      } else {
        $images = [];
      }
      $images[] = $attachment_id;

      error_log('images: ' . print_r($images, true));
      $images = array_unique($images);
      update_post_meta($post_id, 'multiple_featured_images', $images);
    }
  }

  // Update identifiers
  update_field('book_identifiers', compact('isbn_10', 'isbn_13', 'lccn', 'oclc'), $post_id);
}

add_action('save_post', 'fetch_book_data_from_isbn');

/**
 * Wrap the first occurrence of specified delimiters and everything before it in a span with a specific class in post titles.
 *
 * Delimiters include colon (:), long dash (–), brackets ([]), rounded brackets (()), carets (<>), and tildes (`word`).
 * Tildes will be stripped out on the frontend but remain in the backend.
 *
 * @param string $title The original post title.
 * @param int $id The post ID.
 * @return string The modified post title.
 */

/**
 * Formats post titles with special styling for delimited text
 *
 * This function processes post titles and applies special styling to text
 * enclosed in backticks (`). It replaces the backticks with a span element
 * that has primary background color and white text, creating a highlighted
 * effect in the title.
 *
 * @param string $title The post title
 * @param int    $id    The post ID
 * @return string The formatted title
 *
 * @since 1.0.0
 */
function wrap_first_delimiter_in_title ($title, $id) {
  // Array of delimiters to search for

  // Handle tildes globally after processing the first delimiter
  $title = preg_replace_callback('/`([^`]+)`/', function($matches) {
    // Wrap the text within tildes in a span and remove tildes
    return '<span class="primary-bg white">' . $matches[1] . '</span>';
  }, $title);

  return $title;
}

add_filter('the_title', 'wrap_first_delimiter_in_title', 10, 2);

//
//
//

/**
 * Applies special styling to post titles with specific delimiters
 *
 * This function looks for specific delimiters (like '–') in post titles
 * and applies custom styling to the text before and including the delimiter.
 * It creates a visually distinct heading element with background color
 * and white text.
 *
 * @param string $title The post title
 * @return string The styled title
 *
 * @since 1.0.0
 */
function style_the_title ($title) {
  $delimiters = ['–']; // '<'];

  // Check if we're in the main query and it's a single post/page view
  // if (is_single($id) || is_page($id)) {
    foreach ($delimiters as $delimiter) {
      if (strpos($title, $delimiter) !== false) {
        // Split the title into two parts: before the first delimiter and after the first delimiter
        list($before_delimiter, $after_delimiter) = explode($delimiter, $title, 2);

        // Wrap the text before and including the first delimiter in a span with the desired style
        // $wrapped_text = '<span class="quinary">' . $before_delimiter . $delimiter . '</span>';
        // $wrapped_text = '<span class="secondary-bg denary" style="letter-spacing:0px;display:block;border-radius:var(--border-radius);font-size:40%;line-height:1;padding:10px;width:auto;clear:both;margin-bottom:10px;">'
        // . $before_delimiter . $delimiter . '</span>';
        $wrapped_text = '<span class="baum-heading baum-heading tertiary-bg white" style="margin-bottom:10px;">'
        . $before_delimiter . $delimiter . '</span>';

        // Combine the wrapped text with the remaining title
        $title = $wrapped_text . $after_delimiter;
        break; // Only wrap the first found delimiter
      }
    }
  // }
  return $title;
}

// oEmbed functions have been moved to functions-ajax.php

/////////////////////////////////////
// Popular Posts (deprecated)
//
// INFO: this functionality was done away with in favor of Post Views Counter
// because the plugin has charts that help with determining site traffic
//
/////////////////////////////////////

// function getCrunchifyPostViews ($postID) {
//   $count_key = 'post_views_count';
//   $count = get_post_meta($postID, $count_key, true);
//   if ($count == '') {
//     delete_post_meta($postID, $count_key);
//     add_post_meta($postID, $count_key, '0');
//     return "0 Views";
//   }
//   return number_format($count) . ' Views';
// }

// function setCrunchifyPostViews ($postID) {
//   $count_key = 'post_views_count';
//   $count = get_post_meta($postID, $count_key, true);
//   if ($count == '') {
//     $count = 0;
//     delete_post_meta($postID, $count_key);
//     add_post_meta($postID, $count_key, '0');
//   } else {
//     $count++;
//     update_post_meta($postID, $count_key, $count);
//   }
// }

/////////////////////////////////////
// Related Posts (deprecated)
//
// INFO: This functionality is now handled by the Baum Flexible Widget
//
/////////////////////////////////////

// function baum_related_posts ($baum_related_num) {
//   global $post;
//   global $do_not_duplicate;
//   $orig_post = $post;
//   $return_value = 0;
//   $tags = wp_get_post_tags($post->ID);
//     if ($tags) {
//       $tag_ids = array();
//       foreach ($tags as $individual_tag) $tag_ids[] = $individual_tag->term_id;
//       $args = array(
//         'tag__in' => $tag_ids,
//         'order' => 'DESC',
//         'orderby' => 'date',
// 				'post__not_in' => $do_not_duplicate,
//         'posts_per_page'=> $baum_related_num,
//         'ignore_sticky_posts'=> 1
//       );
//       $my_query = new WP_Query($args);
//       $return_value = $my_query;

//     }
//   $post = $orig_post;
//   return $return_value;
// }

/////////////////////////////////////
// Change Label For Posts To Stories
/////////////////////////////////////

/**
 * Changes the WordPress default "Posts" labels to "Stories"
 *
 * This function modifies the labels of the default WordPress post type
 * to use "Stories" instead of "Posts" throughout the admin interface.
 * It also changes the menu icon to better represent the content type.
 *
 * @return void
 *
 * @since 1.0.0
 */
function change_posts_to_stories () {
  $get_post_type = get_post_type_object('post');
  $get_post_type->menu_icon = 'dashicons-edit-large';
  $labels = $get_post_type->labels;
  $labels->name = 'Stories';
  $labels->singular_name = 'Story';
  $labels->add_new = 'Add Story';
  $labels->add_new_item = 'Add Story';
  $labels->edit_item = 'Edit Story';
  $labels->new_item = 'Story';
  $labels->view_item = 'View Story';
  $labels->search_items = 'Search Stories';
  $labels->not_found = 'No Stories found';
  $labels->not_found_in_trash = 'No Stories found in Trash';
  $labels->all_items = 'All Stories';
  $labels->menu_name = 'Stories';
  $labels->name_admin_bar = 'Story';
}

add_action('init', 'change_posts_to_stories');

/**
 * Changes the WordPress default "Categories" labels to "Topics"
 *
 * This function modifies the labels of the default WordPress category taxonomy
 * to use "Topics" instead of "Categories" throughout the admin interface.
 * This provides more semantic meaning for content organization.
 *
 * @return void
 *
 * @since 1.0.0
 */
function change_categories_to_topics () {
  global $wp_taxonomies;
  $labels = &$wp_taxonomies['category']->labels;
  $labels->name = 'Topics';
  $labels->singular_name = 'Topic';
  $labels->add_new = 'Add Topic';
  $labels->add_new_item = 'Add Topic';
  $labels->edit_item = 'Edit Topic';
  $labels->new_item = 'Topic';
  $labels->view_item = 'View Topic';
  $labels->search_items = 'Search Topics';
  $labels->not_found = 'No Topics found';
  $labels->not_found_in_trash = 'No Topics found in Trash';
  $labels->all_items = 'All Topics';
  $labels->menu_name = 'Topics';
  $labels->name_admin_bar = 'Topics';
}

add_action('init', 'change_categories_to_topics');

/////////////////////////////////////
//
// Use meta data in single.php or elsewhere like this
//
// get_post_meta($post->ID, "baum_video_embed", true)
// Values: null / abc123
//
// get_post_meta($post->ID, "baum_post_template", true)
// Values: temp1 / temp2 // ... / temp7
//
// get_post_meta($post->ID, "baum_featured_image", true)
// Values: show / hide
// Values: on / off
//
// get_post_meta($post->ID, "baum_post_sidebar", true)
// Values: show / hide
// Values: on / off
//
// get_post_meta($post->ID, "baum_post_author", true)
// Values: show / hide / guest_only
// Values: on / off / guest_only
//
/////////////////////////////////////


/////////////////////////////////////
// Add 5 / 30 / 60 / 90 / 180 / 360 minute crons
/////////////////////////////////////

// function my_cron_schedules ($schedules) {
//   if (!isset($schedules["5min"])) {
//     $schedules["5min"] = array(
//       'interval' => 5*60,
//       'display' => __('Once every 5 minutes')
//     );
//   }
//   if (!isset($schedules["30min"])) {
//     $schedules["30min"] = array(
//       'interval' => 30*60,
//       'display' => __('Once every 30 minutes')
//     );
//   }
//   if (!isset($schedules["60min"])) {
//     $schedules["60min"] = array(
//       'interval' => 60*60,
//       'display' => __('Once every 60 minutes')
//     );
//   }
//   if (!isset($schedules["90min"])) {
//     $schedules["90min"] = array(
//       'interval' => 90*60,
//       'display' => __('Once every 90 minutes')
//     );
//   }
//   if (!isset($schedules["180min"])) {
//     $schedules["180min"] = array(
//       'interval' => 180*60,
//       'display' => __('Once every 180 minutes')
//     );
//   }
//   if (!isset($schedules["360min"])) {
//     $schedules["360min"] = array(
//       'interval' => 360*60,
//       'display' => __('Once every 360 minutes')
//     );
//   }
//   if (!isset($schedules["720min"])) {
//     $schedules["720min"] = array(
//       'interval' => 720*60,
//       'display' => __('Once every 720 minutes')
//     );
//   }
//   return $schedules;
// }

// add_filter('cron_schedules', 'my_cron_schedules');

/////////////////////////////////////
// Breaking News Expiration Cron Job
/////////////////////////////////////

// function schedule_update_cron () {
//   wp_schedule_event(time(), '5min', 'update_crons_hook');
// }

// if (!wp_next_scheduled('update_crons_hook')) {
//   add_action('init', 'schedule_update_cron');
// }

// function baum_update_crons () {
//   wp_clear_scheduled_hook('tag_hook');
// }

// add_action('update_crons_hook', 'baum_update_crons');

// function schedule_tag_cron () {
//   $baum_breaking_news_cron = get_theme_mod('baum_breaking_news_cron', 180);
//   // wp_schedule_event(time(), $baum_breaking_news_cron, 'tag_hook');
//   $time = time() + ($baum_breaking_news_cron * 60);
//   wp_schedule_single_event($time, 'tag_hook');
// }

// if (!wp_next_scheduled('tag_hook')) {
//   add_action('init', 'schedule_tag_cron');
// }

// function baum_expire_breaking_tag () {
//   $the_query = new WP_Query([ 'tag' => 'breaking' ]);
//   while ($the_query->have_posts()) {
//     if ($the_query->have_posts()) {
//       $the_query->the_post();
//       $post_id = get_the_ID();
//       $post_tags = wp_get_post_terms($post_id, 'post_tag', [
//         'fields' => 'slugs'
//       ]);
//       $pos = array_search('breaking', $post_tags);
//       if (false !== $pos) {
//         unset($post_tags[$pos]);
//         wp_set_post_terms($post_id, $post_tags, 'post_tag');
//       }
//     }
//   }
// }

// add_action('tag_hook', 'baum_expire_breaking_tag');

//
//
//

/**
 * Gets unique email addresses of authors from a specific story collection
 *
 * This function queries posts within a specified story collection taxonomy term
 * and collects unique email addresses from both the main post authors and any
 * additional authors specified in ACF fields.
 *
 * @param string $taxonomy_term The slug of the story collection taxonomy term
 * @return string Comma-separated string of unique email addresses
 *
 * @since 1.0.0
 */
function get_authors_emails_by_story_collection($taxonomy_term) {
  // Initialize an array to store unique email addresses
  $email_addresses = [];

  // Sanitize the taxonomy term
  $taxonomy_term = sanitize_text_field($taxonomy_term);

  // Query posts in the specified taxonomy term
  $query = new WP_Query([
      'post_type'      => ['story', 'post'], // Adjust to your post types
      'tax_query'      => [
          [
              'taxonomy' => 'story-collection', // Taxonomy name
              'field'    => 'slug',
              'terms'    => $taxonomy_term, // Term slug
          ],
      ],
      'posts_per_page' => -1, // Get all posts
  ]);

  if ($query->have_posts()) {
      while ($query->have_posts()) {
          $query->the_post();

          // Get the post author's email
          $post_author_id = get_the_author_meta('ID');
          $post_author_email = get_the_author_meta('user_email', $post_author_id);
          if ($post_author_email && is_email($post_author_email) && !in_array($post_author_email, $email_addresses)) {
              $email_addresses[] = sanitize_email($post_author_email);
          }

          // Get additional authors from the ACF field
          $additional_authors = get_field('additional_authors'); // Adjust field name as needed
          if (!empty($additional_authors) && is_array($additional_authors)) {
              foreach ($additional_authors as $author) {
                  if (!empty($author['ID'])) {
                      $user_data = get_userdata(absint($author['ID']));
                      $additional_author_email = $user_data->user_email ?? null;
                      if ($additional_author_email && is_email($additional_author_email) && !in_array($additional_author_email, $email_addresses)) {
                          $email_addresses[] = sanitize_email($additional_author_email);
                      }
                  }
              }
          }
      }
  }
  wp_reset_postdata();

  // Return a comma-separated string of unique email addresses
  return implode(',', $email_addresses);
}

/**
 * Generates a person card shortcode for story collection authors
 *
 * This helper function creates a baum_person_card shortcode with email addresses
 * from authors in a specific story collection, formatted according to the
 * provided attributes.
 *
 * @param array $atts Shortcode attributes including taxonomy_term, format, and title
 * @return string The generated person card shortcode output
 *
 * @since 1.0.0
 */
function generate_single_baum_person_card_shortcode($atts) {
  // Get the comma-separated list of email addresses
  $email_addresses = get_authors_emails_by_story_collection($atts['taxonomy_term']);

  // Sanitize attributes
  $format = sanitize_text_field($atts['format']);
  $title = sanitize_text_field($atts['title']);

  // Return the person card shortcode with the email addresses
  return do_shortcode("[baum_person_card email='{$email_addresses}' format='{$format}' title='{$title}' float='center' size='64' columns='1' width='100%']");
}

/**
 * Displays authors from a specific story collection as person cards
 *
 * This shortcode function displays all authors (main and additional) from posts
 * within a specified story collection taxonomy term as formatted person cards.
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output of the person cards or error message
 *
 * @since 1.0.0
 *
 * @example [story_collection_authors taxonomy_term="example-collection" format="5" title="Authors"]
 */
function display_story_collection_authors($atts) {
  $atts = shortcode_atts([
    'title' => '',
    'format' => '5',
    'taxonomy_term' => '', // Term slug of the story collection
  ], $atts, 'story_collection_authors');

  // Validate required parameter
  if (empty($atts['taxonomy_term'])) {
      return '<p>Please provide a valid taxonomy term.</p>';
  }

  // Generate and return the person card shortcode
  return generate_single_baum_person_card_shortcode($atts);
}

add_shortcode('story_collection_authors', 'display_story_collection_authors');

//
//
//

/**
 * Gets unique email addresses of influencers from posts
 *
 * This function queries posts (optionally filtered by story collection) and
 * collects unique email addresses from users specified in the 'influencers'
 * ACF field. Can be used to get influencers from all posts or a specific collection.
 *
 * @param string|null $taxonomy_term Optional. The slug of the story collection taxonomy term
 * @return string Comma-separated string of unique influencer email addresses
 *
 * @since 1.0.0
 */
function get_influencer_emails($taxonomy_term = null) {

  // Initialize an array to store unique email addresses
  $email_addresses = [];

  // Set up the query arguments
  $query_args = [
      'post_type'      => ['story', 'post'], // Adjust to your post types
      'posts_per_page' => -1, // Get all posts
  ];

  // If a taxonomy term is provided, add it to the query
  if ($taxonomy_term) {
      $taxonomy_term = sanitize_text_field($taxonomy_term);
      $query_args['tax_query'] = [
          [
              'taxonomy' => 'story-collection', // Taxonomy name
              'field'    => 'slug',
              'terms'    => $taxonomy_term, // Term slug
          ],
      ];
  }

  // Query posts
  $query = new WP_Query($query_args);

  if ($query->have_posts()) {
      while ($query->have_posts()) {
          $query->the_post();

          // Get users from the influencers ACF field
          $influencers = get_field('influencers'); // Adjust field name as needed

          if (!empty($influencers) && is_array($influencers)) {
              foreach ($influencers as $user) {
                  if (!empty($user['ID'])) {
                      $user_data = get_userdata(absint($user['ID']));
                      $influencer_email = $user_data->user_email ?? null;
                      if ($influencer_email && is_email($influencer_email) && !in_array($influencer_email, $email_addresses)) {
                          $email_addresses[] = sanitize_email($influencer_email);
                      }
                  }
              }
          }
      }
  }

  wp_reset_postdata();

  // Return a comma-separated string of unique email addresses
  return implode(',', $email_addresses);
}

//
//
//

/**
 * Generates a person card shortcode for influencers
 *
 * This helper function creates a baum_person_card shortcode with email addresses
 * from influencers, formatted according to the provided attributes with support
 * for multiple columns.
 *
 * @param array $atts Shortcode attributes including taxonomy_term, format, and columns
 * @return string The generated person card shortcode output
 *
 * @since 1.0.0
 */
function generate_influencer_person_card_shortcode($atts) {
  // Get the comma-separated list of influencer email addresses
  $email_addresses = get_influencer_emails($atts['taxonomy_term']);

  // Sanitize attributes
  $format = sanitize_text_field($atts['format']);
  $columns = sanitize_text_field($atts['columns']);

  // Return the person card shortcode with the email addresses
  return do_shortcode("[baum_person_card email='{$email_addresses}' format='{$format}' float='center' size='64' columns='{$columns}' width='100%']");
}

/**
 * Displays influencers as person cards
 *
 * This shortcode function displays influencers from posts (optionally filtered
 * by story collection) as formatted person cards. Supports multiple columns
 * and different display formats.
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output of the influencer person cards
 *
 * @since 1.0.0
 *
 * @example [story_influencers columns="3" format="3"]
 * @example [story_influencers taxonomy_term="example-collection" columns="2" format="5"]
 */
function display_influencers($atts) {
  $atts = shortcode_atts([
    'columns' => '3',
    'format' => '3',
    'taxonomy_term' => '', // Optional: Term slug of the story collection
  ], $atts, 'story_influencers');

  // Generate and return the influencer person card shortcode
  return generate_influencer_person_card_shortcode($atts);
}

add_shortcode('story_influencers', 'display_influencers');

//
// Usage Example
//
//
// For Influencers in All Stories:
// [story_influencers]
//
//
// For Influencers in a Specific Story Collection:
// [story_influencers taxonomy_term="example-term-slug"]
//
//

/**
 * Gets all author email addresses for the current post
 *
 * This function retrieves email addresses from both the main post author
 * and any additional authors specified in the 'additional_authors' ACF field.
 * It can return either a delimited string or an array of author objects.
 *
 * @param string $delimiter The delimiter to use for joining emails (default: ',')
 *                         If empty, returns array of author objects instead
 * @return string|array|null Comma-separated email string, array of authors, or null if no authors
 *
 * @since 1.0.0
 */
function get_all_author_emails ($delimiter = ',') {

  // Get the post author ID
  $post_author_id = get_post_field('post_author', get_the_ID());

  // Retrieve the user object of the post author
  $post_author_object = get_user_by('id', $post_author_id);

  if (!$post_author_object) {
    return null;
  }

  $post_author_object = $post_author_object->to_array();

  // Get the existing additional authors from ACF
  $authors = get_field('additional_authors');

  // Ensure $authors is an array
  // (it might be null if no additional authors are set)
  if (!is_array($authors)) {
    $authors = [];
  }

  // Prepend the post author object to the authors array
  array_unshift($authors, $post_author_object);

  if (!$authors) return null;

  // If delimiter is provided, return email string
  if ($delimiter) {
    // Extract user emails and sanitize them
    $user_emails = array_map(function($author) {
      $email = $author['user_email'] ?? '';
      return is_email($email) ? sanitize_email($email) : '';
    }, $authors);

    // Remove empty emails and convert to a delimited string
    $user_emails = array_filter($user_emails);
    $user_emails_string = implode($delimiter, $user_emails);

    return $user_emails_string;
  }

  // Return the full authors array
  return $authors;
}

/**
 * Gets the playlist slug or ID for a media file attachment
 *
 * This function retrieves the playlist taxonomy term associated with a media
 * attachment and returns either the slug or term ID based on the return parameter.
 * Useful for organizing media files into playlists.
 *
 * @param int $attachment_id The ID of the media attachment
 * @param string $return What to return: 'slug' for term slug, 'id' for term ID
 * @return string|int|false The playlist slug/ID or false if no playlist found
 *
 * @since 1.0.0
 *
 * @example $playlist_slug = baum_get_media_playlist($attachment_id, 'slug');
 * @example $playlist_id = baum_get_media_playlist($attachment_id, 'id');
 */
function baum_get_media_playlist ($attachment_id, $return = 'slug') {
  // Validate attachment ID
  $attachment_id = absint($attachment_id);
  if (!$attachment_id) {
    return false;
  }

  // Get playlist terms for the attachment
  $terms = wp_get_post_terms($attachment_id, 'playlist');

  if (is_wp_error($terms) || empty($terms)) {
      return false;
  }

  // Return the requested format
  return ($return === 'id') ? absint($terms[0]->term_id) : sanitize_title($terms[0]->slug);
}

//
//
//

// function baum_recalculate_playlist_counts() {
//   $taxonomies = ['playlist']; // Add more if needed
//   foreach ($taxonomies as $taxonomy) {
//       $terms = get_terms(['taxonomy' => $taxonomy, 'hide_empty' => false]);
//       foreach ($terms as $term) {
//           wp_update_term_count_now([$term->term_id], $taxonomy);
//       }
//   }
// }

// add_action('admin_init', 'baum_recalculate_playlist_counts');

//
//
//

// function enqueue_leaflet_assets() {
//   // Leaflet CSS and JavaScript
//   wp_enqueue_style('leaflet-css', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css');
//   wp_enqueue_script('leaflet-js', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js', array(), null, true);
//   // Esri Leaflet Plugin
//   wp_enqueue_script('esri-leaflet', 'https://unpkg.com/esri-leaflet@3.0.10/dist/esri-leaflet.js', array('leaflet-js'), null, true);
// }

// add_action('wp_enqueue_scripts', 'enqueue_leaflet_assets');

/**
 * Email Endpoint Functionality
 *
 * Sets up the email endpoint system for rendering posts/categories
 * optimized for email delivery at domain.com/slug/email
 */

// Include email endpoint functionality
require_once get_template_directory() . '/email-endpoint.php';

/**
 * Adds email endpoint to WordPress rewrite rules
 *
 * This function registers the /email endpoint that can be appended
 * to any post, page, or category URL to get email-optimized content.
 *
 * @return void
 */
function add_email_endpoint() {
  add_rewrite_endpoint('email', EP_ALL);
}
add_action('init', 'add_email_endpoint');

/**
 * Handles email endpoint requests
 *
 * This function intercepts requests to the /email endpoint and
 * renders the appropriate email-optimized content.
 *
 * @return void
 */
function handle_email_endpoint_request() {
  global $wp_query;

  if (isset($wp_query->query_vars['email'])) {
    $wp_query->query_vars['email_endpoint'] = true;
    handle_email_endpoint();
  }
}
add_action('template_redirect', 'handle_email_endpoint_request');

/**
 * Flushes rewrite rules when theme is activated
 *
 * This ensures the email endpoint is properly registered
 * when the theme is first activated.
 *
 * @return void
 */
function flush_email_endpoint_rules() {
  add_email_endpoint();
  flush_rewrite_rules();
}
add_action('after_switch_theme', 'flush_email_endpoint_rules');

//
//
//

/**
 * Creates a Leaflet weather map shortcode with multiple weather layers
 *
 * This function generates an interactive weather map using Leaflet.js with
 * OpenWeatherMap data layers and NASA satellite imagery. It includes toggleable
 * weather layers for clouds, precipitation, wind, pressure, temperature, and snow.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the interactive weather maps
 *
 * @since 1.0.0
 *
 * @example [leaflet_weather_map]
 */
function baum_leaflet_unified_weather_shortcode($atts) {
  $api_key = '140135e33fa723259cf21dbe1c630fff'; // Replace with your actual key
  ob_start();
  ?>
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script src="https://unpkg.com/leaflet-openweathermap/leaflet-openweathermap.js"></script>
  <div id="map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  var map = L.map('map').setView([37.8, -96], 4);
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '&copy; OpenStreetMap'
  }).addTo(map);
  // Add NASA GIBS MODIS Terra imagery
  L.tileLayer('https://gibs.earthdata.nasa.gov/wmts/epsg3857/best/MODIS_Terra_CorrectedReflectance_TrueColor/default/2023-08-01/GoogleMapsCompatible_Level9/{z}/{y}/{x}.jpg', {
    attribution: 'NASA GIBS / MODIS Terra',
    tileSize: 256,
    opacity: 0.6,
    maxZoom: 9, // MODIS only goes to level 9
    minZoom: 1,
    noWrap: true
  }).addTo(map);
  </script>
  <div id="leaflet-weather-map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') return;
    var map = L.map('leaflet-weather-map').setView([39.8283, -98.5795], 4);
    var base = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {}).addTo(map);
    L.tileLayer('https://gibs.earthdata.nasa.gov/wmts/epsg3857/best/VIIRS_SNPP_CorrectedReflectance_TrueColor/default/2023-03-01/250m/{z}/{y}/{x}.jpg', { attribution: 'NASA GIBS', opacity: 0.6 }).addTo(map);

    // OpenWeatherMap layers
    var clouds = L.tileLayer('https://tile.openweathermap.org/map/clouds_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      opacity: 0.9
    });

    var precipitation = L.tileLayer('https://tile.openweathermap.org/map/precipitation_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    var wind = L.tileLayer('https://tile.openweathermap.org/map/wind_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    var pressure = L.tileLayer('https://tile.openweathermap.org/map/pressure_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    var temp = L.tileLayer('https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    var snow = L.tileLayer('https://tile.openweathermap.org/map/snow/{z}/{x}/{y}.png?appid=<?php echo esc_attr($api_key); ?>', {
      attribution: '&copy; OpenWeatherMap', opacity: 0.5
    });

    // Layer toggles
    var overlays = {
      "Clouds": clouds,
      "Precipitation": precipitation,
      "Wind": wind,
      "Pressure": pressure,
      "Temperature": temp,
      "Snow": snow
    };

    L.control.layers(null, overlays, { collapsed: false }).addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

add_shortcode('leaflet_weather_map', 'baum_leaflet_unified_weather_shortcode');

//
//
//

/**
 * Creates a basic Leaflet weather map with cloud overlay (deprecated)
 *
 * This function generates a simple weather map using Leaflet.js with
 * OpenWeatherMap cloud data. This is a simpler version compared to the
 * unified weather map shortcode. Currently commented out.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the weather map
 *
 * @since 1.0.0
 * @deprecated Use baum_leaflet_unified_weather_shortcode instead
 */
function leaflet_weather_map_shortcode ($atts) {
  ob_start();
  ?>
  <div id="weather-map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      return;
    }
    var map = L.map('weather-map').setView([39.8283, -98.5795], 4);
    // Base Layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    // Add OpenWeatherMap Clouds layer
    L.OWM.clouds({ opacity: 0.5, appId: '140135e33fa723259cf21dbe1c630fff' }).addTo(map);

    // // Radar Layer
    // var radarLayer = L.tileLayer.wms('https://opengeo.ncep.noaa.gov/geoserver/ows?', {
    //     layers: 'nexrad_base_reflectivity',
    //     format: 'image/png',
    //     transparent: true,
    //     opacity: 0.5,
    //     attribution: 'NOAA NWS'
    // });

    // // Smoke Layer
    // var smokeLayer = L.esri.dynamicMapLayer({
    //     url: 'https://services.arcgisonline.com/arcgis/rest/services/USA_Wildfires/MapServer',
    //     opacity: 0.5,
    //     attribution: 'Esri'
    // });

    // // Temperature Layer
    // var tempLayer = L.tileLayer.wms('https://digital.weather.gov/ndfd/ndfd_wms.cgi?', {
    //     layers: 'maxt',
    //     format: 'image/png',
    //     transparent: true,
    //     opacity: 0.5,
    //     attribution: 'NOAA NWS'
    // });

    // // Cloud Cover Layer
    // var cloudLayer = L.tileLayer.wms('https://digital.weather.gov/ndfd/ndfd_wms.cgi?', {
    //     layers: 'sky',
    //     format: 'image/png',
    //     transparent: true,
    //     opacity: 0.5,
    //     attribution: 'NOAA NWS'
    // });

    // // Layer Control
    // var overlayMaps = {
    //     "Radar": radarLayer,
    //     "Smoke": smokeLayer,
    //     "Temperature": tempLayer,
    //     "Cloud Cover": cloudLayer
    // };

    L.control.layers(null, overlayMaps, { collapsed: false }).addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_weather_map', 'leaflet_weather_map_shortcode');

//
//
//

/**
 * Creates a Leaflet map with NOAA radar overlay
 *
 * This function generates an interactive map displaying NOAA weather radar data
 * using the CONUS base reflectivity layer. Shows precipitation and storm activity
 * across the continental United States.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the radar map
 *
 * @since 1.0.0
 *
 * @example [leaflet_radar_animation]
 */
function leaflet_radar_animation_shortcode($atts) {
  ob_start();
  ?>
  <div id="radar-animation-map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      return;
    }
    var map = L.map('radar-animation-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    var radarLayer = L.tileLayer.wms('https://opengeo.ncep.noaa.gov/geoserver/conus/conus_bref_qcd/ows?', {
      layers: 'conus_bref_qcd',
      format: 'image/png',
      transparent: true,
      opacity: 0.5,
      attribution: 'NOAA NWS',
      version: '1.3.0'
    });
    radarLayer.addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_radar_animation', 'leaflet_radar_animation_shortcode');

//
//
//

/**
 * Creates a Leaflet map with smoke overlay from NOAA
 *
 * This function generates an interactive map displaying smoke data from NOAA NCDC
 * using Esri Leaflet. Useful for tracking wildfire smoke and air quality conditions.
 * Requires Esri Leaflet plugin to be loaded.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the smoke overlay map
 *
 * @since 1.0.0
 *
 * @example [leaflet_smoke_overlay]
 */
function leaflet_smoke_overlay_shortcode($atts) {
  ob_start();
  ?>
  <div id="smoke-overlay-map" style="height: 500px;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined' || typeof L.esri === 'undefined') {
      console.error('Leaflet or Esri Leaflet not loaded.');
      return;
    }
    var map = L.map('smoke-overlay-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    var smokeLayer = L.esri.dynamicMapLayer({
      url: 'https://gis.ncdc.noaa.gov/arcgis/rest/services/cdo/smoke/MapServer',
      opacity: 0.5,
      attribution: 'NOAA NCDC'
    });
    smokeLayer.addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_smoke_overlay', 'leaflet_smoke_overlay_shortcode');

/**
 * Creates a Leaflet map with temperature overlay from NOAA
 *
 * This function generates an interactive map displaying maximum temperature data
 * from NOAA's National Digital Forecast Database (NDFD). Shows temperature
 * forecasts across the United States.
 *
 * @param array $atts Shortcode attributes (currently unused)
 * @return string HTML output containing the temperature map
 *
 * @since 1.0.0
 *
 * @example [leaflet_temperature_map]
 */
function leaflet_temperature_map_shortcode($atts) {
  ob_start();
  ?>
  <div id="temperature-map" style="height: 500px;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      return;
    }
    var map = L.map('temperature-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    var tempLayer = L.tileLayer.wms('https://digital.weather.gov/ndfd/ndfd_wms.cgi?', {
      layers: 'maxt',
      format: 'image/png',
      transparent: true,
      opacity: 0.5,
      attribution: 'NOAA NWS'
    });
    tempLayer.addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_temperature_map', 'leaflet_temperature_map_shortcode');

//
//
//

function leaflet_cloud_cover_map_shortcode($atts) {
  ob_start();
  ?>
  <div id="cloud-cover-map" style="height: 500px;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      return;
    }
    var map = L.map('cloud-cover-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    var cloudLayer = L.tileLayer.wms('https://www.ncei.noaa.gov/thredds/wms/nwm/nwm_cloud_cover.nc', {
      layers: 'Total_cloud_cover_entire_atmosphere_single_layer',
      format: 'image/png',
      transparent: true,
      opacity: 0.5,
      attribution: 'NOAA NCEI'
    });
    cloudLayer.addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_cloud_cover_map', 'leaflet_cloud_cover_map_shortcode');

//
//
//

// function enqueue_esri_leaflet() {
//   wp_enqueue_script('esri-leaflet', 'https://unpkg.com/esri-leaflet/dist/esri-leaflet.js', array('leaflet'), null, true);
// }

// add_action('wp_enqueue_scripts', 'enqueue_esri_leaflet');

//
//
//

// function baum_enqueue_leaflet_assets() {
//   // Leaflet core
//   wp_enqueue_style('leaflet-css', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css');
//   wp_enqueue_script('leaflet-js', 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js', array(), null, true);
//   // Esri Leaflet plugin
//   wp_enqueue_script('esri-leaflet', 'https://unpkg.com/esri-leaflet@3.0.10/dist/esri-leaflet.js', array('leaflet-js'), null, true);
// }

// add_action('wp_enqueue_scripts', 'baum_enqueue_leaflet_assets');

//
//
//

function leaflet_weather_map($atts) {
  ob_start();
  ?>
  <div id="weather-map" style="height:500px;width:100%;border-radius:var(--border-radius);margin:10px 0;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      sleep(2500);
    }
    var map = L.map('weather-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    // var radarLayer = L.tileLayer.wms('https://opengeo.ncep.noaa.gov/geoserver/conus/conus_bref_qcd/ows?', {
    //     layers: 'conus_bref_qcd',
    //     format: 'image/png',
    //     transparent: true,
    //     attribution: 'NOAA NWS'
    // });
    // radarLayer.addTo(map);

    var radarLayer = L.tileLayer.wms('https://opengeo.ncep.noaa.gov/geoserver/conus/conus_bref_qcd/ows?', {
      layers: 'conus_bref_qcd',
      format: 'image/png8', // crucial for transparency!
      transparent: true,    // forces alpha channel
      opacity: 0.6,
      attribution: 'NOAA NWS',
      version: '1.3.0',
      uppercase: true       // optional; NOAA WMS sometimes needs this
    });

    radarLayer.addTo(map);

    // https://services.arcgisonline.com/arcgis/rest/services/World_Weather/MapServer
    // ☀️ Layer Reference (World_Weather)
    // Layer Index	Name
    // 0	Precipitation
    // 1	Wind Speed
    // 2	Surface Pressure
    // 3	Relative Humidity
    // 4	Temperature
    // 5	Cloud Cover

    // L.esri.dynamicMapLayer({
    //     url: 'https://services.arcgisonline.com/arcgis/rest/services/World_Weather/MapServer',
    //     opacity: 0.7,
    //     layers: [4] // Clouds
    // }).addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_weather', 'leaflet_weather_map');

//
//
//

function leaflet_air_quality_map($atts) {
  ob_start();
  ?>
  <div id="air-quality-map" style="height: 500px;"></div>
  <script>
    var map = L.map('air-quality-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    L.esri.featureLayer({
      url: 'https://services.arcgis.com/your_service_url/arcgis/rest/services/Air_Quality/FeatureServer/0'
    }).addTo(map);
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_air_quality', 'leaflet_air_quality_map');

//
//
//

function baum_leaflet_wildfire_map_shortcode($atts) {
  ob_start();
  ?>
  <div id="wildfire-map" style="height: 500px;"></div>
  <script>
  document.addEventListener('DOMContentLoaded', function () {
    if (typeof L === 'undefined') {
      console.error('Leaflet not loaded');
      sleep(2500);
    }
    var map = L.map('wildfire-map').setView([39.8283, -98.5795], 4);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);
    L.esri.featureLayer({
      url: 'https://services3.arcgis.com/T4QMspbfLg3qTGWY/arcgis/rest/services/USA_WildfireActivity/FeatureServer/0'
    }).addTo(map);
  });
  </script>
  <?php
  return ob_get_clean();
}

// add_shortcode('leaflet_wildfire', 'baum_leaflet_wildfire_map_shortcode');

//
//
//

/**
 * Custom filter for WordPress heartbeat send data
 *
 * This function allows monitoring or modifying the data sent during WordPress
 * heartbeat requests. Currently used for optional monitoring purposes.
 *
 * @param array $response The heartbeat response data
 * @param array $data The heartbeat request data
 * @return array The potentially modified response data
 *
 * @since 1.0.0
 */
function custom_heartbeat_send_filter( $response, $data ) {
  // Optional: Modify or monitor heartbeat data
  return $response;
}

add_filter( 'heartbeat_send', 'custom_heartbeat_send_filter', 10, 2 );

/**
 * Customizes WordPress heartbeat settings
 *
 * This function modifies the WordPress heartbeat interval from the default
 * 15 seconds to 60 seconds to reduce server load and improve performance.
 *
 * @param array $settings The current heartbeat settings
 * @return array Modified heartbeat settings with custom interval
 *
 * @since 1.0.0
 */
function custom_heartbeat_settings( $settings ) {
  // Change interval in seconds (default is 15)
  $settings['interval'] = 60; // One ping per minute
  return $settings;
}

add_filter( 'heartbeat_settings', 'custom_heartbeat_settings' );

/**
 * Renders star ratings as HTML with Font Awesome icons
 *
 * This function creates a visual star rating display using Font Awesome icons.
 * It rounds the score to the nearest integer and displays filled stars for
 * the rating and empty stars for the remainder.
 *
 * @param float $score The rating score (0-5)
 * @return string HTML output containing the star rating display
 *
 * @since 1.0.0
 */
function baumpress_render_stars ($score) {
  $stars = round(floatval($score)); // Ensure it's rounded to an integer
  $stars = max(0, min(5, $stars)); // Clamp between 0 and 5
  $output = '<span class="star-rating">';

  for ($i = 1; $i <= 5; $i++) {
    if ($i <= $stars) {
      $output .= '<span class="star full"><i class="fa-fw fa-solid fa-star gold"></i></span>';
    } else {
      $output .= '<span class="star empty"><i class="fa-fw fa-regular fa-star senary"></i></span>';
    }
  }
  $output .= '</span>';
  return $output;
}

/**
 * Displays star ratings with support for half stars
 *
 * This function creates a more detailed star rating display that supports
 * half stars for more precise rating representation. Uses Font Awesome icons
 * with different classes for full, half, and empty stars.
 *
 * @param float $rating The rating value (0-5, supports decimals)
 * @return string HTML output containing the star rating with half-star support
 *
 * @since 1.0.0
 */
function display_stars($rating) {
  $rating = floatval($rating);
  $rating = max(0, min(5, $rating)); // Clamp between 0 and 5

  $output = '';
  $full_stars = floor($rating);
  $half_star = ($rating - $full_stars) >= 0.5 ? true : false;
  $empty_stars = 5 - $full_stars - ($half_star ? 1 : 0);

  // Add full stars
  for ($i = 0; $i < $full_stars; $i++) {
    $output .= "<i class='fas fa-star gold'></i>";
  }

  // Add half star if needed
  if ($half_star) {
    $output .= "<i class='fas fa-star-half-alt gold'></i>";
  }

  // Add empty stars
  for ($i = 0; $i < $empty_stars; $i++) {
    $output .= "<i class='far fa-star senary'></i>";
  }

  return $output;
}


/**
 * Time Capsule Shortcode
 *
 * Creates a shortcode for easily adding Time Capsule components to posts and pages.
 * Usage: [time_capsule title="TITLE" subtitle="Subtitle" date="Date" content="Content" button_text="Button Text"]
 *
 * @param array $atts Shortcode attributes
 * @return string HTML output
 *
 * @since 1.0.0
 */
function baum_time_capsule_shortcode($atts) {
  // Set default attributes
  $atts = shortcode_atts([
    "title" => "TIME CAPSULE",
    "subtitle" => "From Britannica Book Of The Year",
    "date" => date("F j, Y"),
    "content" => "",
    "button_text" => "SHOW ANOTHER EVENT",
    "source" => "",
    "show_icon" => "true",
    "css_class" => "",
    "animate" => "false"
  ], $atts, "time_capsule");

  // Convert string booleans to actual booleans
  $atts["show_icon"] = ($atts["show_icon"] === "true");

  // Add animation class if requested
  if ($atts["animate"] === "true") {
    $atts["css_class"] .= " animate-in";
  }

  // Start output buffering
  ob_start();

  // Load the template part
  get_template_part("parts/baum-time-capsule", null, [
    "title" => $atts["title"],
    "subtitle" => $atts["subtitle"],
    "date" => $atts["date"],
    "content" => $atts["content"],
    "button_text" => $atts["button_text"],
    "source" => $atts["source"],
    "show_icon" => $atts["show_icon"],
    "css_class" => trim($atts["css_class"])
  ]);

  // Return the buffered content
  return ob_get_clean();
}

add_shortcode("time_capsule", "baum_time_capsule_shortcode");
