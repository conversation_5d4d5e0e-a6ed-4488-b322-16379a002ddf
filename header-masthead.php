<?php // auth_redirect(); ?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
<meta charset='<?php bloginfo('charset'); ?>'>
<meta name='viewport' id='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=3.0' />
<?php wp_head(); ?>
<?php
  if (is_category()) {
    include(get_template_directory() . '/schema/category.php');
  }
?>
</head>
<body <?php body_class('home'); ?>>
<?php $location = get_theme_mod('baum_breaking_news_location', 'above'); ?>
<?php if ($location == 'above') { ?>
  <?php get_template_part('parts/baum-breaking', 'news'); ?>
<?php } ?>
<div id='baum-main'>

  <?php get_template_part('parts/baum-header', 'categories'); ?>

  <nav class='navbar navbar-3 navbar-home navbar-masthead'>
    <div class='navbar-masthead-logo'>
      <?php
        get_template_part('parts/baum', 'logo', [
          'date' => true
        ]);
      ?>
    </div>
  </nav>
  <nav id='navbar' class='navbar navbar-3 navbar-home'>
    <div class='navbar-top container'>
      <div class='navbar-left navbar-left-logo'>
        <?php
          wp_nav_menu([
            'theme_location' => 'apple-style-menu',
            'menu_class'     => 'baum-apple-menu',
          ]);
        ?>
        <?php
          // wp_nav_menu([
          //   'theme_location' => 'main-menu-left',
          //   'fallback_cb' => 'false'
          // ]);
        ?>
      </div>
      <div class='navbar-center'>
        <ul class='navbar-list'>
          <?php get_search_form(); ?>
        </ul>
      </div>
      <div class='navbar-right'>
        <?php
          wp_nav_menu([
            'theme_location' => 'main-menu-right',
            'fallback_cb' => 'false',
            'menu_class'     => 'baum-apple-menu baum-apple-menu-right',
          ]);
        ?>
      </div>
    </div>
  </nav>

  <div style='width:100%; height:5px;'></div>
  <?php if ($location == 'below') { ?>
    <?php get_template_part('parts/baum-breaking', 'news'); ?>
  <?php } ?>