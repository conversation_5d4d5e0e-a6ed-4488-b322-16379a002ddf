<?php
/**
 * Template Name: Skeleton Loaders Demo
 *
 * Demonstrates all available skeleton loaders in the BaumPress theme.
 * This page showcases the skeleton loading system for various UI components.
 *
 * @package BaumPress
 * @since 1.0.0
 */

get_header();
?>

<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 40px 20px;">

  <div style="text-align: center; margin-bottom: 60px;">
    <h1 style="color: var(--color-body-text); font-size: 36px; font-weight: 700; margin-bottom: 12px;">
      🎭 Skeleton Loaders Demo
    </h1>
    <p style="color: #666; font-size: 18px; max-width: 600px; margin: 0 auto;">
      Comprehensive skeleton loading system for smooth UX transitions.
      These loaders prevent layout shifts and provide visual feedback during content loading.
    </p>
  </div>

  <!-- Category Header Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📂 Category Header Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Used in category navigation headers while JavaScript adjusts responsive layout.
    </p>
    <?php echo baum_get_category_header_skeleton(); ?>
  </section>

  <!-- Card Skeletons -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      🃏 Card Skeletons
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Perfect for article cards, user profiles, and content previews.
    </p>
    <div style="display: grid; gap: 20px;">
      <?php echo baum_get_card_skeleton(3); ?>
    </div>
  </section>

  <!-- Text Skeletons -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📝 Text Skeletons
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Various text placeholder styles for titles, subtitles, and content lines.
    </p>

    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); margin-bottom: 20px;">
      <h3 style="margin-bottom: 15px; color: var(--color-body-text);">Title & Subtitle</h3>
      <?php echo baum_get_text_skeleton(1, 'title'); ?>
      <?php echo baum_get_text_skeleton(1, 'subtitle'); ?>
    </div>

    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
      <h3 style="margin-bottom: 15px; color: var(--color-body-text);">Content Lines</h3>
      <?php echo baum_get_text_skeleton(6, 'line'); ?>
    </div>
  </section>

  <!-- Button Skeletons -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      🔘 Button Skeletons
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Different button sizes for various UI contexts.
    </p>

    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
      <div style="margin-bottom: 20px;">
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Small Buttons</h4>
        <?php echo baum_get_button_skeleton('small', 3); ?>
      </div>

      <div style="margin-bottom: 20px;">
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Default Buttons</h4>
        <?php echo baum_get_button_skeleton('default', 3); ?>
      </div>

      <div>
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Large Buttons</h4>
        <?php echo baum_get_button_skeleton('large', 2); ?>
      </div>
    </div>
  </section>

  <!-- Navigation Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      🧭 Navigation Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      For main navigation menus and breadcrumbs.
    </p>
    <?php echo baum_get_nav_skeleton(5); ?>
  </section>

  <!-- Form Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📋 Form Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Perfect for contact forms, login screens, and data entry forms.
    </p>

    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); max-width: 500px;">
      <?php echo baum_get_form_skeleton(4); ?>
    </div>
  </section>

  <!-- Image Skeletons -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      🖼️ Image Skeletons
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      For featured images, avatars, and media content.
    </p>

    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
      <div>
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Rectangle</h4>
        <?php echo baum_get_image_skeleton('100%', '150px'); ?>
      </div>
      <div>
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Square</h4>
        <?php echo baum_get_image_skeleton('100%', '150px'); ?>
      </div>
      <div>
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Avatar</h4>
        <?php echo baum_get_image_skeleton('80px', '80px', true); ?>
      </div>
    </div>
  </section>

  <!-- List Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📋 List Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      For menu items, article lists, and navigation lists.
    </p>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
      <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
        <h4 style="margin-bottom: 15px; color: var(--color-body-text);">With Bullets</h4>
        <?php echo baum_get_list_skeleton(5, true); ?>
      </div>

      <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
        <h4 style="margin-bottom: 15px; color: var(--color-body-text);">Without Bullets</h4>
        <?php echo baum_get_list_skeleton(5, false); ?>
      </div>
    </div>
  </section>

  <!-- Table Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📊 Table Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      For data tables, pricing tables, and structured content.
    </p>

    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
      <?php echo baum_get_table_skeleton(6, 4); ?>
    </div>
  </section>

  <!-- Complete Page Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📄 Complete Page Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      A full page layout combining multiple skeleton elements.
    </p>

    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
      <?php echo baum_get_page_skeleton(); ?>
    </div>
  </section>

  <!-- JavaScript Demo -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      ⚡ JavaScript Utilities Demo
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Interactive skeleton loaders controlled by JavaScript.
    </p>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
      <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
        <h4 style="margin-bottom: 15px; color: var(--color-body-text);">Demo Content</h4>
        <div id="demo-content" style="min-height: 120px;">
          <h5 style="color: var(--color-body-text); margin-bottom: 10px;">Sample Article Title</h5>
          <p style="color: #666; line-height: 1.6;">
            This is some sample content that will be replaced with skeleton loaders when you click the buttons.
            The skeleton system provides smooth loading states for better user experience.
          </p>
          <button style="padding: 8px 16px; background: var(--color-secondary); color: white; border: none; border-radius: 6px; cursor: pointer;">
            Read More
          </button>
        </div>
      </div>

      <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
        <h4 style="margin-bottom: 15px; color: var(--color-body-text);">Controls</h4>
        <div style="display: grid; gap: 10px;">
          <button onclick="BaumSkeleton.show('#demo-content', 'text', {lines: 4})" style="padding: 10px 15px; background: #007cba; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
            Show Text Skeleton
          </button>
          <button onclick="BaumSkeleton.show('#demo-content', 'card')" style="padding: 10px 15px; background: #46b450; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
            Show Card Skeleton
          </button>
          <button onclick="BaumSkeleton.hide('#demo-content')" style="padding: 10px 15px; background: #dc3232; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
            Hide Skeleton
          </button>
          <button onclick="BaumSkeleton.toggle('#demo-content', 'text')" style="padding: 10px 15px; background: #ffb900; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px;">
            Toggle Skeleton
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Usage Instructions -->
  <section style="margin-bottom: 60px; background: #f8f9fa; padding: 40px; border-radius: 12px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      💡 Usage Instructions
    </h2>

    <div style="display: grid; gap: 20px;">
      <div>
        <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin-bottom: 10px;">
          PHP Functions
        </h3>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 14px;"><code><?php echo htmlspecialchars('<?php echo baum_get_category_header_skeleton(); ?>
<?php echo baum_get_card_skeleton(3); ?>
<?php echo baum_get_text_skeleton(5, "line"); ?>
<?php echo baum_get_button_skeleton("large", 2); ?>'); ?></code></pre>
      </div>

      <div>
        <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin-bottom: 10px;">
          JavaScript API
        </h3>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 14px;"><code>// Show skeleton
BaumSkeleton.show('#element', 'text', {lines: 3});

// Hide skeleton
BaumSkeleton.hide('#element');

// Toggle skeleton
BaumSkeleton.toggle('#element', 'card');

// Show while promise is pending
BaumSkeleton.showWhilePending('#element', fetchData());

// jQuery plugin (if available)
$('#element').skeleton('show', 'text');</code></pre>
      </div>

      <div>
        <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin-bottom: 10px;">
          CSS Classes
        </h3>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 14px;"><code>.baum-skeleton
.baum-skeleton-category-header
.baum-skeleton-card
.baum-skeleton-text
.baum-skeleton-button
.baum-skeleton-fade-in</code></pre>
      </div>

      <div>
        <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin-bottom: 10px;">
          Data Attributes
        </h3>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 14px;"><code>&lt;div data-skeleton="text" data-skeleton-lines="3"&gt;
  Content will be replaced with skeleton
&lt;/div&gt;

&lt;div data-skeleton="card" data-skeleton-auto-hide&gt;
  Auto-hide after 3 seconds
&lt;/div&gt;</code></pre>
      </div>
    </div>
  </section>

</div>

<script>
// Demo: Show skeleton loading effect
document.addEventListener('DOMContentLoaded', function() {
  // Add a button to toggle dark mode for skeleton demo
  const toggleBtn = document.createElement('button');
  toggleBtn.textContent = '🌙 Toggle Dark Mode';
  toggleBtn.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: #333;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  `;

  toggleBtn.addEventListener('click', function() {
    document.body.classList.toggle('dark-mode');
    toggleBtn.textContent = document.body.classList.contains('dark-mode') ?
      '☀️ Toggle Light Mode' : '🌙 Toggle Dark Mode';
  });

  document.body.appendChild(toggleBtn);
});
</script>

<?php get_footer(); ?>
