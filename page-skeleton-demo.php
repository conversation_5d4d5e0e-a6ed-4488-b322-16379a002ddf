<?php
/**
 * Template Name: Skeleton Loaders Demo
 * 
 * Demonstrates all available skeleton loaders in the BaumPress theme.
 * This page showcases the skeleton loading system for various UI components.
 * 
 * @package BaumPress
 * @since 1.0.0
 */

get_header();
?>

<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 40px 20px;">
  
  <div style="text-align: center; margin-bottom: 60px;">
    <h1 style="color: var(--color-body-text); font-size: 36px; font-weight: 700; margin-bottom: 12px;">
      🎭 Skeleton Loaders Demo
    </h1>
    <p style="color: #666; font-size: 18px; max-width: 600px; margin: 0 auto;">
      Comprehensive skeleton loading system for smooth UX transitions. 
      These loaders prevent layout shifts and provide visual feedback during content loading.
    </p>
  </div>

  <!-- Category Header Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📂 Category Header Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Used in category navigation headers while JavaScript adjusts responsive layout.
    </p>
    <?php echo baum_get_category_header_skeleton(); ?>
  </section>

  <!-- Card Skeletons -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      🃏 Card Skeletons
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Perfect for article cards, user profiles, and content previews.
    </p>
    <div style="display: grid; gap: 20px;">
      <?php echo baum_get_card_skeleton(3); ?>
    </div>
  </section>

  <!-- Text Skeletons -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📝 Text Skeletons
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Various text placeholder styles for titles, subtitles, and content lines.
    </p>
    
    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); margin-bottom: 20px;">
      <h3 style="margin-bottom: 15px; color: var(--color-body-text);">Title & Subtitle</h3>
      <?php echo baum_get_text_skeleton(1, 'title'); ?>
      <?php echo baum_get_text_skeleton(1, 'subtitle'); ?>
    </div>

    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
      <h3 style="margin-bottom: 15px; color: var(--color-body-text);">Content Lines</h3>
      <?php echo baum_get_text_skeleton(6, 'line'); ?>
    </div>
  </section>

  <!-- Button Skeletons -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      🔘 Button Skeletons
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Different button sizes for various UI contexts.
    </p>
    
    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
      <div style="margin-bottom: 20px;">
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Small Buttons</h4>
        <?php echo baum_get_button_skeleton('small', 3); ?>
      </div>
      
      <div style="margin-bottom: 20px;">
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Default Buttons</h4>
        <?php echo baum_get_button_skeleton('default', 3); ?>
      </div>
      
      <div>
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Large Buttons</h4>
        <?php echo baum_get_button_skeleton('large', 2); ?>
      </div>
    </div>
  </section>

  <!-- Navigation Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      🧭 Navigation Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      For main navigation menus and breadcrumbs.
    </p>
    <?php echo baum_get_nav_skeleton(5); ?>
  </section>

  <!-- Form Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📋 Form Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      Perfect for contact forms, login screens, and data entry forms.
    </p>
    
    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); max-width: 500px;">
      <?php echo baum_get_form_skeleton(4); ?>
    </div>
  </section>

  <!-- Image Skeletons -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      🖼️ Image Skeletons
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      For featured images, avatars, and media content.
    </p>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
      <div>
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Rectangle</h4>
        <?php echo baum_get_image_skeleton('100%', '150px'); ?>
      </div>
      <div>
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Square</h4>
        <?php echo baum_get_image_skeleton('100%', '150px'); ?>
      </div>
      <div>
        <h4 style="margin-bottom: 10px; color: var(--color-body-text);">Avatar</h4>
        <?php echo baum_get_image_skeleton('80px', '80px', true); ?>
      </div>
    </div>
  </section>

  <!-- List Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📋 List Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      For menu items, article lists, and navigation lists.
    </p>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
      <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
        <h4 style="margin-bottom: 15px; color: var(--color-body-text);">With Bullets</h4>
        <?php echo baum_get_list_skeleton(5, true); ?>
      </div>
      
      <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
        <h4 style="margin-bottom: 15px; color: var(--color-body-text);">Without Bullets</h4>
        <?php echo baum_get_list_skeleton(5, false); ?>
      </div>
    </div>
  </section>

  <!-- Table Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📊 Table Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      For data tables, pricing tables, and structured content.
    </p>
    
    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
      <?php echo baum_get_table_skeleton(6, 4); ?>
    </div>
  </section>

  <!-- Complete Page Skeleton -->
  <section style="margin-bottom: 60px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      📄 Complete Page Skeleton
    </h2>
    <p style="color: #666; margin-bottom: 20px;">
      A full page layout combining multiple skeleton elements.
    </p>
    
    <div style="background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
      <?php echo baum_get_page_skeleton(); ?>
    </div>
  </section>

  <!-- Usage Instructions -->
  <section style="margin-bottom: 60px; background: #f8f9fa; padding: 40px; border-radius: 12px;">
    <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin-bottom: 20px;">
      💡 Usage Instructions
    </h2>
    
    <div style="display: grid; gap: 20px;">
      <div>
        <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin-bottom: 10px;">
          PHP Functions
        </h3>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 14px;"><code><?php echo htmlspecialchars('<?php echo baum_get_category_header_skeleton(); ?>
<?php echo baum_get_card_skeleton(3); ?>
<?php echo baum_get_text_skeleton(5, "line"); ?>
<?php echo baum_get_button_skeleton("large", 2); ?>'); ?></code></pre>
      </div>
      
      <div>
        <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin-bottom: 10px;">
          CSS Classes
        </h3>
        <pre style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 14px;"><code>.baum-skeleton
.baum-skeleton-category-header
.baum-skeleton-card
.baum-skeleton-text
.baum-skeleton-button
.baum-skeleton-fade-in</code></pre>
      </div>
    </div>
  </section>

</div>

<script>
// Demo: Show skeleton loading effect
document.addEventListener('DOMContentLoaded', function() {
  // Add a button to toggle dark mode for skeleton demo
  const toggleBtn = document.createElement('button');
  toggleBtn.textContent = '🌙 Toggle Dark Mode';
  toggleBtn.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: #333;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  `;
  
  toggleBtn.addEventListener('click', function() {
    document.body.classList.toggle('dark-mode');
    toggleBtn.textContent = document.body.classList.contains('dark-mode') ? 
      '☀️ Toggle Light Mode' : '🌙 Toggle Dark Mode';
  });
  
  document.body.appendChild(toggleBtn);
});
</script>

<?php get_footer(); ?>
