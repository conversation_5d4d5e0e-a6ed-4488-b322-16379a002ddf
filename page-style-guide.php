<?php
/**
 * Template Name: Style Guide
 *
 * This page displays a comprehensive style guide showing all BaumPress
 * design elements including typography, buttons, forms, cards, and components.
 *
 * @package BaumPress
 * @since 1.0.0
 */

get_header(); ?>

<div style="width: 100%; max-width: 1110px; margin: 0 auto; padding: 20px;">
  <div style="width: 100%;">
    <div style="width: 100%;">

      <h1 style="font-size: 4rem; font-weight: 900; margin-bottom: 20px;">BaumPress Style Guide</h1>
      <p>This page showcases all the design elements and components available in the BaumPress theme.</p>

      <!-- =================================================================
           COLOR PALETTE
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Color Palette</h2>

        <h3 style="color: var(--color-body-text);">Theme Colors</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; margin-bottom: 30px;">
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-primary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Primary</strong><br>
            <code style="font-size: 10px;">--color-primary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-secondary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Secondary</strong><br>
            <code style="font-size: 10px;">--color-secondary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-tertiary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Tertiary</strong><br>
            <code style="font-size: 10px;">--color-tertiary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-quaternary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Quaternary</strong><br>
            <code style="font-size: 10px;">--color-quaternary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-quinary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Quinary</strong><br>
            <code style="font-size: 10px;">--color-quinary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-senary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Senary</strong><br>
            <code style="font-size: 10px;">--color-senary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-septenary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Septenary</strong><br>
            <code style="font-size: 10px;">--color-septenary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-octonary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Octonary</strong><br>
            <code style="font-size: 10px;">--color-octonary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-nonary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Nonary</strong><br>
            <code style="font-size: 10px;">--color-nonary</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-denary); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Denary</strong><br>
            <code style="font-size: 10px;">--color-denary</code>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Brand Colors</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-red); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Red</strong><br>
            <code style="font-size: 10px;">--color-red</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-orange); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Orange</strong><br>
            <code style="font-size: 10px;">--color-orange</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-yellow); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Yellow</strong><br>
            <code style="font-size: 10px;">--color-yellow</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-green); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Green</strong><br>
            <code style="font-size: 10px;">--color-green</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-blue); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Blue</strong><br>
            <code style="font-size: 10px;">--color-blue</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-purple); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Purple</strong><br>
            <code style="font-size: 10px;">--color-purple</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-pink); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Pink</strong><br>
            <code style="font-size: 10px;">--color-pink</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-teal); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Teal</strong><br>
            <code style="font-size: 10px;">--color-teal</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-gold); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Gold</strong><br>
            <code style="font-size: 10px;">--color-gold</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-silver); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Silver</strong><br>
            <code style="font-size: 10px;">--color-silver</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-bronze); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Bronze</strong><br>
            <code style="font-size: 10px;">--color-bronze</code>
          </div>
          <div style="text-align: center;">
            <div style="width: 100%; height: 50px; background: var(--color-gray); border-radius: var(--border-radius); margin-bottom: 8px; border: 1px solid #ddd;"></div>
            <strong style="font-size: 12px;">Gray</strong><br>
            <code style="font-size: 10px;">--color-gray</code>
          </div>
        </div>
      </section>

      <!-- =================================================================
           ALERTS AND NOTIFICATIONS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Alerts and Notifications</h2>

        <h3 style="color: var(--color-body-text);">Alert Types</h3>
        <div style="display: flex; flex-direction: column; gap: 15px;">
          <div class="baum-notice notice-success" style="background: #d4edda; border-left: 4px solid #28a745; padding: 15px; border-radius: var(--border-radius); display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-check-circle" style="color: #28a745; font-size: 18px;"></i>
            <div><strong>Success:</strong> This is a success alert message.</div>
          </div>

          <div class="baum-notice notice-info" style="background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; border-radius: var(--border-radius); display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-info-circle" style="color: #17a2b8; font-size: 18px;"></i>
            <div><strong>Info:</strong> This is an informational alert message.</div>
          </div>

          <div class="baum-notice notice-warning" style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; border-radius: var(--border-radius); display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-exclamation-triangle" style="color: #ffc107; font-size: 18px;"></i>
            <div><strong>Warning:</strong> This is a warning alert message.</div>
          </div>

          <div class="baum-notice notice-danger" style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; border-radius: var(--border-radius); display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-times-circle" style="color: #dc3545; font-size: 18px;"></i>
            <div><strong>Error:</strong> This is an error alert message.</div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Breaking News Alert</h3>
        <div style="background: #dc3545; color: white; padding: 15px; border-radius: var(--border-radius); margin: 20px 0;">
          <div style="display: flex; align-items: center; gap: 10px;">
            <span style="background: white; color: #dc3545; padding: 4px 8px; border-radius: 4px; font-weight: bold; font-size: 12px;">BREAKING</span>
            <span>This is a breaking news alert that appears at the top of the page.</span>
          </div>
        </div>

      <!-- =================================================================
           TYPOGRAPHY
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text); font-size: 2.5rem; margin-bottom: 40px;">Typography</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px;">
          <!-- Left Column - Headings -->
          <div>
            <h3 style="color: var(--color-body-text); margin-bottom: 20px; border-bottom: 2px solid var(--color-secondary); padding-bottom: 8px;">Heading Hierarchy</h3>

            <h1 style="font-size: 3rem; font-weight: bold; color: var(--color-body-text); margin: 20px 0 10px 0; line-height: 1.2;">Heading 1 - Article Title</h1>
            <p style="color: #666; font-size: 14px; margin-bottom: 20px;">48px, Bold - Main content titles</p>

            <h2 style="color: var(--color-body-text); margin: 20px 0 10px 0;">Heading 2 - Section Title</h2>
            <p style="color: #666; font-size: 14px; margin-bottom: 20px;">32px, Semi-bold - Major sections</p>

            <h3 style="color: var(--color-body-text); margin: 20px 0 10px 0;">Heading 3 - Subsection Title</h3>
            <p style="color: #666; font-size: 14px; margin-bottom: 20px;">24px, Semi-bold - Subsections</p>

            <h4 style="color: var(--color-body-text); margin: 20px 0 10px 0;">Heading 4 - Section Header</h4>
            <p style="color: #666; font-size: 14px; margin-bottom: 20px;">20px, Medium - Content headers</p>

            <h5 style="color: var(--color-body-text); margin: 20px 0 10px 0;">Heading 5 - Card Title</h5>
            <p style="color: #666; font-size: 14px; margin-bottom: 20px;">18px, Medium - Card and component titles</p>

            <h6 style="color: var(--color-body-text); margin: 20px 0 10px 0;">Heading 6 - Small Title</h6>
            <p style="color: #666; font-size: 14px; margin-bottom: 20px;">16px, Medium - Small titles and labels</p>
          </div>

          <!-- Right Column - Text Styles -->
          <div>
            <h3 style="color: var(--color-body-text); margin-bottom: 20px; border-bottom: 2px solid var(--color-secondary); padding-bottom: 8px;">Text Styles & Elements</h3>

            <div style="margin-bottom: 25px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 10px;">Body Text</h4>
              <p style="margin-bottom: 10px;">This is regular paragraph text with <strong>bold text</strong>, <em>italic text</em>, and <a href="#" style="color: var(--color-primary); text-decoration: underline;">a link</a>. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
              <p style="color: #666; font-size: 14px;">16px, Regular - Standard body text</p>
            </div>

            <div style="margin-bottom: 25px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 10px;">Text Variations</h4>
              <p class="small" style="font-size: 14px; margin-bottom: 5px;">Small text for captions and metadata</p>
              <p class="smaller" style="font-size: 12px; margin-bottom: 5px;">Smaller text for fine print and disclaimers</p>
              <p style="color: #666; font-size: 14px;">14px & 12px - Secondary text sizes</p>
            </div>

            <div style="margin-bottom: 25px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 10px;">Code & Technical</h4>
              <p><code style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace;">Inline code example</code></p>
              <pre style="background: #f8f9fa; padding: 15px; border-radius: var(--border-radius); border-left: 4px solid var(--color-primary); overflow-x: auto; font-family: 'Courier New', monospace; font-size: 14px; margin: 10px 0;"><code>// Code block example
function example() {
  return "Hello World";
}</code></pre>
            </div>

            <div style="margin-bottom: 25px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 10px;">Quotes & Citations</h4>
              <blockquote style="border-left: 4px solid var(--color-secondary); padding-left: 20px; margin: 15px 0; font-style: italic; color: #555;">
                <p style="margin-bottom: 10px;">"This is a blockquote used for highlighting important quotes or excerpts from other sources."</p>
                <cite style="font-size: 14px; color: #888;">— Citation Source</cite>
              </blockquote>
            </div>
          </div>
        </div>

        <!-- Full Width Typography Examples -->
        <div style="margin-top: 40px;">
          <h3 style="color: var(--color-body-text); margin-bottom: 20px; border-bottom: 2px solid var(--color-secondary); padding-bottom: 8px;">Typography in Context</h3>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
            <!-- Article Example -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: var(--border-radius); border: 1px solid #e0e0e0;">
              <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Article Layout Example</h4>
              <h1 style="font-size: 2rem; margin-bottom: 10px; color: var(--color-body-text);">The Future of Web Design</h1>
              <p style="color: #666; font-size: 14px; margin-bottom: 15px;">Published on March 15, 2024 by John Doe</p>
              <p style="margin-bottom: 15px;">Web design continues to evolve with new technologies and user expectations. Modern websites must balance <strong>aesthetic appeal</strong> with <em>functional usability</em>.</p>
              <h2 style="margin: 20px 0 10px 0; color: var(--color-body-text);">Key Trends</h2>
              <p style="margin-bottom: 10px;">Several trends are shaping the future of web design:</p>
              <ul style="margin-left: 20px; margin-bottom: 15px; color: #666;">
                <li style="color: var(--color-body-text);">Minimalist interfaces</li>
                <li style="color: var(--color-body-text);">Dark mode support</li>
                <li style="color: var(--color-body-text);">Micro-interactions</li>
              </ul>
            </div>

            <!-- UI Component Example -->
            <div style="background: white; padding: 25px; border-radius: var(--border-radius); border: 1px solid #e0e0e0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
              <h4 style="color: var(--color-body-text); margin-bottom: 15px;">UI Component Example</h4>
              <h5 style="margin-bottom: 8px; color: var(--color-body-text);">User Profile</h5>
              <p style="font-size: 14px; color: #666; margin-bottom: 15px;">Manage your account settings</p>
              <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 15px;">
                <div style="width: 40px; height: 40px; background: var(--color-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">JD</div>
                <div>
                  <h6 style="margin: 0; color: var(--color-body-text);">John Doe</h6>
                  <p style="margin: 0; font-size: 12px; color: #888;"><EMAIL></p>
                </div>
              </div>
              <p style="font-size: 13px; color: #999;">Last login: 2 hours ago</p>
            </div>
          </div>
        </div>
      </section>

      <!-- =================================================================
           BUTTONS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Buttons</h2>

        <h3 style="color: var(--color-body-text);">Button Sizes</h3>
        <div style="margin-bottom: 30px; display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
          <button class="baum-button-huge">Huge Button</button>
          <button class="baum-button-large">Large Button</button>
          <button class="baum-button-medium">Medium Button</button>
          <button class="baum-button">Default Button</button>
          <button class="baum-button-small">Small Button</button>
          <button class="baum-button-micro">Micro Button</button>
        </div>

        <h3 style="color: var(--color-body-text);">Badges</h3>
        <div style="margin-bottom: 30px; display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
          <span class="baum-badge" style="letter-spacing: -0.5px; font-size: 13px;">Default Badge</span>
          <span class="baum-badge baum-badge-primary" style="letter-spacing: -0.5px; font-size: 13px;">Primary Badge</span>
          <span class="baum-badge baum-badge-success" style="letter-spacing: -0.5px; font-size: 13px;">Success Badge</span>
          <span class="baum-badge baum-badge-warning" style="letter-spacing: -0.5px; font-size: 13px;">Warning Badge</span>
          <span class="baum-badge baum-badge-danger" style="letter-spacing: -0.5px; font-size: 13px;">Danger Badge</span>
        </div>

        <h4 style="color: var(--color-body-text);">Badges with Icons</h4>
        <div style="margin-bottom: 30px; display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
          <span class="baum-badge" style="letter-spacing: -0.5px; font-size: 13px; background: var(--color-blue); color: white;">
            <i class="fas fa-user" style="margin-right: 4px;"></i>Author
          </span>
          <span class="baum-badge" style="letter-spacing: -0.5px; font-size: 13px; background: var(--color-teal); color: white;">
            <i class="fas fa-cloud" style="margin-right: 4px;"></i>Cloud
          </span>
          <span class="baum-badge" style="letter-spacing: -0.5px; font-size: 13px; background: var(--color-pink); color: white;">
            <i class="fas fa-heart" style="margin-right: 4px;"></i>Favorite
          </span>
          <span class="baum-badge" style="letter-spacing: -0.5px; font-size: 13px; background: var(--color-green); color: white;">
            <i class="fas fa-check" style="margin-right: 4px;"></i>Verified
          </span>
          <span class="baum-badge" style="letter-spacing: -0.5px; font-size: 13px; background: var(--color-orange); color: white;">
            <i class="fas fa-star" style="margin-right: 4px;"></i>Featured
          </span>
          <span class="baum-badge" style="letter-spacing: -0.5px; font-size: 13px; background: var(--color-purple); color: white;">
            <i class="fas fa-crown" style="margin-right: 4px;"></i>Premium
          </span>
        </div>

        <h3 style="color: var(--color-body-text);">Button States</h3>
        <div style="margin-bottom: 20px; display: flex; gap: 10px; align-items: center;">
          <button class="baum-button baum-button-large">Default Button</button>
          <input type="submit" value="Submit Button" class="baum-button baum-button-large" style="background: var(--color-secondary); color: white; border: none; font-size: 12px; line-height: 14px; letter-spacing: normal; border-radius: var(--border-radius); padding: 0 50px; font-weight: 900; text-decoration: none; display: inline-flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s ease;">
          <input type="reset" value="Reset Button" class="baum-button baum-button-large" style="background: var(--color-secondary); color: white; border: none; font-size: 12px; line-height: 14px; letter-spacing: normal; border-radius: var(--border-radius); padding: 0 50px; font-weight: 900; text-decoration: none; display: inline-flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s ease;">
          <a href="#" class="baum-button baum-button-large" style="text-decoration: none; display: inline-flex; align-items: center; justify-content: center;">Link Button</a>
        </div>

        <h3 style="color: var(--color-body-text);">Button Groups</h3>
        <div style="margin-bottom: 30px;">
          <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Connected Button Group (3-5 buttons)</h4>
          <div class="button-group" style="display: inline-flex; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <button class="baum-button" style="border-radius: 0; border-right: 1px solid rgba(255,255,255,0.2); margin: 0;">Left</button>
            <button class="baum-button" style="border-radius: 0; border-right: 1px solid rgba(255,255,255,0.2); margin: 0;">Center</button>
            <button class="baum-button" style="border-radius: 0; border-right: 1px solid rgba(255,255,255,0.2); margin: 0;">Middle</button>
            <button class="baum-button" style="border-radius: 0; border-right: 1px solid rgba(255,255,255,0.2); margin: 0;">Another</button>
            <button class="baum-button" style="border-radius: 0; margin: 0;">Right</button>
          </div>
        </div>

        <div style="margin-bottom: 30px;">
          <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Split Button with Dropdown</h4>
          <div class="split-button" style="display: inline-flex; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); position: relative;">
            <button class="baum-button" style="border-radius: 0; border-right: 1px solid rgba(255,255,255,0.2); margin: 0; padding: 0 20px;">
              Primary Action
            </button>
            <button class="baum-button dropdown-toggle" style="border-radius: 0; margin: 0; padding: 0 12px; position: relative;" onclick="toggleDropdown(this)">
              <i class="fas fa-chevron-down"></i>
            </button>
            <div class="dropdown-menu" style="position: absolute; top: 100%; right: 0; background: rgba(255,255,255,0.95); backdrop-filter: blur(20px); border: 1px solid rgba(0,0,0,0.1); border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.2); min-width: 180px; z-index: 1000; display: none; padding: 6px 0; margin-top: 4px;">
              <a href="#" style="display: flex; align-items: center; padding: 8px 16px; color: var(--color-body-text); text-decoration: none; font-size: 14px; font-weight: 500; transition: all 0.15s ease;" onmouseover="this.style.background='rgba(0,0,0,0.05)'; this.style.borderRadius='6px'; this.style.margin='0 6px'" onmouseout="this.style.background='transparent'; this.style.margin='0'">
                <i class="fas fa-edit" style="margin-right: 12px; color: var(--color-secondary); width: 16px; text-align: center;"></i>
                <span>Edit</span>
                <span style="margin-left: auto; color: #999; font-size: 12px;">⌘E</span>
              </a>
              <a href="#" style="display: flex; align-items: center; padding: 8px 16px; color: var(--color-body-text); text-decoration: none; font-size: 14px; font-weight: 500; transition: all 0.15s ease;" onmouseover="this.style.background='rgba(0,0,0,0.05)'; this.style.borderRadius='6px'; this.style.margin='0 6px'" onmouseout="this.style.background='transparent'; this.style.margin='0'">
                <i class="fas fa-copy" style="margin-right: 12px; color: var(--color-blue); width: 16px; text-align: center;"></i>
                <span>Duplicate</span>
                <span style="margin-left: auto; color: #999; font-size: 12px;">⌘D</span>
              </a>
              <div style="height: 1px; background: rgba(0,0,0,0.1); margin: 6px 16px;"></div>
              <a href="#" style="display: flex; align-items: center; padding: 8px 16px; color: var(--color-body-text); text-decoration: none; font-size: 14px; font-weight: 500; transition: all 0.15s ease;" onmouseover="this.style.background='rgba(0,0,0,0.05)'; this.style.borderRadius='6px'; this.style.margin='0 6px'" onmouseout="this.style.background='transparent'; this.style.margin='0'">
                <i class="fas fa-share" style="margin-right: 12px; color: var(--color-green); width: 16px; text-align: center;"></i>
                <span>Share</span>
                <span style="margin-left: auto; color: #999; font-size: 12px;">⌘S</span>
              </a>
              <a href="#" style="display: flex; align-items: center; padding: 8px 16px; color: var(--color-body-text); text-decoration: none; font-size: 14px; font-weight: 500; transition: all 0.15s ease;" onmouseover="this.style.background='rgba(0,0,0,0.05)'; this.style.borderRadius='6px'; this.style.margin='0 6px'" onmouseout="this.style.background='transparent'; this.style.margin='0'">
                <i class="fas fa-download" style="margin-right: 12px; color: var(--color-purple); width: 16px; text-align: center;"></i>
                <span>Export</span>
                <span style="margin-left: auto; color: #999; font-size: 12px;">⌘⇧E</span>
              </a>
              <div style="height: 1px; background: rgba(0,0,0,0.1); margin: 6px 16px;"></div>
              <a href="#" style="display: flex; align-items: center; padding: 8px 16px; color: var(--color-danger); text-decoration: none; font-size: 14px; font-weight: 500; transition: all 0.15s ease;" onmouseover="this.style.background='rgba(239,68,68,0.1)'; this.style.borderRadius='6px'; this.style.margin='0 6px'" onmouseout="this.style.background='transparent'; this.style.margin='0'">
                <i class="fas fa-trash" style="margin-right: 12px; width: 16px; text-align: center;"></i>
                <span>Delete</span>
                <span style="margin-left: auto; color: #999; font-size: 12px;">⌫</span>
              </a>
            </div>
          </div>
        </div>

        <div style="margin-bottom: 30px;">
          <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Two-Button Split</h4>
          <div class="button-group-two" style="display: inline-flex; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <button class="baum-button" style="border-radius: 0; border-right: 1px solid rgba(255,255,255,0.2); margin: 0; padding: 0 20px;">
              Save
            </button>
            <button class="baum-button" style="border-radius: 0; margin: 0; padding: 0 20px;">
              Cancel
            </button>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Button Colors</h3>
        <div style="margin-bottom: 20px;">
          <button class="baum-button"><i class="fas fa-star" style="margin-right: 6px; vertical-align: middle;"></i>Button with Icon</button>
          <button class="baum-button baum-button-secondary">Secondary</button>
          <button class="baum-button baum-button-success">Success</button>
          <button class="baum-button baum-button-warning">Warning</button>
          <button class="baum-button baum-button-danger">Danger</button>
        </div>

        <script>
        function toggleDropdown(button) {
          const dropdown = button.nextElementSibling;
          const isVisible = dropdown.style.display === 'block';

          // Close all other dropdowns
          document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.style.display = 'none';
          });

          // Toggle current dropdown
          dropdown.style.display = isVisible ? 'none' : 'block';
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
          if (!e.target.closest('.split-button')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
              menu.style.display = 'none';
            });
          }
        });
        </script>

        <h3 style="color: var(--color-body-text);">Disabled States</h3>
        <div>
          <button class="baum-button" disabled>Disabled Button</button>
          <button class="baum-button baum-button-secondary" disabled>Disabled Secondary</button>
        </div>

        <!-- =================================================================
             PAGINATION (MATCHING SINGLE.PHP)
             ================================================================= -->
        <h2 style="color: var(--color-body-text); margin-top: 60px;">Pagination</h2>

        <h3 style="color: var(--color-body-text);">Story Post Pagination</h3>
        <div style="margin: 20px 0;">
          <div class="baum-page-links" style="margin-bottom:50px;">
            <span class="button baum-button baum-button-small" style="display: inline-flex; align-items: center; justify-content: center; text-align: center; font-weight: 800; font-size: 10px;">
              <i class="fa-fw fa-solid fa-caret-left"></i> &nbsp; Previous
            </span>
            <span class="button baum-button baum-button-small" style="background: var(--color-primary); color: white; display: inline-flex; align-items: center; justify-content: center; text-align: center; font-weight: 800;">
              1
            </span>
            <span class="button baum-button baum-button-small" style="display: inline-flex; align-items: center; justify-content: center; text-align: center; font-weight: 800;">
              2
            </span>
            <span class="button baum-button baum-button-small" style="display: inline-flex; align-items: center; justify-content: center; text-align: center; font-weight: 800;">
              3
            </span>
            <span class="button baum-button baum-button-small" style="display: inline-flex; align-items: center; justify-content: center; text-align: center; font-weight: 800; font-size: 10px;">
              Next Page &nbsp; <i class="fa-fw fa-solid fa-caret-right"></i>
            </span>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Archive Pagination</h3>
        <div style="margin: 20px 0;">
          <?php
          // Display actual pagination if available
          global $wp_query;
          if ($wp_query->max_num_pages > 1) {
            pagination();
          } else {
            // Show example pagination
            echo '<div style="display: flex; justify-content: center; gap: 5px;">';
            echo '<a href="#" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); text-decoration: none; color: var(--color-secondary);">‹ Previous</a>';
            echo '<a href="#" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); text-decoration: none; background: var(--color-primary); color: white;">1</a>';
            echo '<a href="#" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); text-decoration: none; color: var(--color-secondary);">2</a>';
            echo '<a href="#" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); text-decoration: none; color: var(--color-secondary);">3</a>';
            echo '<span style="padding: 8px 12px;">...</span>';
            echo '<a href="#" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); text-decoration: none; color: var(--color-secondary);">10</a>';
            echo '<a href="#" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); text-decoration: none; color: var(--color-secondary);">Next ›</a>';
            echo '</div>';
          }
          ?>
        </div>
      </section>

      <!-- =================================================================
           FUNCTIONALITY TEST SECTION
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">🧪 Functionality Tests</h2>
        <div style="background: #e3f2fd; padding: 20px; border-radius: var(--border-radius); margin-bottom: 30px;">
          <h3 style="color: var(--color-body-text);">Interactive Component Status:</h3>
          <div id="test-results" style="font-family: monospace; font-size: 12px;">
            <div>🔄 Loading tests...</div>
          </div>
        </div>
      </section>

      <!-- =================================================================
           SEARCH MODAL
           ================================================================= -->
           <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Search Modal</h2>

        <h3 style="color: var(--color-body-text);">Search with Autocomplete</h3>
        <div style="margin: 20px 0;">
          <button id="open-search" style="padding: 12px 20px; background: var(--color-secondary); color: var(--color-white); border: none; border-radius: var(--border-radius); font-size: 16px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 8px;">
            <i class="fa-solid fa-search"></i>
            Open Search Modal
            <span style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px; font-size: 12px; margin-left: 8px;">Ctrl+K</span>
          </button>
        </div>

        <!-- Search Modal -->
        <div id="search-modal" class="search-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; backdrop-filter: blur(4px);">
          <div class="search-modal-content" style="position: absolute; top: 15%; left: 50%; transform: translateX(-50%); width: 90%; max-width: 600px; background: var(--color-background); border-radius: var(--border-radius); box-shadow: 0 20px 60px rgba(0,0,0,0.3); overflow: hidden;">

            <!-- Search Header -->
            <div class="search-header" style="padding: 20px 20px 0 20px;">
              <div style="position: relative;">
                <i class="fa-solid fa-search" style="position: absolute; left: 16px; top: 50%; transform: translateY(-50%); color: var(--color-gray); font-size: 18px;"></i>
                <input type="text" id="search-input" placeholder="Search for anything..." style="width: 100%; padding: 16px 50px 16px 50px; border: 2px solid var(--color-gray); border-radius: var(--border-radius); font-size: 18px; background: var(--color-background); color: var(--color-body-text); outline: none;">
                <button id="close-search" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--color-gray); font-size: 20px; cursor: pointer; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                  <i class="fa-solid fa-times"></i>
                </button>
              </div>
            </div>

            <!-- Search Results -->
            <div class="search-results" style="max-height: 400px; overflow-y: auto; padding: 20px;">
              <div class="search-suggestions" style="margin-bottom: 20px;">
                <div style="font-size: 12px; font-weight: 600; color: var(--color-gray); text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 8px;">Quick Actions</div>
                <div class="suggestion-item" data-action="new-post" style="display: flex; align-items: center; padding: 12px; border-radius: var(--border-radius); cursor: pointer; transition: all 0.2s ease; margin-bottom: 4px;">
                  <i class="fa-solid fa-plus" style="width: 20px; color: var(--color-secondary); margin-right: 12px;"></i>
                  <span style="color: var(--color-body-text);">Create New Post</span>
                  <span style="margin-left: auto; font-size: 12px; color: var(--color-gray);">Ctrl+N</span>
                </div>
                <div class="suggestion-item" data-action="settings" style="display: flex; align-items: center; padding: 12px; border-radius: var(--border-radius); cursor: pointer; transition: all 0.2s ease; margin-bottom: 4px;">
                  <i class="fa-solid fa-cog" style="width: 20px; color: var(--color-tertiary); margin-right: 12px;"></i>
                  <span style="color: var(--color-body-text);">Settings</span>
                  <span style="margin-left: auto; font-size: 12px; color: var(--color-gray);">Ctrl+,</span>
                </div>
                <div class="suggestion-item" data-action="help" style="display: flex; align-items: center; padding: 12px; border-radius: var(--border-radius); cursor: pointer; transition: all 0.2s ease; margin-bottom: 4px;">
                  <i class="fa-solid fa-question-circle" style="width: 20px; color: var(--color-quaternary); margin-right: 12px;"></i>
                  <span style="color: var(--color-body-text);">Help & Support</span>
                  <span style="margin-left: auto; font-size: 12px; color: var(--color-gray);">F1</span>
                </div>
              </div>

              <div class="search-content" style="display: none;">
                <div style="font-size: 12px; font-weight: 600; color: var(--color-gray); text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 8px;">Search Results</div>
                <div id="search-results-list">
                  <!-- Dynamic search results will be inserted here -->
                </div>
              </div>

              <div class="no-results" style="display: none; text-align: center; padding: 40px 20px; color: var(--color-gray);">
                <i class="fa-solid fa-search" style="font-size: 48px; margin-bottom: 16px; opacity: 0.3;"></i>
                <div style="font-size: 18px; margin-bottom: 8px;">No results found</div>
                <div style="font-size: 14px;">Try adjusting your search terms</div>
              </div>
            </div>

            <!-- Search Footer -->
            <div class="search-footer" style="padding: 16px 20px; border-top: 1px solid var(--color-gray); background: var(--color-background); display: flex; align-items: center; justify-content: space-between; font-size: 12px; color: var(--color-gray);">
              <div style="display: flex; align-items: center; gap: 16px;">
                <span style="display: flex; align-items: center; gap: 4px;">
                  <kbd style="background: var(--color-gray); color: var(--color-white); padding: 2px 6px; border-radius: 3px; font-size: 10px;">↑</kbd>
                  <kbd style="background: var(--color-gray); color: var(--color-white); padding: 2px 6px; border-radius: 3px; font-size: 10px;">↓</kbd>
                  to navigate
                </span>
                <span style="display: flex; align-items: center; gap: 4px;">
                  <kbd style="background: var(--color-gray); color: var(--color-white); padding: 2px 6px; border-radius: 3px; font-size: 10px;">Enter</kbd>
                  to select
                </span>
                <span style="display: flex; align-items: center; gap: 4px;">
                  <kbd style="background: var(--color-gray); color: var(--color-white); padding: 2px 6px; border-radius: 3px; font-size: 10px;">Esc</kbd>
                  to close
                </span>
              </div>
              <div>
                Powered by BaumPress Search
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- =================================================================
           AUTHENTICATION FORMS
           ================================================================= -->
           <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Authentication Forms</h2>

        <h3 style="color: var(--color-body-text);">Authentication Forms</h3>
        <div style="display: flex; gap: 20px; margin: 20px 0;">
          <!-- Create Account Form -->
          <div style="flex: 1; max-width: 400px;">
            <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Create Account Form</h4>
            <div class="auth-form-container" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); border: 1px solid #f0f0f0;">
            <!-- Logo/Icon -->
            <div style="text-align: center; margin-bottom: 24px;">
              <div style="width: 60px; height: 60px; background: #333; border-radius: 12px; display: inline-flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                ⚪
              </div>
            </div>

            <!-- Header -->
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="margin: 0 0 6px 0; color: var(--color-body-text); font-size: 20px; font-weight: 700;">Create your account</h2>
              <p style="margin: 0; color: #666; font-size: 14px;">Welcome! Please fill in the details to get started.</p>
            </div>

            <!-- Social Login Buttons -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 16px;">
              <button style="display: flex; align-items: center; justify-content: center; gap: 8px; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; color: #333; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;">
                <i class="fab fa-google" style="color: #4285f4;"></i>
                Google
              </button>
              <button style="display: flex; align-items: center; justify-content: center; gap: 8px; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; background: white; color: #333; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;">
                <i class="fab fa-github" style="color: #333;"></i>
                GitHub
              </button>
            </div>

            <!-- Divider -->
            <div style="text-align: center; margin: 16px 0; position: relative;">
              <span style="background: white; padding: 0 12px; color: #666; font-size: 12px; position: relative; z-index: 1;">or</span>
              <div style="position: absolute; top: 50%; left: 0; right: 0; height: 1px; background: #e0e0e0; z-index: 0;"></div>
            </div>

            <!-- Form Fields -->
            <form style="display: flex; flex-direction: column; gap: 12px;">
              <!-- Name Fields -->
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                <div>
                  <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">First name</label>
                  <span style="color: #999; font-size: 12px; margin-left: 4px;">Optional</span>
                  <input type="text" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; color: var(--color-body-text); background: #f8f9fa; box-sizing: border-box; transition: all 0.2s ease;">
                </div>
                <div>
                  <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">Last name</label>
                  <span style="color: #999; font-size: 12px; margin-left: 4px;">Optional</span>
                  <input type="text" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; color: var(--color-body-text); background: #f8f9fa; box-sizing: border-box; transition: all 0.2s ease;">
                </div>
              </div>

              <!-- Username -->
              <div>
                <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">Username</label>
                <input type="text" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; color: var(--color-body-text); background: #f8f9fa; box-sizing: border-box; transition: all 0.2s ease;">
              </div>

              <!-- Email -->
              <div>
                <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">Email address</label>
                <input type="email" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; color: var(--color-body-text); background: #f8f9fa; box-sizing: border-box; transition: all 0.2s ease;">
              </div>

              <!-- Phone -->
              <div>
                <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">Phone number</label>
                <div style="display: flex; gap: 8px;">
                  <select style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; color: var(--color-body-text); background: #f8f9fa; min-width: 80px;">
                    <option>US</option>
                    <option>UK</option>
                    <option>CA</option>
                  </select>
                  <input type="tel" placeholder="+1" style="flex: 1; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; color: var(--color-body-text); background: #f8f9fa; box-sizing: border-box; transition: all 0.2s ease;">
                </div>
              </div>

              <!-- Password -->
              <div>
                <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">Password</label>
                <div style="position: relative;">
                  <input type="password" style="width: 100%; padding: 12px 40px 12px 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; color: var(--color-body-text); background: #f8f9fa; box-sizing: border-box; transition: all 0.2s ease;">
                  <button type="button" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #666; cursor: pointer;">
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
              </div>

              <!-- Submit Button -->
              <button type="submit" style="width: 100%; padding: 14px; background: #333; color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; margin-top: 8px; transition: all 0.2s ease;">
                Continue →
              </button>
            </form>

            <!-- Footer -->
            <div style="text-align: center; margin-top: 16px;">
              <p style="margin: 0; color: #666; font-size: 12px;">
                Already have an account? <a href="#" style="color: var(--color-primary); text-decoration: none; font-weight: 500;">Sign in</a>
              </p>
            </div>

            <!-- Security Badge -->
            <div style="text-align: center; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
              <p style="margin: 0; color: #999; font-size: 10px; display: flex; align-items: center; justify-content: center; gap: 4px;">
                Secured by <strong style="color: #666;">clerk</strong>
              </p>
            </div>
            </div>
          </div>

          <!-- Choose Account Form -->
          <div style="flex: 1; max-width: 400px;">
            <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Choose Account Form</h4>
            <div class="auth-form-container" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); border: 1px solid #f0f0f0;">
            <!-- Logo/Icon -->
            <div style="text-align: center; margin-bottom: 24px;">
              <div style="width: 60px; height: 60px; background: #333; border-radius: 12px; display: inline-flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                ⚪
              </div>
            </div>

            <!-- Header -->
            <div style="text-align: center; margin-bottom: 32px;">
              <h2 style="margin: 0 0 8px 0; color: var(--color-body-text); font-size: 24px; font-weight: 700;">Choose an account</h2>
              <p style="margin: 0; color: #666; font-size: 16px;">Select the account with which you wish to continue.</p>
            </div>

            <!-- Account Options -->
            <div style="display: flex; flex-direction: column; gap: 12px;">
              <!-- Personal Account -->
              <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; background: white;">
                <div style="display: flex; align-items: center; gap: 12px;">
                  <div style="width: 40px; height: 40px; border-radius: 50%; overflow: hidden; background: #f0f0f0;">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 40 40'%3E%3Crect width='40' height='40' fill='%23ddd'/%3E%3Ctext x='20' y='25' text-anchor='middle' fill='%23666' font-size='14'%3E👤%3C/text%3E%3C/svg%3E" style="width: 100%; height: 100%; object-fit: cover;" alt="Profile">
                  </div>
                  <div>
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 2px;">Personal account</div>
                  </div>
                </div>
                <i class="fas fa-chevron-right" style="color: #ccc; font-size: 14px;"></i>
              </div>

              <!-- Clerk App -->
              <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; background: white;">
                <div style="display: flex; align-items: center; gap: 12px;">
                  <div style="width: 40px; height: 40px; background: #6366f1; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: 600;">
                    C
                  </div>
                  <div>
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 2px;">Clerk App</div>
                    <div style="color: #666; font-size: 14px;">Admin</div>
                  </div>
                </div>
              </div>

              <!-- Clerk Sample Apps -->
              <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; background: white;">
                <div style="display: flex; align-items: center; gap: 12px;">
                  <div style="width: 40px; height: 40px; background: #666; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px; font-weight: 600;">
                    👤
                  </div>
                  <div>
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 2px;">Clerk Sample Apps</div>
                    <div style="color: #666; font-size: 14px;">Admin</div>
                  </div>
                </div>
                <button style="background: #f0f0f0; color: #666; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; font-weight: 500; cursor: pointer;">
                  Request to join
                </button>
              </div>

              <!-- Test org -->
              <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; background: white;">
                <div style="display: flex; align-items: center; gap: 12px;">
                  <div style="width: 40px; height: 40px; background: #f0f0f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #666; font-size: 18px; font-weight: 600;">
                    🏢
                  </div>
                  <div>
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 2px;">Test org</div>
                    <div style="color: #666; font-size: 14px;">Member</div>
                  </div>
                </div>
                <button style="background: var(--color-primary); color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; font-weight: 500; cursor: pointer;">
                  Join
                </button>
              </div>

              <!-- Create Organization -->
              <div style="display: flex; align-items: center; gap: 12px; padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; background: white;">
                <div style="width: 40px; height: 40px; background: #f0f0f0; border: 1px dashed #ccc; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #666; font-size: 18px;">
                  +
                </div>
                <div>
                  <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">Create organization</div>
                </div>
              </div>
            </div>

            <!-- Security Badge -->
            <div style="text-align: center; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
              <p style="margin: 0; color: #999; font-size: 10px; display: flex; align-items: center; justify-content: center; gap: 4px;">
                Secured by <strong style="color: #666;">clerk</strong>
              </p>
            </div>
            </div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Organization Switcher</h3>
        <div style="max-width: 300px; margin: 20px 0;">
          <!-- Organization Switcher Dropdown -->
          <div class="org-switcher-dropdown" style="background: white; border-radius: 12px; padding: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.15); border: 1px solid #e0e0e0; position: relative;">
            <!-- Current Organization -->
            <div style="display: flex; align-items: center; gap: 12px; padding: 8px 12px; border-radius: 8px; cursor: pointer; transition: all 0.2s ease;">
              <div style="width: 24px; height: 24px; background: #666; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: 600;">
                👤
              </div>
              <span style="color: var(--color-body-text); font-size: 16px; font-weight: 500; flex: 1;">Clerk Sample Apps</span>
              <i class="fas fa-chevron-down" style="color: #999; font-size: 12px;"></i>
            </div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Organization Dashboard</h3>
        <div style="max-width: 500px; margin: 20px 0;">
          <div class="org-dashboard" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid #f0f0f0;">
            <!-- Header with Settings -->
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 32px;">
              <div style="display: flex; align-items: center; gap: 16px;">
                <div style="width: 60px; height: 60px; background: #f0f0f0; border-radius: 12px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 24px;">
                  🏢
                </div>
                <div>
                  <h2 style="margin: 0 0 4px 0; color: var(--color-body-text); font-size: 24px; font-weight: 700;">Test org</h2>
                  <div style="color: #666; font-size: 16px;">Member</div>
                </div>
              </div>
              <button style="width: 40px; height: 40px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s ease;">
                <i class="fas fa-cog" style="color: #666; font-size: 16px;"></i>
              </button>
            </div>

            <!-- Account Options -->
            <div style="display: flex; flex-direction: column; gap: 16px;">
              <!-- Personal Account -->
              <div style="display: flex; align-items: center; gap: 16px; padding: 16px; border: 1px solid #f0f0f0; border-radius: 12px; background: #f8f9fa;">
                <div style="width: 48px; height: 48px; border-radius: 50%; overflow: hidden; background: #f0f0f0;">
                  <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 48 48'%3E%3Crect width='48' height='48' fill='%23ddd'/%3E%3Ctext x='24' y='30' text-anchor='middle' fill='%23666' font-size='16'%3E👤%3C/text%3E%3C/svg%3E" style="width: 100%; height: 100%; object-fit: cover;" alt="Profile">
                </div>
                <div style="flex: 1;">
                  <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin-bottom: 2px;">Personal account</div>
                </div>
              </div>

              <!-- Test Account -->
              <div style="display: flex; align-items: center; justify-content: space-between; padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; background: white;">
                <div style="display: flex; align-items: center; gap: 16px;">
                  <div style="width: 48px; height: 48px; background: #666; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; font-weight: 600;">
                    👤
                  </div>
                  <div>
                    <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin-bottom: 2px;">Test</div>
                  </div>
                </div>
                <i class="fas fa-chevron-right" style="color: #ccc; font-size: 14px;"></i>
              </div>

              <!-- Create Organization -->
              <div style="display: flex; align-items: center; gap: 16px; padding: 16px; border: 1px solid #e0e0e0; border-radius: 12px; cursor: pointer; transition: all 0.2s ease; background: white;">
                <div style="width: 48px; height: 48px; background: #f0f0f0; border: 1px dashed #ccc; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #666; font-size: 20px;">
                  +
                </div>
                <div>
                  <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">Create organization</div>
                </div>
              </div>
            </div>

            <!-- Security Badge -->
            <div style="text-align: center; margin-top: 24px; padding-top: 20px; border-top: 1px solid #f0f0f0;">
              <p style="margin: 0; color: #999; font-size: 12px; display: flex; align-items: center; justify-content: center; gap: 6px;">
                Secured by <strong style="color: #666;">clerk</strong>
              </p>
            </div>
          </div>
        </div>


        <div style="max-width: 600px; margin: 20px 0;">
          <div class="api-responses-enhanced" style="background: #f8f9fa; border-radius: 12px; padding: 24px;">
            <h4 style="color: #666; font-size: 14px; font-weight: 600; margin: 0 0 20px 0; text-transform: uppercase; letter-spacing: 0.5px;">RESPONSES</h4>

            <div style="display: flex; flex-direction: column; gap: 12px;">
              <!-- 200 OK -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="width: 8px; height: 8px; background: #34c759; border-radius: 50%;"></div>
                    <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">200</div>
                    <div style="color: #666; font-size: 16px;">OK</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">The request was successful and the server returned the requested data.</p>
                </div>
              </div>

              <!-- 400 Bad Request -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                      <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">400</div>
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.4;">One of the request parameters is invalid. See the returned message for details.</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; margin-top: 4px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">Bad Request - The server could not understand the request due to invalid syntax.</p>
                </div>
              </div>

              <!-- 403 Forbidden -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                      <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">403</div>
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.4;">Authentication headers are missing or invalid. Make sure you authenticate your request with a valid API key.</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; margin-top: 4px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">Forbidden - The client does not have access rights to the content.</p>
                </div>
              </div>

              <!-- 429 Rate Limit -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                      <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">429</div>
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.4;">Too many requests. You hit the rate limit. Use the X-RateLimit-... response headers to make sure you're under the rate limit.</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; margin-top: 4px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">Too Many Requests - The user has sent too many requests in a given amount of time.</p>
                </div>
              </div>

              <!-- 500 Internal Server Error -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                      <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">500</div>
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.4;">Internal server error. We recommend retrying these later. If the issue persists, please contact us on Slack or on the Community Forum.</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; margin-top: 4px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">Internal Server Error - The server has encountered a situation it doesn't know how to handle.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- =================================================================
           ACCOUNT MANAGEMENT COMPONENTS
           ================================================================= -->
           <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Account Management Components</h2>

        <h3 style="color: var(--color-body-text);">Account Settings Modal</h3>
        <div style="margin: 20px 0;">
          <button id="open-account-modal" style="padding: 12px 20px; background: var(--color-secondary); color: var(--color-white); border: none; border-radius: var(--border-radius); font-size: 16px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-user-cog"></i>
            Open Account Settings
          </button>
        </div>

        <!-- Account Settings Modal -->
        <div id="account-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; backdrop-filter: blur(4px);">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: var(--border-radius); box-shadow: 0 20px 40px rgba(0,0,0,0.2); width: 90%; max-width: 900px; max-height: 90vh; overflow: hidden; display: flex;">

            <!-- Sidebar -->
            <div style="width: 250px; background: #f8f9fa; padding: 20px; border-right: 1px solid #e0e0e0;">
              <div style="margin-bottom: 30px;">
                <h3 style="margin: 0 0 5px 0; color: var(--color-body-text); font-size: 18px; font-weight: 600;">Account</h3>
                <p style="margin: 0; color: #666; font-size: 14px;">Manage your account info.</p>
              </div>

              <nav>
                <div class="account-nav-item active" data-tab="profile" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: var(--border-radius); cursor: pointer; margin-bottom: 8px; background: var(--color-secondary); color: white;">
                  <i class="fas fa-user" style="width: 16px;"></i>
                  <span style="font-size: 14px; font-weight: 500;">Profile</span>
                </div>
                <div class="account-nav-item" data-tab="security" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: var(--border-radius); cursor: pointer; margin-bottom: 8px; color: #666; transition: all 0.2s ease;">
                  <i class="fas fa-shield-alt" style="width: 16px;"></i>
                  <span style="font-size: 14px; font-weight: 500;">Security</span>
                </div>
              </nav>
            </div>

            <!-- Main Content -->
            <div style="flex: 1; padding: 30px; overflow-y: auto;">
              <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 30px;">
                <h2 style="margin: 0; color: var(--color-body-text); font-size: 20px; font-weight: 600;">Profile details</h2>
                <button id="close-account-modal" style="background: none; border: none; color: #999; font-size: 20px; cursor: pointer; padding: 5px;">
                  <i class="fas fa-times"></i>
                </button>
              </div>

              <!-- Profile Tab Content -->
              <div id="profile-tab" class="account-tab-content">
                <!-- Profile Header -->
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #f0f0f0;">
                  <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 20px; font-weight: 600;">
                      JD
                    </div>
                    <div>
                      <h3 style="margin: 0 0 4px 0; color: var(--color-body-text); font-size: 16px; font-weight: 600;">Jaylon Dias</h3>
                    </div>
                  </div>
                  <button style="padding: 8px 16px; background: none; border: 1px solid #ddd; border-radius: var(--border-radius); color: #666; font-size: 14px; cursor: pointer;">
                    Edit profile
                  </button>
                </div>

                <!-- Email Addresses -->
                <div style="margin-bottom: 30px;">
                  <h4 style="margin: 0 0 15px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">Email addresses</h4>
                  <div style="display: grid; gap: 12px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px; background: #f8f9fa; border-radius: var(--border-radius);">
                      <span style="color: var(--color-body-text); font-size: 14px;"><EMAIL></span>
                      <span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">Primary</span>
                    </div>
                    <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px; background: #f8f9fa; border-radius: var(--border-radius);">
                      <span style="color: var(--color-body-text); font-size: 14px;"><EMAIL></span>
                    </div>
                    <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px; background: #f8f9fa; border-radius: var(--border-radius);">
                      <span style="color: var(--color-body-text); font-size: 14px;"><EMAIL></span>
                    </div>
                    <button style="display: flex; align-items: center; gap: 8px; padding: 12px; background: none; border: 1px dashed #ddd; border-radius: var(--border-radius); color: #666; font-size: 14px; cursor: pointer; width: 100%; justify-content: center;">
                      <i class="fas fa-plus"></i>
                      Add email address
                    </button>
                  </div>
                </div>

                <!-- Phone Number -->
                <div style="margin-bottom: 30px;">
                  <h4 style="margin: 0 0 15px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">Phone number</h4>
                  <div style="display: grid; gap: 12px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px; background: #f8f9fa; border-radius: var(--border-radius);">
                      <span style="color: var(--color-body-text); font-size: 14px;">+****************</span>
                      <span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">Primary</span>
                    </div>
                    <button style="display: flex; align-items: center; gap: 8px; padding: 12px; background: none; border: 1px dashed #ddd; border-radius: var(--border-radius); color: #666; font-size: 14px; cursor: pointer; width: 100%; justify-content: center;">
                      <i class="fas fa-plus"></i>
                      Add phone number
                    </button>
                  </div>
                </div>

                <!-- Connected Accounts -->
                <div style="margin-bottom: 30px;">
                  <h4 style="margin: 0 0 15px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">Connected accounts</h4>
                  <div style="display: grid; gap: 12px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; padding: 12px; background: #f8f9fa; border-radius: var(--border-radius);">
                      <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 20px; height: 20px; background: #db4437; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 10px; font-weight: bold;">G</div>
                        <div>
                          <div style="color: var(--color-body-text); font-size: 14px; font-weight: 500;">Google</div>
                          <div style="color: #999; font-size: 12px;"><EMAIL></div>
                        </div>
                      </div>
                    </div>
                    <button style="display: flex; align-items: center; gap: 8px; padding: 12px; background: none; border: 1px dashed #ddd; border-radius: var(--border-radius); color: #666; font-size: 14px; cursor: pointer; width: 100%; justify-content: center;">
                      <i class="fas fa-plus"></i>
                      Connect account
                    </button>
                  </div>
                </div>

                <!-- Footer -->
                <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #f0f0f0; text-align: center;">
                  <div style="display: flex; align-items: center; justify-content: center; gap: 8px; color: #999; font-size: 12px;">
                    <span>Secured by</span>
                    <strong style="color: #666;">clerk</strong>
                  </div>
                </div>
              </div>

              <!-- Security Tab Content (Hidden by default) -->
              <div id="security-tab" class="account-tab-content" style="display: none;">
                <h3 style="margin: 0 0 20px 0; color: var(--color-body-text); font-size: 18px; font-weight: 600;">Security Settings</h3>
                <p style="color: #666; margin-bottom: 30px;">Manage your account security and authentication methods.</p>

                <div style="background: #f8f9fa; padding: 20px; border-radius: var(--border-radius); text-align: center;">
                  <i class="fas fa-shield-alt" style="font-size: 48px; color: #ccc; margin-bottom: 15px;"></i>
                  <p style="margin: 0; color: #666;">Security settings would be implemented here.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Account Switcher Dropdown</h3>
        <div style="margin: 20px 0;">
          <button id="open-account-switcher" style="padding: 12px 20px; background: var(--color-primary); color: var(--color-white); border: none; border-radius: var(--border-radius); font-size: 16px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-user-circle"></i>
            Account Switcher
          </button>
        </div>

        <!-- Account Switcher Dropdown -->
        <div id="account-switcher-dropdown" style="display: none; position: relative; max-width: 400px; margin: 20px 0;">
          <div style="position: absolute; top: 0; right: 0; background: white; border: 1px solid #e0e0e0; border-radius: var(--border-radius); box-shadow: 0 8px 24px rgba(0,0,0,0.15); padding: 20px; width: 350px; z-index: 100;">

            <!-- Current Account -->
            <div style="display: flex; align-items: center; gap: 15px; padding: 15px; background: #f8f9fa; border-radius: var(--border-radius); margin-bottom: 20px;">
              <div style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; font-weight: 600;">
                CW
              </div>
              <div style="flex: 1;">
                <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 2px;">Cameron Walker</div>
                <div style="color: #666; font-size: 14px;"><EMAIL></div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div style="display: flex; gap: 10px; margin-bottom: 20px;">
              <button style="flex: 1; padding: 10px; background: none; border: 1px solid #ddd; border-radius: var(--border-radius); color: #666; font-size: 14px; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 8px;">
                <i class="fas fa-cog"></i>
                Manage account
              </button>
              <button style="flex: 1; padding: 10px; background: none; border: 1px solid #ddd; border-radius: var(--border-radius); color: #666; font-size: 14px; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 8px;">
                <i class="fas fa-sign-out-alt"></i>
                Sign out
              </button>
            </div>

            <!-- Other Accounts -->
            <div style="border-top: 1px solid #f0f0f0; padding-top: 15px;">
              <div style="display: flex; align-items: center; gap: 15px; padding: 12px; border-radius: var(--border-radius); cursor: pointer; transition: background 0.2s ease;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">
                <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; font-weight: 600;">
                  CW
                </div>
                <div style="flex: 1;">
                  <div style="color: var(--color-body-text); font-size: 14px; font-weight: 500; margin-bottom: 2px;">Cameron Walker</div>
                  <div style="color: #666; font-size: 12px;"><EMAIL></div>
                </div>
              </div>

              <div style="display: flex; align-items: center; gap: 15px; padding: 12px; border-radius: var(--border-radius); cursor: pointer; transition: background 0.2s ease;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #e0e0e0; display: flex; align-items: center; justify-content: center; color: #666; font-size: 14px; font-weight: 600;">
                  CW
                </div>
                <div style="flex: 1;">
                  <div style="color: var(--color-body-text); font-size: 14px; font-weight: 500; margin-bottom: 2px;">Cameron Walker</div>
                  <div style="color: #666; font-size: 12px;"><EMAIL></div>
                </div>
              </div>

              <button style="display: flex; align-items: center; gap: 12px; padding: 12px; background: none; border: 1px dashed #ddd; border-radius: var(--border-radius); color: #666; font-size: 14px; cursor: pointer; width: 100%; margin-top: 10px;">
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999; font-size: 16px;">
                  <i class="fas fa-plus"></i>
                </div>
                <span>Add account</span>
              </button>
            </div>

            <!-- Sign Out All -->
            <div style="border-top: 1px solid #f0f0f0; margin-top: 15px; padding-top: 15px;">
              <button style="display: flex; align-items: center; gap: 8px; padding: 10px; background: none; border: none; color: #666; font-size: 14px; cursor: pointer; width: 100%; justify-content: center;">
                <i class="fas fa-sign-out-alt"></i>
                Sign out of all accounts
              </button>
            </div>

            <!-- Footer -->
            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #f0f0f0; text-align: center;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 8px; color: #999; font-size: 12px;">
                <span>Secured by</span>
                <strong style="color: #666;">clerk</strong>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- =================================================================
           ORGANIZATION CREATION FORM
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Organization Creation Form</h2>

        <h3 style="color: var(--color-body-text);">Organization Profile Modal</h3>
        <div style="margin: 20px 0;">
          <button id="open-org-profile-modal" style="padding: 12px 20px; background: var(--color-primary); color: var(--color-white); border: none; border-radius: var(--border-radius); font-size: 16px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-building"></i>
            Open Organization Profile
          </button>
        </div>

        <!-- Organization Profile Modal -->
        <div id="org-profile-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; backdrop-filter: blur(4px);">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.15); width: 90%; max-width: 1000px; padding: 0; overflow: hidden;">

            <!-- Organization Profile Content -->
            <div style="display: grid; grid-template-columns: 280px 1fr; min-height: 600px;">

              <!-- Left Sidebar -->
              <div style="background: #f9fafb; border-right: 1px solid #e5e7eb; padding: 30px 0;">

                <!-- Header -->
                <div style="padding: 0 24px 30px;">
                  <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 700; margin: 0 0 8px;">Organization</h2>
                  <p style="color: #6b7280; font-size: 14px; margin: 0;">Manage your organization.</p>
                </div>

                <!-- Navigation Menu -->
                <nav style="padding: 0;">

                  <!-- General Tab (Active) -->
                  <div style="background: #e5e7eb; border-right: 3px solid var(--color-secondary); margin: 0 0 4px;">
                    <div style="padding: 12px 24px; display: flex; align-items: center; gap: 12px;">
                      <i class="fas fa-building" style="color: var(--color-body-text); font-size: 16px; width: 16px;"></i>
                      <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">General</span>
                    </div>
                  </div>

                  <!-- Members Tab -->
                  <div style="margin: 0 0 4px; transition: background 0.2s ease;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='transparent'">
                    <div style="padding: 12px 24px; display: flex; align-items: center; gap: 12px; cursor: pointer;">
                      <i class="fas fa-users" style="color: #9ca3af; font-size: 16px; width: 16px;"></i>
                      <span style="color: #6b7280; font-size: 16px;">Members</span>
                    </div>
                  </div>

                </nav>

                <!-- Footer -->
                <div style="position: absolute; bottom: 24px; left: 24px; display: flex; align-items: center; gap: 8px;">
                  <span style="color: #9ca3af; font-size: 12px;">Secured by</span>
                  <div style="display: flex; align-items: center; gap: 4px;">
                    <div style="width: 16px; height: 16px; background: var(--color-body-text); border-radius: 2px; display: flex; align-items: center; justify-content: center;">
                      <span style="color: white; font-size: 10px; font-weight: bold;">C</span>
                    </div>
                    <span style="color: #6b7280; font-size: 12px; font-weight: 600;">clerk</span>
                  </div>
                </div>

              </div>

              <!-- Main Content Area -->
              <div style="padding: 40px;">

                <!-- Page Header -->
                <div style="margin-bottom: 40px;">
                  <h1 style="color: var(--color-body-text); font-size: 28px; font-weight: 700; margin: 0;">General details</h1>
                </div>

                <!-- Organization Profile Section -->
                <div style="margin-bottom: 40px;">
                  <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin: 0 0 20px;">Organization profile</h3>

                  <div style="display: flex; align-items: center; justify-content: space-between; padding: 20px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <div style="display: flex; align-items: center; gap: 16px;">
                      <!-- Organization Avatar -->
                      <div style="width: 48px; height: 48px; background: #6b7280; border-radius: 50%; display: flex; align-items: center; justify-content: center; position: relative;">
                        <i class="fas fa-building" style="color: white; font-size: 20px;"></i>
                        <!-- Small badge -->
                        <div style="position: absolute; bottom: -2px; right: -2px; width: 16px; height: 16px; background: #10b981; border: 2px solid white; border-radius: 50%;"></div>
                      </div>
                      <div>
                        <h4 style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin: 0;">Sample Organization</h4>
                      </div>
                    </div>
                    <button style="background: transparent; border: 1px solid #d1d5db; color: #374151; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.borderColor='#9ca3af'; this.style.background='#f9fafb'" onmouseout="this.style.borderColor='#d1d5db'; this.style.background='transparent'">
                      Edit profile
                    </button>
                  </div>
                </div>

                <!-- Verified Domains Section -->
                <div style="margin-bottom: 40px;">
                  <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin: 0 0 20px;">Verified domains</h3>

                  <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px;">
                    <div style="display: flex; align-items: flex-start; gap: 16px;">
                      <button style="background: transparent; border: 1px dashed #d1d5db; color: #6b7280; padding: 8px; border-radius: 6px; cursor: pointer; transition: all 0.2s ease; margin-top: 4px;" onmouseover="this.style.borderColor='#9ca3af'; this.style.color='#374151'" onmouseout="this.style.borderColor='#d1d5db'; this.style.color='#6b7280'">
                        <i class="fas fa-plus" style="font-size: 14px;"></i>
                      </button>
                      <div style="flex: 1;">
                        <h4 style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin: 0 0 8px;">Add domain</h4>
                        <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin: 0;">Allow users to join the organization automatically or request to join based on a verified email domain.</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Leave Organization Section -->
                <div style="margin-bottom: 40px;">
                  <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin: 0 0 20px;">Leave organization</h3>

                  <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span style="color: var(--color-body-text); font-size: 16px;">Leave organization</span>
                    <button style="background: transparent; border: 1px solid #ef4444; color: #ef4444; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='#ef4444'; this.style.color='white'" onmouseout="this.style.background='transparent'; this.style.color='#ef4444'">
                      Leave
                    </button>
                  </div>
                </div>

              </div>

            </div>

            <!-- Close Button -->
            <button id="close-org-profile-modal" style="position: absolute; top: 20px; right: 20px; background: none; border: none; color: #999; font-size: 20px; cursor: pointer; padding: 8px; border-radius: 50%; transition: all 0.2s ease;">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Create Organization Modal</h3>
        <div style="margin: 20px 0;">
          <button id="open-org-modal" style="padding: 12px 20px; background: var(--color-secondary); color: var(--color-white); border: none; border-radius: var(--border-radius); font-size: 16px; font-weight: 600; cursor: pointer; display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-building"></i>
            Create Organization
          </button>
        </div>

        <!-- Organization Creation Modal -->
        <div id="org-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; backdrop-filter: blur(4px);">
          <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.15); width: 90%; max-width: 500px; padding: 0; overflow: hidden;">

            <!-- Form Content -->
            <div style="padding: 40px;">
              <!-- Header -->
              <div style="margin-bottom: 40px;">
                <h2 style="margin: 0; color: var(--color-body-text); font-size: 28px; font-weight: 700; line-height: 1.2;">Create organization</h2>
              </div>

              <!-- Logo Upload Section -->
              <div style="margin-bottom: 32px;">
                <label style="display: block; margin-bottom: 12px; color: var(--color-body-text); font-size: 16px; font-weight: 600;">Logo</label>

                <div style="display: flex; align-items: flex-start; gap: 16px;">
                  <!-- Logo Preview Area -->
                  <div class="logo-upload-area" style="width: 120px; height: 120px; border: 2px dashed #d0d0d0; border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; background: #fafafa; cursor: pointer; transition: all 0.2s ease; position: relative; overflow: hidden;">
                    <div class="upload-placeholder" style="text-align: center; color: #999;">
                      <i class="fas fa-upload" style="font-size: 24px; margin-bottom: 8px; display: block;"></i>
                    </div>
                    <input type="file" id="logo-upload" accept="image/*" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer;">
                  </div>

                  <!-- Upload Instructions -->
                  <div style="flex: 1; padding-top: 8px;">
                    <button type="button" class="upload-btn" style="background: none; border: 1px solid #d0d0d0; border-radius: var(--border-radius); padding: 12px 20px; color: var(--color-body-text); font-size: 14px; font-weight: 500; cursor: pointer; margin-bottom: 8px; transition: all 0.2s ease;">
                      Upload
                    </button>
                    <p style="margin: 0; color: #999; font-size: 14px; line-height: 1.4;">Recommend size 1:1, upto 5MB.</p>
                  </div>
                </div>
              </div>

              <!-- Organization Name -->
              <div style="margin-bottom: 32px;">
                <label for="org-name" style="display: block; margin-bottom: 8px; color: var(--color-body-text); font-size: 16px; font-weight: 600;">Name</label>
                <input type="text" id="org-name" placeholder="Enter organization name" style="width: 100%; padding: 16px; border: 1px solid #e0e0e0; border-radius: var(--border-radius); font-size: 16px; color: var(--color-body-text); background: white; transition: all 0.2s ease; box-sizing: border-box;">
              </div>

              <!-- Slug URL -->
              <div style="margin-bottom: 40px;">
                <label for="org-slug" style="display: block; margin-bottom: 8px; color: var(--color-body-text); font-size: 16px; font-weight: 600;">Slug URL</label>
                <input type="text" id="org-slug" placeholder="my-org" value="my-org" style="width: 100%; padding: 16px; border: 1px solid #e0e0e0; border-radius: var(--border-radius); font-size: 16px; color: #999; background: white; transition: all 0.2s ease; box-sizing: border-box;">
              </div>

              <!-- Create Button -->
              <div style="margin-bottom: 32px;">
                <button type="submit" class="create-org-btn" style="width: 100%; padding: 16px; background: #2c2c2c; color: white; border: none; border-radius: var(--border-radius); font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease;">
                  Create organization
                </button>
              </div>
            </div>

            <!-- Footer -->
            <div style="padding: 20px 40px; border-top: 1px solid #f0f0f0; background: #fafafa; text-align: center;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 8px; color: #999; font-size: 14px;">
                <span>Secured by</span>
                <strong style="color: #666;">clerk</strong>
              </div>
            </div>

            <!-- Close Button -->
            <button id="close-org-modal" style="position: absolute; top: 20px; right: 20px; background: none; border: none; color: #999; font-size: 20px; cursor: pointer; padding: 8px; border-radius: 50%; transition: all 0.2s ease;">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- Standalone Form Example -->
        <h3 style="color: var(--color-body-text);">Standalone Form</h3>
        <div style="max-width: 500px; margin: 20px 0;">
          <div class="org-creation-form" style="background: white; border: 1px solid #e0e0e0; border-radius: 20px; padding: 40px; box-shadow: 0 4px 12px rgba(0,0,0,0.08);">

            <!-- Header -->
            <div style="margin-bottom: 40px;">
              <h2 style="margin: 0; color: var(--color-body-text); font-size: 28px; font-weight: 700; line-height: 1.2;">Create organization</h2>
            </div>

            <!-- Logo Upload Section -->
            <div style="margin-bottom: 32px;">
              <label style="display: block; margin-bottom: 12px; color: var(--color-body-text); font-size: 16px; font-weight: 600;">Logo</label>

              <div style="display: flex; align-items: flex-start; gap: 16px;">
                <!-- Logo Preview Area -->
                <div class="logo-upload-area-standalone" style="width: 120px; height: 120px; border: 2px dashed #d0d0d0; border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; background: #fafafa; cursor: pointer; transition: all 0.2s ease; position: relative; overflow: hidden;">
                  <div class="upload-placeholder" style="text-align: center; color: #999;">
                    <i class="fas fa-upload" style="font-size: 24px; margin-bottom: 8px; display: block;"></i>
                  </div>
                  <input type="file" id="logo-upload-standalone" accept="image/*" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer;">
                </div>

                <!-- Upload Instructions -->
                <div style="flex: 1; padding-top: 8px;">
                  <button type="button" class="upload-btn-standalone" style="background: none; border: 1px solid #d0d0d0; border-radius: var(--border-radius); padding: 12px 20px; color: var(--color-body-text); font-size: 14px; font-weight: 500; cursor: pointer; margin-bottom: 8px; transition: all 0.2s ease;">
                    Upload
                  </button>
                  <p style="margin: 0; color: #999; font-size: 14px; line-height: 1.4;">Recommend size 1:1, upto 5MB.</p>
                </div>
              </div>
            </div>

            <!-- Organization Name -->
            <div style="margin-bottom: 32px;">
              <label for="org-name-standalone" style="display: block; margin-bottom: 8px; color: var(--color-body-text); font-size: 16px; font-weight: 600;">Name</label>
              <input type="text" id="org-name-standalone" placeholder="Enter organization name" style="width: 100%; padding: 16px; border: 1px solid #e0e0e0; border-radius: var(--border-radius); font-size: 16px; color: var(--color-body-text); background: white; transition: all 0.2s ease; box-sizing: border-box;">
            </div>

            <!-- Slug URL -->
            <div style="margin-bottom: 40px;">
              <label for="org-slug-standalone" style="display: block; margin-bottom: 8px; color: var(--color-body-text); font-size: 16px; font-weight: 600;">Slug URL</label>
              <input type="text" id="org-slug-standalone" placeholder="my-org" value="my-org" style="width: 100%; padding: 16px; border: 1px solid #e0e0e0; border-radius: var(--border-radius); font-size: 16px; color: #999; background: white; transition: all 0.2s ease; box-sizing: border-box;">
            </div>

            <!-- Create Button -->
            <div style="margin-bottom: 32px;">
              <button type="submit" class="create-org-btn-standalone" style="width: 100%; padding: 16px; background: #2c2c2c; color: white; border: none; border-radius: var(--border-radius); font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease;">
                Create organization
              </button>
            </div>

            <!-- Footer -->
            <div style="padding-top: 20px; border-top: 1px solid #f0f0f0; text-align: center;">
              <div style="display: flex; align-items: center; justify-content: center; gap: 8px; color: #999; font-size: 14px;">
                <span>Secured by</span>
                <strong style="color: #666;">clerk</strong>
              </div>
            </div>
          </div>
        </div>
      </section>


      <!-- =================================================================
           ORGANIZATION PROFILE COMPONENTS
           ================================================================= -->
           <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Organization Profile Components</h2>

        <h3 style="color: var(--color-body-text);">Organization Settings Interface</h3>
        <div style="margin: 20px 0; background: white; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; max-width: 1000px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">

          <div style="display: grid; grid-template-columns: 280px 1fr; min-height: 600px;">

            <!-- Left Sidebar -->
            <div style="background: #f9fafb; border-right: 1px solid #e5e7eb; padding: 30px 0;">

              <!-- Header -->
              <div style="padding: 0 24px 30px;">
                <h2 style="color: var(--color-body-text); font-size: 24px; font-weight: 700; margin: 0 0 8px;">Organization</h2>
                <p style="color: #6b7280; font-size: 14px; margin: 0;">Manage your organization.</p>
              </div>

              <!-- Navigation Menu -->
              <nav style="padding: 0;">

                <!-- General Tab (Active) -->
                <div style="background: #e5e7eb; border-right: 3px solid var(--color-secondary); margin: 0 0 4px;">
                  <div style="padding: 12px 24px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-building" style="color: var(--color-body-text); font-size: 16px; width: 16px;"></i>
                    <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">General</span>
                  </div>
                </div>

                <!-- Members Tab -->
                <div style="margin: 0 0 4px; transition: background 0.2s ease;" onmouseover="this.style.background='#f3f4f6'" onmouseout="this.style.background='transparent'">
                  <div style="padding: 12px 24px; display: flex; align-items: center; gap: 12px; cursor: pointer;">
                    <i class="fas fa-users" style="color: #9ca3af; font-size: 16px; width: 16px;"></i>
                    <span style="color: #6b7280; font-size: 16px;">Members</span>
                  </div>
                </div>

              </nav>

              <!-- Footer -->
              <div style="position: absolute; bottom: 24px; left: 24px; display: flex; align-items: center; gap: 8px;">
                <span style="color: #9ca3af; font-size: 12px;">Secured by</span>
                <div style="display: flex; align-items: center; gap: 4px;">
                  <div style="width: 16px; height: 16px; background: var(--color-body-text); border-radius: 2px; display: flex; align-items: center; justify-content: center;">
                    <span style="color: white; font-size: 10px; font-weight: bold;">C</span>
                  </div>
                  <span style="color: #6b7280; font-size: 12px; font-weight: 600;">clerk</span>
                </div>
              </div>

            </div>

            <!-- Main Content Area -->
            <div style="padding: 40px;">

              <!-- Page Header -->
              <div style="margin-bottom: 40px;">
                <h1 style="color: var(--color-body-text); font-size: 28px; font-weight: 700; margin: 0;">General details</h1>
              </div>

              <!-- Organization Profile Section -->
              <div style="margin-bottom: 40px;">
                <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin: 0 0 20px;">Organization profile</h3>

                <div style="display: flex; align-items: center; justify-content: space-between; padding: 20px; background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px;">
                  <div style="display: flex; align-items: center; gap: 16px;">
                    <!-- Organization Avatar -->
                    <div style="width: 48px; height: 48px; background: #6b7280; border-radius: 50%; display: flex; align-items: center; justify-content: center; position: relative;">
                      <i class="fas fa-user" style="color: white; font-size: 20px;"></i>
                      <!-- Small badge -->
                      <div style="position: absolute; bottom: -2px; right: -2px; width: 16px; height: 16px; background: #10b981; border: 2px solid white; border-radius: 50%;"></div>
                    </div>
                    <div>
                      <h4 style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin: 0;">Clerk Sample Apps</h4>
                    </div>
                  </div>
                  <button style="background: transparent; border: 1px solid #d1d5db; color: #374151; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.borderColor='#9ca3af'; this.style.background='#f9fafb'" onmouseout="this.style.borderColor='#d1d5db'; this.style.background='transparent'">
                    Edit profile
                  </button>
                </div>
              </div>

              <!-- Verified Domains Section -->
              <div style="margin-bottom: 40px;">
                <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin: 0 0 20px;">Verified domains</h3>

                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px;">
                  <div style="display: flex; align-items: flex-start; gap: 16px;">
                    <button style="background: transparent; border: 1px dashed #d1d5db; color: #6b7280; padding: 8px; border-radius: 6px; cursor: pointer; transition: all 0.2s ease; margin-top: 4px;" onmouseover="this.style.borderColor='#9ca3af'; this.style.color='#374151'" onmouseout="this.style.borderColor='#d1d5db'; this.style.color='#6b7280'">
                      <i class="fas fa-plus" style="font-size: 14px;"></i>
                    </button>
                    <div style="flex: 1;">
                      <h4 style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin: 0 0 8px;">Add domain</h4>
                      <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin: 0;">Allow users to join the organization automatically or request to join based on a verified email domain.</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Leave Organization Section -->
              <div style="margin-bottom: 40px;">
                <h3 style="color: var(--color-body-text); font-size: 18px; font-weight: 600; margin: 0 0 20px;">Leave organization</h3>

                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <span style="color: var(--color-body-text); font-size: 16px;">Leave organization</span>
                  <button style="background: transparent; border: 1px solid #ef4444; color: #ef4444; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='#ef4444'; this.style.color='white'" onmouseout="this.style.background='transparent'; this.style.color='#ef4444'">
                    Leave
                  </button>
                </div>
              </div>

            </div>

          </div>

        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Dark Theme Variation</h3>
        <div style="margin: 20px 0; background: #1f2937; border: 1px solid #374151; border-radius: 12px; overflow: hidden; max-width: 1000px; box-shadow: 0 1px 3px rgba(0,0,0,0.3);">

          <div style="display: grid; grid-template-columns: 280px 1fr; min-height: 600px;">

            <!-- Dark Sidebar -->
            <div style="background: #111827; border-right: 1px solid #374151; padding: 30px 0;">

              <!-- Dark Header -->
              <div style="padding: 0 24px 30px;">
                <h2 style="color: white; font-size: 24px; font-weight: 700; margin: 0 0 8px;">Organization</h2>
                <p style="color: #9ca3af; font-size: 14px; margin: 0;">Manage your organization.</p>
              </div>

              <!-- Dark Navigation -->
              <nav style="padding: 0;">

                <!-- Dark General Tab (Active) -->
                <div style="background: #374151; border-right: 3px solid var(--color-secondary); margin: 0 0 4px;">
                  <div style="padding: 12px 24px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-building" style="color: white; font-size: 16px; width: 16px;"></i>
                    <span style="color: white; font-size: 16px; font-weight: 600;">General</span>
                  </div>
                </div>

                <!-- Dark Members Tab -->
                <div style="margin: 0 0 4px; transition: background 0.2s ease;" onmouseover="this.style.background='#1f2937'" onmouseout="this.style.background='transparent'">
                  <div style="padding: 12px 24px; display: flex; align-items: center; gap: 12px; cursor: pointer;">
                    <i class="fas fa-users" style="color: #6b7280; font-size: 16px; width: 16px;"></i>
                    <span style="color: #9ca3af; font-size: 16px;">Members</span>
                  </div>
                </div>

              </nav>

              <!-- Dark Footer -->
              <div style="position: absolute; bottom: 24px; left: 24px; display: flex; align-items: center; gap: 8px;">
                <span style="color: #6b7280; font-size: 12px;">Secured by</span>
                <div style="display: flex; align-items: center; gap: 4px;">
                  <div style="width: 16px; height: 16px; background: white; border-radius: 2px; display: flex; align-items: center; justify-content: center;">
                    <span style="color: #111827; font-size: 10px; font-weight: bold;">C</span>
                  </div>
                  <span style="color: #9ca3af; font-size: 12px; font-weight: 600;">clerk</span>
                </div>
              </div>

            </div>

            <!-- Dark Main Content -->
            <div style="padding: 40px; background: #1f2937;">

              <!-- Dark Page Header -->
              <div style="margin-bottom: 40px;">
                <h1 style="color: white; font-size: 28px; font-weight: 700; margin: 0;">General details</h1>
              </div>

              <!-- Dark Organization Profile -->
              <div style="margin-bottom: 40px;">
                <h3 style="color: white; font-size: 18px; font-weight: 600; margin: 0 0 20px;">Organization profile</h3>

                <div style="display: flex; align-items: center; justify-content: space-between; padding: 20px; background: #111827; border: 1px solid #374151; border-radius: 8px;">
                  <div style="display: flex; align-items: center; gap: 16px;">
                    <div style="width: 48px; height: 48px; background: #6b7280; border-radius: 50%; display: flex; align-items: center; justify-content: center; position: relative;">
                      <i class="fas fa-user" style="color: white; font-size: 20px;"></i>
                      <div style="position: absolute; bottom: -2px; right: -2px; width: 16px; height: 16px; background: #10b981; border: 2px solid #111827; border-radius: 50%;"></div>
                    </div>
                    <div>
                      <h4 style="color: white; font-size: 16px; font-weight: 600; margin: 0;">Clerk Sample Apps</h4>
                    </div>
                  </div>
                  <button style="background: transparent; border: 1px solid #4b5563; color: #d1d5db; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.borderColor='#6b7280'; this.style.background='#374151'" onmouseout="this.style.borderColor='#4b5563'; this.style.background='transparent'">
                    Edit profile
                  </button>
                </div>
              </div>

              <!-- Dark Verified Domains -->
              <div style="margin-bottom: 40px;">
                <h3 style="color: white; font-size: 18px; font-weight: 600; margin: 0 0 20px;">Verified domains</h3>

                <div style="border: 1px solid #374151; border-radius: 8px; padding: 20px; background: #111827;">
                  <div style="display: flex; align-items: flex-start; gap: 16px;">
                    <button style="background: transparent; border: 1px dashed #4b5563; color: #9ca3af; padding: 8px; border-radius: 6px; cursor: pointer; transition: all 0.2s ease; margin-top: 4px;" onmouseover="this.style.borderColor='#6b7280'; this.style.color='#d1d5db'" onmouseout="this.style.borderColor='#4b5563'; this.style.color='#9ca3af'">
                      <i class="fas fa-plus" style="font-size: 14px;"></i>
                    </button>
                    <div style="flex: 1;">
                      <h4 style="color: white; font-size: 16px; font-weight: 600; margin: 0 0 8px;">Add domain</h4>
                      <p style="color: #9ca3af; font-size: 14px; line-height: 1.5; margin: 0;">Allow users to join the organization automatically or request to join based on a verified email domain.</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Dark Leave Organization -->
              <div style="margin-bottom: 40px;">
                <h3 style="color: white; font-size: 18px; font-weight: 600; margin: 0 0 20px;">Leave organization</h3>

                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <span style="color: white; font-size: 16px;">Leave organization</span>
                  <button style="background: transparent; border: 1px solid #ef4444; color: #ef4444; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='#ef4444'; this.style.color='white'" onmouseout="this.style.background='transparent'; this.style.color='#ef4444'">
                    Leave
                  </button>
                </div>
              </div>

            </div>

          </div>

        </div>
      </section>

      <!-- =================================================================
           FORM ELEMENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text); font-size: 2.5rem; margin-bottom: 40px;">Form Elements</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 40px;">
          <!-- Left Column - Basic Inputs -->
          <div>
            <h3 style="color: var(--color-body-text); margin-bottom: 20px; border-bottom: 2px solid var(--color-secondary); padding-bottom: 8px;">Basic Input Types</h3>

            <form style="max-width: 100%;">
          <div style="margin-bottom: 10px;">
            <label for="text-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Text Input:</label>
            <input type="text" id="text-input" placeholder="Enter text here" style="width: 100%; max-width: 400px; padding: 6px;">
          </div>

          <div style="margin-bottom: 10px;">
            <label for="email-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Email Input:</label>
            <input type="email" id="email-input" placeholder="<EMAIL>" style="width: 100%; max-width: 350px; padding: 6px;">
          </div>

          <div style="margin-bottom: 10px;">
            <label for="password-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Password Input:</label>
            <input type="password" id="password-input" placeholder="Password" style="width: 100%; max-width: 300px; padding: 6px;">
          </div>

          <div style="margin-bottom: 20px;">
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Floating Label Input:</label>
            <div class="floating-label-container" style="position: relative; width: 100%; max-width: 400px;">
              <input type="text" id="floating-input" class="floating-input" placeholder=" ">
              <label for="floating-input" class="floating-label">Enter your name</label>
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label for="side-label-input" style="display: inline-block; font-size: 12px; font-weight: 600; margin-right: 10px; color: var(--color-secondary); width: 100px;">Side Label:</label>
            <input type="text" id="side-label-input" placeholder="Input with side label" style="width: 300px;">
          </div>

          <div style="margin-bottom: 20px;">
            <label for="number-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Number Input:</label>
            <input type="number" id="number-input" placeholder="123" style="width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius);">
          </div>

          <div style="margin-bottom: 20px;">
            <label for="search-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Search Input:</label>
            <input type="search" id="search-input" placeholder="Search..." style="width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius);">
          </div>

          <div style="margin-bottom: 20px;">
            <label for="select-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Standard Select Dropdown:</label>
            <select id="select-input" style="width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius);">
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </select>
          </div>

          <div style="margin-bottom: 20px;">
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Single Select Searchable Dropdown:</label>
            <div class="custom-dropdown" style="position: relative; width: 100%; max-width: 300px;">
              <input type="text" id="dropdown-search" placeholder="Search options..." style="width: 100%; padding: 10px 35px 10px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer;" readonly>
              <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666; pointer-events: none;"></i>
              <div class="dropdown-options" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                <div class="dropdown-search-container" style="padding: 8px; border-bottom: 1px solid #eee;">
                  <input type="text" class="dropdown-search-input" placeholder="Type to search..." style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                </div>
                <div class="dropdown-option" data-value="united-states" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇺🇸 United States</div>
                <div class="dropdown-option" data-value="canada" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇨🇦 Canada</div>
                <div class="dropdown-option" data-value="united-kingdom" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇬🇧 United Kingdom</div>
                <div class="dropdown-option" data-value="germany" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇩🇪 Germany</div>
                <div class="dropdown-option" data-value="france" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇫🇷 France</div>
                <div class="dropdown-option" data-value="japan" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇯🇵 Japan</div>
                <div class="dropdown-option" data-value="australia" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇦🇺 Australia</div>
                <div class="dropdown-option" data-value="brazil" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇧🇷 Brazil</div>
                <div class="dropdown-option" data-value="india" style="padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">🇮🇳 India</div>
                <div class="dropdown-option" data-value="china" style="padding: 10px; cursor: pointer;">🇨🇳 China</div>
              </div>
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Multi-Select with Infinite Scroll & AJAX Search:</label>
            <div class="multi-select-dropdown-ajax" style="position: relative; width: 100%; max-width: 400px;">
              <div class="multi-select-display" style="min-height: 40px; padding: 8px 35px 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; background: white; display: flex; flex-wrap: wrap; gap: 4px; align-items: center;">
                <span class="multi-select-placeholder" style="color: #999; font-size: 14px;">Search tags...</span>
              </div>
              <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666; pointer-events: none;"></i>
              <div class="multi-select-options" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 300px; overflow-y: auto; z-index: 1000; display: none;">
                <div class="multi-select-search-container" style="padding: 8px; border-bottom: 1px solid #eee; position: sticky; top: 0; background: white; z-index: 1;">
                  <input type="text" class="multi-select-search-input" placeholder="Search tags..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                  <div style="font-size: 10px; color: #666; margin-top: 4px;">Type to search thousands of tags...</div>
                </div>
                <div class="multi-select-options-container">
                  <!-- Options will be loaded here via AJAX -->
                  <div class="loading-indicator" style="padding: 20px; text-align: center; color: #666; font-size: 12px;">
                    <i class="fas fa-spinner fa-spin"></i> Loading tags...
                  </div>
                </div>
                <div class="load-more-indicator" style="padding: 10px; text-align: center; color: #666; font-size: 11px; border-top: 1px solid #f0f0f0; display: none;">
                  <i class="fas fa-spinner fa-spin"></i> Loading more...
                </div>
              </div>
            </div>
            <div style="font-size: 11px; color: #666; margin-top: 4px;">
              💡 <strong>Features:</strong> AJAX search, infinite scroll (10 items at a time), searches both loaded options and server
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Radio Select Groups:</label>

            <!-- Standard Radio Group -->
            <div style="margin-bottom: 30px;">
              <label style="display: block; margin-bottom: 12px; font-weight: bold;">Choose Your Plan:</label>
              <div class="radio-group" style="display: flex; flex-direction: column; gap: 12px;">
                <label class="radio-option" style="display: flex; align-items: center; padding: 12px; border: 2px solid #e0e0e0; border-radius: var(--border-radius); cursor: pointer; transition: all 0.3s ease; background: white;">
                  <input type="radio" name="plan_demo" value="basic" style="margin-right: 12px; transform: scale(1.2);">
                  <div>
                    <div style="font-weight: 600; color: var(--color-body-text);">Basic Plan</div>
                    <div style="font-size: 12px; color: #666; margin-top: 2px;">$9/month - Essential features</div>
                  </div>
                </label>
                <label class="radio-option radio-option-selected" style="display: flex; align-items: center; padding: 12px; border: 2px solid var(--color-secondary); border-radius: var(--border-radius); cursor: pointer; transition: all 0.3s ease; background: #f8f9ff;">
                  <input type="radio" name="plan_demo" value="pro" style="margin-right: 12px; transform: scale(1.2);" checked>
                  <div>
                    <div style="font-weight: 600; color: var(--color-body-text);">Pro Plan</div>
                    <div style="font-size: 12px; color: #666; margin-top: 2px;">$19/month - Advanced features</div>
                  </div>
                </label>
                <label class="radio-option" style="display: flex; align-items: center; padding: 12px; border: 2px solid #e0e0e0; border-radius: var(--border-radius); cursor: pointer; transition: all 0.3s ease; background: white;">
                  <input type="radio" name="plan_demo" value="enterprise" style="margin-right: 12px; transform: scale(1.2);">
                  <div>
                    <div style="font-weight: 600; color: var(--color-body-text);">Enterprise Plan</div>
                    <div style="font-size: 12px; color: #666; margin-top: 2px;">$49/month - All features + support</div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Compact Radio Group -->
            <div style="margin-bottom: 30px;">
              <label style="display: block; margin-bottom: 12px; font-weight: bold;">Content Type:</label>
              <div class="radio-group-compact" style="display: flex; gap: 8px; flex-wrap: wrap;">
                <label class="radio-compact" style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; transition: all 0.3s ease; background: white; font-size: 14px;">
                  <input type="radio" name="content_type_demo" value="post" style="margin-right: 6px;">
                  Posts
                </label>
                <label class="radio-compact radio-compact-selected" style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid var(--color-secondary); border-radius: var(--border-radius); cursor: pointer; transition: all 0.3s ease; background: var(--color-secondary); color: white; font-size: 14px;">
                  <input type="radio" name="content_type_demo" value="page" style="margin-right: 6px;" checked>
                  Pages
                </label>
                <label class="radio-compact" style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; transition: all 0.3s ease; background: white; font-size: 14px;">
                  <input type="radio" name="content_type_demo" value="product" style="margin-right: 6px;">
                  Products
                </label>
                <label class="radio-compact" style="display: flex; align-items: center; padding: 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; transition: all 0.3s ease; background: white; font-size: 14px;">
                  <input type="radio" name="content_type_demo" value="event" style="margin-right: 6px;">
                  Events
                </label>
              </div>
            </div>

            <!-- Button-Style Radio Group -->
            <div style="margin-bottom: 30px;">
              <label style="display: block; margin-bottom: 12px; font-weight: bold;">Priority Level:</label>
              <div class="radio-group-buttons" style="display: flex; gap: 0; border: 1px solid #ddd; border-radius: var(--border-radius); overflow: hidden; width: fit-content;">
                <label class="radio-button" style="display: flex; align-items: center; justify-content: center; padding: 8px 16px; cursor: pointer; transition: all 0.3s ease; background: white; border-right: 1px solid #ddd; font-size: 14px; font-weight: 500; min-height: 36px;">
                  <input type="radio" name="priority_demo" value="low" style="display: none;">
                  Low
                </label>
                <label class="radio-button radio-button-selected" style="display: flex; align-items: center; justify-content: center; padding: 8px 16px; cursor: pointer; transition: all 0.3s ease; background: var(--color-secondary); color: white; border-right: 1px solid #ddd; font-size: 14px; font-weight: 500; min-height: 36px;">
                  <input type="radio" name="priority_demo" value="medium" style="display: none;" checked>
                  Medium
                </label>
                <label class="radio-button" style="display: flex; align-items: center; justify-content: center; padding: 8px 16px; cursor: pointer; transition: all 0.3s ease; background: white; font-size: 14px; font-weight: 500; min-height: 36px;">
                  <input type="radio" name="priority_demo" value="high" style="display: none;">
                  High
                </label>
              </div>
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label for="textarea-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Auto-Growing Textarea (Apple Messages Style):</label>
            <div style="position: relative; width: 100%; max-width: 500px;">
              <textarea id="textarea-input" class="auto-grow-textarea" placeholder="Start typing and watch me grow..." style="width: 100%; min-height: 40px; max-height: 200px; padding: 12px; border: 1px solid #ddd; border-radius: var(--border-radius); resize: none; overflow-y: hidden; font-family: inherit; line-height: 1.4;"></textarea>
              <div class="character-count" style="position: absolute; bottom: 8px; right: 12px; font-size: 11px; color: #999; background: rgba(255,255,255,0.9); padding: 2px 4px; border-radius: 3px;">
                <span id="char-count">0</span>/<span id="char-max">280</span>
              </div>
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label for="textarea-standard" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Standard Textarea:</label>
            <textarea id="textarea-standard" placeholder="Standard textarea with character count" style="width: 100%; max-width: 500px; height: 100px; padding: 12px; border: 1px solid #ddd; border-radius: var(--border-radius); font-family: inherit;"></textarea>
            <div style="text-align: right; font-size: 11px; color: #999; margin-top: 4px;">
              <span id="standard-char-count">0</span>/500 characters
            </div>
          </div>

            </form>
          </div>

          <!-- Right Column - Advanced Inputs -->
          <div>
            <h3 style="color: var(--color-body-text); margin-bottom: 20px; border-bottom: 2px solid var(--color-secondary); padding-bottom: 8px;">Checkboxes & Radio Buttons</h3>

            <div style="margin-bottom: 25px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Checkboxes</h4>
              <div style="margin-bottom: 15px;">
                <input type="checkbox" id="checkbox1" class="custom-checkbox">
                <label for="checkbox1" class="custom-checkbox-label">Checkbox Option 1</label>
              </div>

              <div style="margin-bottom: 15px;">
                <input type="checkbox" id="checkbox2" class="custom-checkbox" checked>
                <label for="checkbox2" class="custom-checkbox-label">Checkbox Option 2 (Checked)</label>
              </div>
            </div>

            <div style="margin-bottom: 25px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Radio Buttons</h4>
              <div style="margin-bottom: 15px;">
                <input type="radio" id="radio1" name="radio-group" value="option1" class="custom-radio">
                <label for="radio1" class="custom-radio-label">Radio Option 1</label>
              </div>

              <div style="margin-bottom: 15px;">
                <input type="radio" id="radio2" name="radio-group" value="option2" class="custom-radio" checked>
                <label for="radio2" class="custom-radio-label">Radio Option 2 (Selected)</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Full Width Advanced Form Elements -->
        <div style="margin-top: 40px;">
          <h3 style="color: var(--color-body-text); margin-bottom: 20px; border-bottom: 2px solid var(--color-secondary); padding-bottom: 8px;">Advanced Form Elements</h3>

          <form style="max-width: 800px;">

          <div style="margin-bottom: 20px;">
            <label for="date-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Date Input:</label>
            <input type="date" id="date-input" style="width: 200px; padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius); color-scheme: light;">
          </div>

          <div style="margin-bottom: 20px;">
            <label for="datetime-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">DateTime Input:</label>
            <input type="datetime-local" id="datetime-input" style="width: 250px; padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius); color-scheme: light;">
          </div>

          <div style="margin-bottom: 20px;">
            <label for="time-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Time Input:</label>
            <input type="time" id="time-input" style="width: 150px; padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius); color-scheme: light;">
          </div>

          <div style="margin-bottom: 20px;">
            <label for="cc-input" style="display: block; font-size: 14px; font-weight: 500; margin-bottom: 6px; color: var(--color-body-text);">Credit Card Input:</label>
            <input type="text" id="cc-input" placeholder="1234 5678 9012 3456" maxlength="19" style="width: 300px; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-family: monospace; letter-spacing: 1px; background: #f8f9fa; color: var(--color-body-text); transition: all 0.2s ease;" oninput="formatCreditCard(this)" onfocus="this.style.borderColor='var(--color-secondary)'" onblur="this.style.borderColor='#e0e0e0'">
          </div>

          <div style="margin-bottom: 20px;">
            <label for="color-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Color Picker:</label>
            <input type="color" id="color-input" value="#1569f3" style="width: 60px; height: 40px; border: 1px solid #ddd; border-radius: var(--border-radius);">
          </div>

          <div style="margin-bottom: 20px;">
            <label for="url-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">URL Input:</label>
            <input type="url" id="url-input" placeholder="https://example.com" style="width: 100%; max-width: 400px; padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius);">
          </div>

          <div style="margin-bottom: 20px;">
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Standard File Upload:</label>
            <input type="file" id="file-input" style="padding: 8px; border: 1px solid #ddd; border-radius: var(--border-radius);">
          </div>

          <div style="margin-bottom: 20px;">
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Drag & Drop File Upload:</label>
            <div class="file-drop-area" style="border: 2px dashed #ddd; border-radius: var(--border-radius); padding: 40px; text-align: center; background: #fafafa; cursor: pointer; transition: all 0.3s ease; width: 100%; max-width: 400px;">
              <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #ccc; margin-bottom: 15px; display: block;"></i>
              <p style="margin: 0 0 10px 0; font-size: 16px; color: #666;">Drag & drop files here</p>
              <p style="margin: 0; font-size: 14px; color: #999;">or <span style="color: var(--color-primary); text-decoration: underline;">click to browse</span></p>
              <input type="file" class="file-input-hidden" multiple style="display: none;">
              <div class="file-list" style="margin-top: 15px; text-align: left; display: none;">
                <h4 style="margin: 0 0 10px 0; font-size: 14px; color: var(--color-secondary);">Selected Files:</h4>
                <div class="file-items"></div>
              </div>
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label for="range-input" style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Standard Range Slider:</label>
            <div style="display: flex; align-items: center; gap: 15px;">
              <input type="range" id="range-input" min="0" max="100" value="50" style="width: 300px;">
              <span id="range-value" style="font-weight: 600; color: var(--color-primary);">50</span>
            </div>
          </div>

          <div style="margin-bottom: 20px;">
            <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Advanced Range Slider:</label>
            <div class="advanced-range-container" style="width: 100%; max-width: 400px;">
              <div style="display: flex; justify-content: space-between; margin-bottom: 8px; font-size: 12px; color: #666;">
                <span>0</span>
                <span id="advanced-range-value" style="font-weight: 600; color: var(--color-primary);">75</span>
                <span>100</span>
              </div>
              <div class="advanced-range-track" style="position: relative; height: 8px; background: #e0e0e0; border-radius: 4px; cursor: pointer;">
                <div class="advanced-range-fill" style="position: absolute; left: 0; top: 0; height: 100%; background: var(--color-secondary); border-radius: 4px; width: 75%; transition: width 0.2s ease;"></div>
                <div class="advanced-range-thumb" style="position: absolute; top: 50%; transform: translate(-50%, -50%); left: 75%; width: 20px; height: 20px; background: white; border: 3px solid var(--color-primary); border-radius: 50%; cursor: grab; box-shadow: 0 2px 6px rgba(0,0,0,0.2); transition: all 0.2s ease;"></div>
              </div>
              <input type="range" id="advanced-range" min="0" max="100" value="75" style="opacity: 0; position: absolute; pointer-events: none;">
            </div>
          </div>

          <button type="submit" class="baum-button baum-button-large">Submit Form</button>
          <button type="reset" class="baum-button baum-button-large">Reset Form</button>
          <a href="#" class="baum-button baum-button-large" style="text-decoration: none; display: inline-flex; align-items: center; justify-content: center;">Link Button</a>
        </form>

        <h3 style="color: var(--color-body-text);">Credit Card Checkout Forms</h3>
        <div style="display: flex; gap: 30px; margin: 20px 0;">
          <!-- Donation Form -->
          <div style="flex: 1; max-width: 500px;">
            <h4 style="color: var(--color-body-text);">Donation Form</h4>
          <div class="baum-checkout-form" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); border: 1px solid #f0f0f0;">

            <!-- Logo/Icon -->
            <div style="text-align: center; margin-bottom: 24px;">
              <div style="width: 60px; height: 60px; background: #333; border-radius: 12px; display: inline-flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                💝
              </div>
            </div>

            <!-- Header -->
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="margin: 0 0 6px 0; color: var(--color-body-text); font-size: 20px; font-weight: 700;">Make a donation</h2>
              <p style="margin: 0; color: #666; font-size: 14px;">Support our mission with a secure donation.</p>
            </div>

            <!-- Amount Selection -->
            <div class="amount-selection" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">Donation Amount</label>
              <div class="amount-buttons" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 12px;">
                <button type="button" class="amount-btn" data-amount="25" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; background: #f8f9fa; color: var(--color-body-text); font-weight: 500; cursor: pointer; transition: all 0.2s ease;">$25</button>
                <button type="button" class="amount-btn" data-amount="50" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; background: #f8f9fa; color: var(--color-body-text); font-weight: 500; cursor: pointer; transition: all 0.2s ease;">$50</button>
                <button type="button" class="amount-btn" data-amount="100" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; background: #f8f9fa; color: var(--color-body-text); font-weight: 500; cursor: pointer; transition: all 0.2s ease;">$100</button>
              </div>
              <div style="position: relative;">
                <span style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #666; font-weight: 500; font-size: 16px;">$</span>
                <input type="number" id="custom-amount" placeholder="Custom amount" style="width: 100%; padding: 12px 12px 12px 28px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); box-sizing: border-box; transition: all 0.2s ease;">
              </div>
            </div>

            <!-- Donation Type -->
            <div class="donation-type" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">Donation Type</label>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                <label class="radio-option" style="display: flex; align-items: center; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; background: #f8f9fa;">
                  <input type="radio" name="donation-type" value="one-time" checked style="margin-right: 8px;">
                  <span style="font-weight: 500;">One-time</span>
                </label>
                <label class="radio-option" style="display: flex; align-items: center; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; background: #f8f9fa;">
                  <input type="radio" name="donation-type" value="monthly" style="margin-right: 8px;">
                  <span style="font-weight: 500;">Monthly</span>
                </label>
              </div>
            </div>

            <!-- Personal Information -->
            <div class="personal-info" style="margin-bottom: 20px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 12px; font-size: 16px; font-weight: 600;">Personal Information</h4>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                <input type="text" placeholder="First Name" required style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); box-sizing: border-box; transition: all 0.2s ease;">
                <input type="text" placeholder="Last Name" required style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); box-sizing: border-box; transition: all 0.2s ease;">
              </div>
              <input type="email" placeholder="Email Address" required style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); margin-bottom: 12px; box-sizing: border-box; transition: all 0.2s ease;">
              <input type="tel" placeholder="Phone Number (Optional)" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); box-sizing: border-box; transition: all 0.2s ease;">
            </div>

            <!-- Payment Information -->
            <div class="payment-info" style="margin-bottom: 20px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 12px; font-size: 16px; font-weight: 600;">Payment Information</h4>

              <!-- Card Number -->
              <div style="position: relative; margin-bottom: 12px;">
                <input type="text" id="card-number" placeholder="1234 5678 9012 3456" maxlength="19" style="width: 100%; padding: 12px 50px 12px 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); font-family: monospace; box-sizing: border-box; transition: all 0.2s ease;">
                <div class="card-icons" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); display: flex; gap: 4px;">
                  <i class="fa-brands fa-cc-visa" style="color: #1a1f71; font-size: 18px;"></i>
                  <i class="fa-brands fa-cc-mastercard" style="color: #eb001b; font-size: 18px;"></i>
                  <i class="fa-brands fa-cc-amex" style="color: #006fcf; font-size: 18px;"></i>
                </div>
              </div>

              <!-- Expiry and CVC -->
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                <input type="text" id="card-expiry" placeholder="MM/YY" maxlength="5" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); font-family: monospace; box-sizing: border-box; transition: all 0.2s ease;">
                <div style="position: relative;">
                  <input type="text" id="card-cvc" placeholder="CVC" maxlength="4" style="width: 100%; padding: 12px 40px 12px 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); font-family: monospace; box-sizing: border-box; transition: all 0.2s ease;">
                  <i class="fa-solid fa-question-circle" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666; cursor: help;" title="3-digit code on back of card (4 digits for Amex)"></i>
                </div>
              </div>

              <!-- Cardholder Name -->
              <input type="text" placeholder="Cardholder Name" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); box-sizing: border-box; transition: all 0.2s ease;">
            </div>

            <!-- Billing Address -->
            <div class="billing-address" style="margin-bottom: 20px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 12px; font-size: 16px; font-weight: 600;">Billing Address</h4>
              <input type="text" placeholder="Street Address" style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); margin-bottom: 12px; box-sizing: border-box; transition: all 0.2s ease;">
              <div style="display: grid; grid-template-columns: 2fr 1fr 1fr; gap: 12px;">
                <input type="text" placeholder="City" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); box-sizing: border-box; transition: all 0.2s ease;">
                <input type="text" placeholder="State" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); box-sizing: border-box; transition: all 0.2s ease;">
                <input type="text" placeholder="ZIP" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); box-sizing: border-box; transition: all 0.2s ease;">
              </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="donate-btn" style="width: 100%; padding: 14px; background: #333; color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 8px; margin-top: 8px;">
              <i class="fa-solid fa-lock" style="font-size: 14px;"></i>
              <span class="btn-text">Donate $25 →</span>
            </button>

            <!-- Security Badge -->
            <div style="text-align: center; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
              <p style="margin: 0; color: #999; font-size: 10px; display: flex; align-items: center; justify-content: center; gap: 4px;">
                <i class="fa-solid fa-shield-halved" style="font-size: 10px;"></i>
                Secured by <strong style="color: #666;">stripe</strong>
              </p>
            </div>
          </div>
        </div>

          <!-- Subscription Form -->
          <div style="flex: 1; max-width: 500px;">
            <h4 style="color: var(--color-body-text);">Subscription Form</h4>
          <div class="baum-subscription-form" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); border: 1px solid #f0f0f0;">

            <!-- Logo/Icon -->
            <div style="text-align: center; margin-bottom: 24px;">
              <div style="width: 60px; height: 60px; background: #333; border-radius: 12px; display: inline-flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                💳
              </div>
            </div>

            <!-- Header -->
            <div style="text-align: center; margin-bottom: 20px;">
              <h2 style="margin: 0 0 6px 0; color: var(--color-body-text); font-size: 20px; font-weight: 700;">Choose your plan</h2>
              <p style="margin: 0; color: #666; font-size: 14px;">Start your subscription with secure payment.</p>
            </div>

            <!-- Plan Selection -->
            <div class="plan-selection" style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 6px; color: var(--color-body-text); font-size: 14px; font-weight: 500;">Choose Your Plan</label>
              <div class="plan-options" style="display: grid; gap: 8px;">
                <label class="plan-option" style="display: flex; align-items: center; justify-content: space-between; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; background: #f8f9fa;">
                  <div style="display: flex; align-items: center;">
                    <input type="radio" name="plan" value="basic" checked style="margin-right: 12px;">
                    <div>
                      <div style="font-weight: 500; color: var(--color-body-text); font-size: 14px;">Basic Plan</div>
                      <div style="font-size: 12px; color: #666;">Access to basic features</div>
                    </div>
                  </div>
                  <div style="font-weight: 600; color: var(--color-body-text); font-size: 16px;">$9/mo</div>
                </label>

                <label class="plan-option" style="display: flex; align-items: center; justify-content: space-between; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; background: #f8f9fa;">
                  <div style="display: flex; align-items: center;">
                    <input type="radio" name="plan" value="pro" style="margin-right: 12px;">
                    <div>
                      <div style="font-weight: 500; color: var(--color-body-text); font-size: 14px;">Pro Plan</div>
                      <div style="font-size: 12px; color: #666;">All features + priority support</div>
                    </div>
                  </div>
                  <div style="font-weight: 600; color: var(--color-body-text); font-size: 16px;">$19/mo</div>
                </label>

                <label class="plan-option" style="display: flex; align-items: center; justify-content: space-between; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; background: #f8f9fa;">
                  <div style="display: flex; align-items: center;">
                    <input type="radio" name="plan" value="enterprise" style="margin-right: 12px;">
                    <div>
                      <div style="font-weight: 500; color: var(--color-body-text); font-size: 14px;">Enterprise</div>
                      <div style="font-size: 12px; color: #666;">Custom solutions for teams</div>
                    </div>
                  </div>
                  <div style="font-weight: 600; color: var(--color-body-text); font-size: 16px;">$49/mo</div>
                </label>
              </div>
            </div>

            <!-- Payment Information -->
            <div class="payment-info" style="margin-bottom: 20px;">
              <h4 style="color: var(--color-body-text); margin-bottom: 12px; font-size: 16px; font-weight: 600;">Payment Information</h4>
              <input type="email" placeholder="Email Address" required style="width: 100%; padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); margin-bottom: 12px; box-sizing: border-box; transition: all 0.2s ease;">

              <div style="position: relative; margin-bottom: 12px;">
                <input type="text" id="sub-card-number" placeholder="1234 5678 9012 3456" maxlength="19" style="width: 100%; padding: 12px 50px 12px 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); font-family: monospace; box-sizing: border-box; transition: all 0.2s ease;">
                <div class="card-icons" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); display: flex; gap: 4px;">
                  <i class="fa-brands fa-cc-visa" style="color: #1a1f71; font-size: 18px;"></i>
                  <i class="fa-brands fa-cc-mastercard" style="color: #eb001b; font-size: 18px;"></i>
                </div>
              </div>

              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                <input type="text" id="sub-card-expiry" placeholder="MM/YY" maxlength="5" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); font-family: monospace; box-sizing: border-box; transition: all 0.2s ease;">
                <input type="text" id="sub-card-cvc" placeholder="CVC" maxlength="4" style="padding: 12px; border: 1px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: #f8f9fa; color: var(--color-body-text); font-family: monospace; box-sizing: border-box; transition: all 0.2s ease;">
              </div>
            </div>

            <!-- Subscribe Button -->
            <button type="submit" class="subscribe-btn" style="width: 100%; padding: 14px; background: #333; color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 8px; margin-top: 8px;">
              <i class="fa-solid fa-credit-card" style="font-size: 14px;"></i>
              <span class="sub-btn-text">Start Basic Plan - $9/mo →</span>
            </button>

            <!-- Footer -->
            <div style="text-align: center; margin-top: 16px;">
              <p style="margin: 0; color: #666; font-size: 12px;">
                Cancel anytime • No setup fees • 30-day money-back guarantee
              </p>
            </div>

            <!-- Security Badge -->
            <div style="text-align: center; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
              <p style="margin: 0; color: #999; font-size: 10px; display: flex; align-items: center; justify-content: center; gap: 4px;">
                <i class="fa-solid fa-shield-halved" style="font-size: 10px;"></i>
                Secured by <strong style="color: #666;">stripe</strong>
              </p>
            </div>
          </div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Form Element Population Methods</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: var(--border-radius); margin: 15px 0;">
          <p style="margin-bottom: 15px; font-weight: 600;">All form elements (selects, radios, checkboxes, multiselects) can be populated using multiple methods:</p>

          <div style="display: grid; gap: 15px;">


            <!-- Primary Author -->
            <div style="margin-bottom: 20px;">
              <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Primary Author</label>
              <div style="display: flex; align-items: center; gap: 12px; padding: 12px; border: 1px solid #e0e0e0; border-radius: var(--border-radius); background: #f8f9fa;">
                <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--color-secondary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 14px;">
                  <?php echo strtoupper(substr(wp_get_current_user()->display_name, 0, 1)); ?>
                </div>
                <div>
                  <div style="font-weight: 600; color: var(--color-body-text);"><?php echo wp_get_current_user()->display_name; ?></div>
                  <div style="font-size: 12px; color: #666;"><?php echo wp_get_current_user()->user_email; ?></div>
                </div>
              </div>
            </div>

            <!-- Co-Authors Multi-Select -->
            <div style="margin-bottom: 20px;">
              <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Co-Authors</label>
              <div class="multi-select-dropdown" id="co-authors-dropdown" style="position: relative; width: 100%;">
                <div class="multi-select-display" style="min-height: 40px; padding: 8px 35px 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; background: white; display: flex; flex-wrap: wrap; gap: 4px; align-items: center;">
                  <span class="multi-select-placeholder" style="color: #999; font-size: 14px;">Select co-authors...</span>
                </div>
                <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666; pointer-events: none;"></i>
                <div class="multi-select-options" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                  <div class="multi-select-search-container" style="padding: 8px; border-bottom: 1px solid #eee;">
                    <input type="text" class="multi-select-search-input" placeholder="Search authors..." style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                  </div>
                  <?php
                  $users = get_users(['role__in' => ['editor', 'author', 'contributor']]);
                  foreach ($users as $user) {
                    if ($user->ID !== get_current_user_id()) {
                      echo '<label class="multi-select-option" style="display: flex; align-items: center; padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">
                              <input type="checkbox" value="' . $user->ID . '" style="margin-right: 8px;">
                              <span>👤 ' . $user->display_name . '</span>
                            </label>';
                    }
                  }
                  ?>
                </div>
              </div>
            </div>

            <!-- Categories and Tags Row -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">

              <!-- Categories Multi-Select -->
              <div>
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Categories</label>
                <div class="multi-select-dropdown" id="categories-dropdown" style="position: relative; width: 100%;">
                  <div class="multi-select-display" style="min-height: 40px; padding: 8px 35px 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; background: white; display: flex; flex-wrap: wrap; gap: 4px; align-items: center;">
                    <span class="multi-select-placeholder" style="color: #999; font-size: 14px;">Select categories...</span>
                  </div>
                  <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666; pointer-events: none;"></i>
                  <div class="multi-select-options" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                    <div class="multi-select-search-container" style="padding: 8px; border-bottom: 1px solid #eee;">
                      <input type="text" class="multi-select-search-input" placeholder="Search categories..." style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                    </div>
                    <?php
                    $categories = get_categories(['hide_empty' => false]);
                    foreach ($categories as $category) {
                      echo '<label class="multi-select-option" style="display: flex; align-items: center; padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">
                              <input type="checkbox" value="' . $category->term_id . '" style="margin-right: 8px;">
                              <span>📁 ' . $category->name . '</span>
                            </label>';
                    }
                    ?>
                  </div>
                </div>
              </div>

              <!-- Tags Multi-Select -->
              <div>
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 4px; color: var(--color-secondary);">Tags</label>
                <div class="multi-select-dropdown" id="tags-dropdown" style="position: relative; width: 100%;">
                  <div class="multi-select-display" style="min-height: 40px; padding: 8px 35px 8px 12px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; background: white; display: flex; flex-wrap: wrap; gap: 4px; align-items: center;">
                    <span class="multi-select-placeholder" style="color: #999; font-size: 14px;">Select tags...</span>
                  </div>
                  <i class="fas fa-chevron-down" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #666; pointer-events: none;"></i>
                  <div class="multi-select-options" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                    <div class="multi-select-search-container" style="padding: 8px; border-bottom: 1px solid #eee;">
                      <input type="text" class="multi-select-search-input" placeholder="Search tags..." style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                    </div>
                    <?php
                    $tags = get_tags(['hide_empty' => false]);
                    foreach ($tags as $tag) {
                      echo '<label class="multi-select-option" style="display: flex; align-items: center; padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0;">
                              <input type="checkbox" value="' . $tag->term_id . '" style="margin-right: 8px;">
                              <span>🏷️ ' . $tag->name . '</span>
                            </label>';
                    }
                    ?>
                  </div>
                </div>
              </div>
            </div>

            <!-- HTML Method -->
            <div style="background: white; padding: 12px; border-radius: var(--border-radius); border-left: 4px solid var(--color-blue);">
              <h4 style="color: var(--color-body-text); margin-bottom: 8px;">📝 Static HTML</h4>
              <p style="margin-bottom: 8px; font-size: 14px;">Hardcode options directly in HTML for static content:</p>

              <!-- Single Select Example -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Single Select:</label>
                <select style="width: 200px; padding: 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <option value="">Choose option...</option>
                  <option value="option1">Option 1</option>
                  <option value="option2">Option 2</option>
                  <option value="option3">Option 3</option>
                </select>
              </div>

              <!-- Multiselect Example -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Multi Select:</label>
                <select multiple style="width: 200px; height: 80px; padding: 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <option value="html">HTML</option>
                  <option value="css">CSS</option>
                  <option value="js">JavaScript</option>
                  <option value="php">PHP</option>
                </select>
              </div>

              <pre style="background: #f1f1f1; padding: 8px; border-radius: 4px; font-size: 11px; overflow-x: auto;"><code>&lt;select&gt;
  &lt;option value="option1"&gt;Option 1&lt;/option&gt;
  &lt;option value="option2"&gt;Option 2&lt;/option&gt;
&lt;/select&gt;</code></pre>
            </div>

            <!-- WordPress Posts API -->
            <div style="background: white; padding: 12px; border-radius: var(--border-radius); border-left: 4px solid var(--color-green);">
              <h4 style="color: var(--color-body-text); margin-bottom: 8px;">📄 WordPress Posts API</h4>
              <p style="margin-bottom: 8px; font-size: 14px;">Populate with posts, pages, or custom post types:</p>

              <!-- Single Select Example -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Single Select (Posts):</label>
                <select style="width: 250px; padding: 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <option value="">Select a post...</option>
                  <?php
                  $posts = get_posts(['post_type' => 'post', 'numberposts' => 5]);
                  foreach($posts as $post) {
                    echo '<option value="' . $post->ID . '">' . esc_html($post->post_title) . '</option>';
                  }
                  ?>
                </select>
              </div>

              <!-- Multiselect Example -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Multi Select (Pages):</label>
                <select multiple style="width: 250px; height: 80px; padding: 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <?php
                  $pages = get_posts(['post_type' => 'page', 'numberposts' => 5]);
                  foreach($pages as $page) {
                    echo '<option value="' . $page->ID . '">' . esc_html($page->post_title) . '</option>';
                  }
                  ?>
                </select>
              </div>

              <pre style="background: #f1f1f1; padding: 8px; border-radius: 4px; font-size: 11px; overflow-x: auto;"><code>&lt;?php
$posts = get_posts(['post_type' => 'post', 'numberposts' => -1]);
foreach($posts as $post) {
  echo "&lt;option value='{$post->ID}'&gt;{$post->post_title}&lt;/option&gt;";
}
?&gt;</code></pre>
            </div>

            <!-- WordPress Taxonomies -->
            <div style="background: white; padding: 12px; border-radius: var(--border-radius); border-left: 4px solid var(--color-purple);">
              <h4 style="color: var(--color-body-text); margin-bottom: 8px;">🏷️ WordPress Taxonomies</h4>
              <p style="margin-bottom: 8px; font-size: 14px;">Populate with categories, tags, or custom taxonomies:</p>

              <!-- Single Select Example -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Single Select (Categories):</label>
                <select style="width: 250px; padding: 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <option value="">Select category...</option>
                  <?php
                  $categories = get_terms(['taxonomy' => 'category', 'hide_empty' => false, 'number' => 5]);
                  foreach($categories as $category) {
                    echo '<option value="' . $category->term_id . '">' . esc_html($category->name) . ' (' . $category->count . ')</option>';
                  }
                  ?>
                </select>
              </div>

              <!-- Multiselect Example -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Multi Select (Tags):</label>
                <select multiple style="width: 250px; height: 80px; padding: 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <?php
                  $tags = get_terms(['taxonomy' => 'post_tag', 'hide_empty' => false, 'number' => 8]);
                  foreach($tags as $tag) {
                    echo '<option value="' . $tag->term_id . '">' . esc_html($tag->name) . ' (' . $tag->count . ')</option>';
                  }
                  ?>
                </select>
              </div>

              <pre style="background: #f1f1f1; padding: 8px; border-radius: 4px; font-size: 11px; overflow-x: auto;"><code>&lt;?php
$terms = get_terms(['taxonomy' => 'category', 'hide_empty' => false]);
foreach($terms as $term) {
  echo "&lt;option value='{$term->term_id}'&gt;{$term->name}&lt;/option&gt;";
}
?&gt;</code></pre>
            </div>

            <!-- WordPress Users API -->
            <div style="background: white; padding: 12px; border-radius: var(--border-radius); border-left: 4px solid var(--color-orange);">
              <h4 style="color: var(--color-body-text); margin-bottom: 8px;">👥 WordPress Users API</h4>
              <p style="margin-bottom: 8px; font-size: 14px;">Populate with user data:</p>

              <!-- Single Select Example -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Single Select (Authors):</label>
                <select style="width: 250px; padding: 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <option value="">Select author...</option>
                  <?php
                  $users = get_users(['role__in' => ['author', 'editor'], 'number' => 5]);
                  foreach($users as $user) {
                    echo '<option value="' . $user->ID . '">' . esc_html($user->display_name) . ' (' . $user->user_login . ')</option>';
                  }
                  ?>
                </select>
              </div>

              <!-- Multiselect Example -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">Multi Select (All Users):</label>
                <select multiple style="width: 250px; height: 80px; padding: 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <?php
                  $all_users = get_users(['number' => 8]);
                  foreach($all_users as $user) {
                    echo '<option value="' . $user->ID . '">' . esc_html($user->display_name) . ' (' . $user->user_email . ')</option>';
                  }
                  ?>
                </select>
              </div>

              <pre style="background: #f1f1f1; padding: 8px; border-radius: 4px; font-size: 11px; overflow-x: auto;"><code>&lt;?php
$users = get_users(['role__in' => ['author', 'editor']]);
foreach($users as $user) {
  echo "&lt;option value='{$user->ID}'&gt;{$user->display_name}&lt;/option&gt;";
}
?&gt;</code></pre>
            </div>

            <!-- REST API -->
            <div style="background: white; padding: 12px; border-radius: var(--border-radius); border-left: 4px solid var(--color-teal);">
              <h4 style="color: var(--color-body-text); margin-bottom: 8px;">🔗 REST API / AJAX</h4>
              <p style="margin-bottom: 8px; font-size: 14px;">Dynamically populate via JavaScript with infinite scroll:</p>

              <!-- AJAX Single Select -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">AJAX Single Select:</label>
                <div class="ajax-single-select" style="position: relative; width: 250px;">
                  <input type="text" placeholder="Search posts..." style="width: 100%; padding: 6px 25px 6px 6px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 12px;">
                  <i class="fas fa-chevron-down" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); color: #666; font-size: 10px;"></i>
                  <div class="ajax-dropdown" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 150px; overflow-y: auto; z-index: 1000; display: none;">
                    <div style="padding: 8px; text-align: center; color: #666; font-size: 11px;">
                      <i class="fas fa-spinner fa-spin"></i> Loading...
                    </div>
                  </div>
                </div>
              </div>

              <!-- AJAX Multiselect -->
              <div style="margin-bottom: 10px;">
                <label style="display: block; font-size: 12px; font-weight: 600; margin-bottom: 2px; color: var(--color-secondary);">AJAX Multi Select (Fixed):</label>
                <div class="multi-select-dropdown-ajax-fixed" style="position: relative; width: 250px;">
                  <div class="multi-select-display" style="min-height: 32px; padding: 4px 25px 4px 6px; border: 1px solid #ddd; border-radius: var(--border-radius); cursor: pointer; background: white; display: flex; flex-wrap: wrap; gap: 2px; align-items: center;">
                    <span class="multi-select-placeholder" style="color: #999; font-size: 12px;">Search tags...</span>
                  </div>
                  <i class="fas fa-chevron-down" style="position: absolute; right: 8px; top: 50%; transform: translateY(-50%); color: #666; font-size: 10px; pointer-events: none;"></i>
                  <div class="multi-select-options" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 var(--border-radius) var(--border-radius); max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                    <div class="multi-select-search-container" style="padding: 6px; border-bottom: 1px solid #eee; position: sticky; top: 0; background: white; z-index: 1;">
                      <input type="text" class="multi-select-search-input" placeholder="Search..." style="width: 100%; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;">
                    </div>
                    <div class="multi-select-options-container">
                      <div class="loading-indicator" style="padding: 15px; text-align: center; color: #666; font-size: 11px;">
                        <i class="fas fa-spinner fa-spin"></i> Loading tags...
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <pre style="background: #f1f1f1; padding: 8px; border-radius: 4px; font-size: 11px; overflow-x: auto;"><code>fetch('/wp-json/wp/v2/posts?per_page=10&page=' + page)
  .then(response => response.json())
  .then(posts => {
    posts.forEach(post => {
      const option = new Option(post.title.rendered, post.id);
      selectElement.add(option);
    });
  });</code></pre>
            </div>

            <!-- Custom Fields -->
            <div style="background: white; padding: 15px; border-radius: var(--border-radius); border-left: 4px solid var(--color-red);">
              <h4 style="color: var(--color-body-text); margin-bottom: 10px;">⚙️ Custom Fields (ACF)</h4>
              <p style="margin-bottom: 10px; font-size: 14px;">Populate from Advanced Custom Fields:</p>
              <pre style="background: #f1f1f1; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;"><code>&lt;?php
$field = get_field_object('field_name');
if($field['choices']) {
  foreach($field['choices'] as $value => $label) {
    echo "&lt;option value='{$value}'&gt;{$label}&lt;/option&gt;";
  }
}
?&gt;</code></pre>
            </div>

            <!-- Database Queries -->
            <div style="background: white; padding: 15px; border-radius: var(--border-radius); border-left: 4px solid var(--color-gray);">
              <h4 style="color: var(--color-body-text); margin-bottom: 10px;">🗄️ Custom Database Queries</h4>
              <p style="margin-bottom: 10px; font-size: 14px;">Populate from custom database tables:</p>
              <pre style="background: #f1f1f1; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;"><code>&lt;?php
global $wpdb;
$results = $wpdb->get_results("SELECT id, name FROM custom_table");
foreach($results as $row) {
  echo "&lt;option value='{$row->id}'&gt;{$row->name}&lt;/option&gt;";
}
?&gt;</code></pre>
            </div>

            <!-- External APIs -->
            <div style="background: white; padding: 15px; border-radius: var(--border-radius); border-left: 4px solid var(--color-pink);">
              <h4 style="color: var(--color-body-text); margin-bottom: 10px;">🌐 External APIs</h4>
              <p style="margin-bottom: 10px; font-size: 14px;">Populate from external data sources:</p>
              <pre style="background: #f1f1f1; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;"><code>&lt;?php
$response = wp_remote_get('https://api.example.com/data');
$data = json_decode(wp_remote_retrieve_body($response), true);
foreach($data as $item) {
  echo "&lt;option value='{$item['id']}'&gt;{$item['name']}&lt;/option&gt;";
}
?&gt;</code></pre>
            </div>
          </div>

          <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: var(--border-radius);">
            <h4 style="color: var(--color-body-text); margin-bottom: 10px;">💡 Pro Tips:</h4>
            <ul style="margin: 0; padding-left: 20px; font-size: 14px;">
              <li>Use <code>wp_cache_set()</code> and <code>wp_cache_get()</code> for expensive queries</li>
              <li>Implement search/filter functionality with AJAX for large datasets</li>
              <li>Use <code>wp_localize_script()</code> to pass PHP data to JavaScript</li>
              <li>Consider pagination for very large option lists</li>
              <li>Always sanitize and validate user input</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- =================================================================
           LISTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Lists</h2>

        <script>
        console.log('Style guide JavaScript loading...');

        // Update range slider value display
        document.getElementById('range-input').addEventListener('input', function() {
          document.getElementById('range-value').textContent = this.value;
        });

        // Auto-growing textarea functionality
        function autoGrowTextarea(textarea) {
          textarea.style.height = 'auto';
          textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
        }

        const autoGrowTextareas = document.querySelectorAll('.auto-grow-textarea');
        autoGrowTextareas.forEach(textarea => {
          textarea.addEventListener('input', function() {
            autoGrowTextarea(this);

            // Update character count
            const charCount = this.value.length;
            const charCountElement = document.getElementById('char-count');
            const charMaxElement = document.getElementById('char-max');

            if (charCountElement) {
              charCountElement.textContent = charCount;
              const maxChars = parseInt(charMaxElement.textContent);

              // Change color based on character count
              if (charCount > maxChars * 0.9) {
                charCountElement.style.color = '#dc3545';
              } else if (charCount > maxChars * 0.7) {
                charCountElement.style.color = '#ffc107';
              } else {
                charCountElement.style.color = '#666';
              }
            }
          });

          // Initial resize
          autoGrowTextarea(textarea);
        });

        // Standard textarea character count
        const standardTextarea = document.getElementById('textarea-standard');
        if (standardTextarea) {
          standardTextarea.addEventListener('input', function() {
            const charCount = this.value.length;
            document.getElementById('standard-char-count').textContent = charCount;
          });
        }

        // Custom dropdown functionality
        const customDropdown = document.querySelector('.custom-dropdown');
        if (customDropdown) {
          const dropdownInput = customDropdown.querySelector('#dropdown-search');
          const dropdownOptions = customDropdown.querySelector('.dropdown-options');
          const searchInput = customDropdown.querySelector('.dropdown-search-input');
          const options = customDropdown.querySelectorAll('.dropdown-option');

          dropdownInput.addEventListener('click', function() {
            dropdownOptions.style.display = dropdownOptions.style.display === 'block' ? 'none' : 'block';
            if (dropdownOptions.style.display === 'block') {
              searchInput.focus();
            }
          });

          searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            options.forEach(option => {
              const text = option.textContent.toLowerCase();
              option.style.display = text.includes(searchTerm) ? 'block' : 'none';
            });
          });

          options.forEach(option => {
            option.addEventListener('click', function() {
              dropdownInput.value = this.textContent;
              dropdownOptions.style.display = 'none';
              searchInput.value = '';
              options.forEach(opt => opt.style.display = 'block');
            });
          });

          // Close dropdown when clicking outside
          document.addEventListener('click', function(e) {
            if (!customDropdown.contains(e.target)) {
              dropdownOptions.style.display = 'none';
            }
          });
        }

        // AJAX Multi-select dropdown functionality
        console.log('Initializing AJAX multi-select dropdown...');
        const ajaxMultiSelectDropdown = document.querySelector('.multi-select-dropdown-ajax');
        if (ajaxMultiSelectDropdown) {
          const display = ajaxMultiSelectDropdown.querySelector('.multi-select-display');
          const placeholder = ajaxMultiSelectDropdown.querySelector('.multi-select-placeholder');
          const options = ajaxMultiSelectDropdown.querySelector('.multi-select-options');
          const searchInput = ajaxMultiSelectDropdown.querySelector('.multi-select-search-input');
          const optionsContainer = ajaxMultiSelectDropdown.querySelector('.multi-select-options-container');
          const loadMoreIndicator = ajaxMultiSelectDropdown.querySelector('.load-more-indicator');

          let selectedItems = [];
          let loadedItems = [];
          let currentPage = 1;
          let isLoading = false;
          let searchTerm = '';

          // Load initial items
          loadItems();

          display.addEventListener('click', function() {
            options.style.display = options.style.display === 'block' ? 'none' : 'block';
            if (options.style.display === 'block') {
              searchInput.focus();
            }
          });

          // Search functionality with debounce
          let searchTimeout;
          searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
              searchTerm = this.value.toLowerCase();
              currentPage = 1;
              loadedItems = [];
              loadItems(true);
            }, 300);
          });

          // Infinite scroll
          options.addEventListener('scroll', function() {
            if (this.scrollTop + this.clientHeight >= this.scrollHeight - 5 && !isLoading) {
              currentPage++;
              loadItems();
            }
          });

          function loadItems(reset = false) {
            if (isLoading) return;
            isLoading = true;

            if (reset) {
              optionsContainer.innerHTML = '<div class="loading-indicator" style="padding: 20px; text-align: center; color: #666; font-size: 12px;"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
            } else {
              loadMoreIndicator.style.display = 'block';
            }

            // Simulate AJAX call to WordPress API
            setTimeout(() => {
              const mockTags = generateMockTags(searchTerm, currentPage);

              if (reset) {
                optionsContainer.innerHTML = '';
                loadedItems = [];
              }

              if (mockTags.length === 0 && currentPage === 1) {
                optionsContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #666; font-size: 12px;">No tags found</div>';
              } else {
                mockTags.forEach(tag => {
                  if (!loadedItems.find(item => item.value === tag.value)) {
                    loadedItems.push(tag);
                    const option = createOptionElement(tag);
                    optionsContainer.appendChild(option);
                  }
                });
              }

              loadMoreIndicator.style.display = 'none';
              isLoading = false;
            }, 500);
          }

          function generateMockTags(search, page) {
            const allTags = [
              'javascript', 'python', 'react', 'nodejs', 'php', 'mysql', 'css', 'html',
              'wordpress', 'vue', 'angular', 'typescript', 'mongodb', 'postgresql',
              'docker', 'kubernetes', 'aws', 'azure', 'git', 'github', 'api', 'rest',
              'graphql', 'json', 'xml', 'bootstrap', 'tailwind', 'sass', 'less',
              'webpack', 'babel', 'npm', 'yarn', 'composer', 'laravel', 'symfony',
              'django', 'flask', 'express', 'fastapi', 'spring', 'hibernate'
            ];

            let filteredTags = allTags;
            if (search) {
              filteredTags = allTags.filter(tag => tag.toLowerCase().includes(search));
            }

            const startIndex = (page - 1) * 10;
            const endIndex = startIndex + 10;

            return filteredTags.slice(startIndex, endIndex).map(tag => ({
              value: tag,
              label: tag.charAt(0).toUpperCase() + tag.slice(1),
              count: Math.floor(Math.random() * 50) + 1
            }));
          }

          function createOptionElement(tag) {
            const label = document.createElement('label');
            label.className = 'multi-select-option';
            label.style.cssText = 'display: flex; align-items: center; padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0;';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.value = tag.value;
            checkbox.style.marginRight = '8px';

            const span = document.createElement('span');
            span.textContent = `${tag.label} (${tag.count})`;

            label.appendChild(checkbox);
            label.appendChild(span);

            checkbox.addEventListener('change', function() {
              updateSelectedItems();
            });

            return label;
          }

          function updateSelectedItems() {
            selectedItems = [];
            const checkboxes = ajaxMultiSelectDropdown.querySelectorAll('input[type="checkbox"]:checked');
            checkboxes.forEach(checkbox => {
              const label = checkbox.nextElementSibling.textContent.split(' (')[0];
              selectedItems.push({
                value: checkbox.value,
                label: label
              });
            });
            updateDisplay();
          }

          function updateDisplay() {
            display.innerHTML = '';

            if (selectedItems.length === 0) {
              const placeholderSpan = document.createElement('span');
              placeholderSpan.className = 'multi-select-placeholder';
              placeholderSpan.style.cssText = 'color: #999; font-size: 14px;';
              placeholderSpan.textContent = 'Search tags...';
              display.appendChild(placeholderSpan);
            } else {
              selectedItems.forEach(item => {
                const tag = document.createElement('span');
                tag.style.cssText = 'background: var(--color-primary); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; display: flex; align-items: center; gap: 4px;';
                tag.innerHTML = `
                  ${item.label}
                  <button type="button" onclick="removeAjaxItem('${item.value}')" style="background: none; border: none; color: white; cursor: pointer; padding: 0; font-size: 12px; line-height: 1;">×</button>
                `;
                display.appendChild(tag);
              });
            }
          }

          // Global function to remove items
          window.removeAjaxItem = function(value) {
            const checkbox = ajaxMultiSelectDropdown.querySelector(`input[value="${value}"]`);
            if (checkbox) {
              checkbox.checked = false;
              updateSelectedItems();
            }
          };

          // Close dropdown when clicking outside
          document.addEventListener('click', function(e) {
            if (!ajaxMultiSelectDropdown.contains(e.target)) {
              options.style.display = 'none';
            }
          });
        }

        // Fixed AJAX Multi-select dropdown functionality
        console.log('Initializing fixed AJAX multi-select dropdown...');
        const ajaxMultiSelectDropdownFixed = document.querySelector('.multi-select-dropdown-ajax-fixed');
        if (ajaxMultiSelectDropdownFixed) {
          const display = ajaxMultiSelectDropdownFixed.querySelector('.multi-select-display');
          const options = ajaxMultiSelectDropdownFixed.querySelector('.multi-select-options');
          const searchInput = ajaxMultiSelectDropdownFixed.querySelector('.multi-select-search-input');
          const optionsContainer = ajaxMultiSelectDropdownFixed.querySelector('.multi-select-options-container');

          let selectedItems = [];
          let loadedItems = [];
          let currentPage = 1;
          let isLoading = false;
          let searchTerm = '';

          // Load initial items
          loadItems();

          display.addEventListener('click', function() {
            options.style.display = options.style.display === 'block' ? 'none' : 'block';
            if (options.style.display === 'block') {
              searchInput.focus();
            }
          });

          // Search functionality with debounce
          let searchTimeout;
          searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
              searchTerm = this.value.toLowerCase();
              currentPage = 1;
              loadedItems = [];
              loadItems(true);
            }, 300);
          });

          // Infinite scroll
          options.addEventListener('scroll', function() {
            if (this.scrollTop + this.clientHeight >= this.scrollHeight - 5 && !isLoading) {
              currentPage++;
              loadItems();
            }
          });

          function loadItems(reset = false) {
            if (isLoading) return;
            isLoading = true;

            if (reset) {
              optionsContainer.innerHTML = '<div class="loading-indicator" style="padding: 15px; text-align: center; color: #666; font-size: 11px;"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
            }

            // Simulate AJAX call to WordPress API
            setTimeout(() => {
              const mockTags = generateMockTagsFixed(searchTerm, currentPage);

              if (reset) {
                optionsContainer.innerHTML = '';
                loadedItems = [];
              }

              if (mockTags.length === 0 && currentPage === 1) {
                optionsContainer.innerHTML = '<div style="padding: 15px; text-align: center; color: #666; font-size: 11px;">No tags found</div>';
              } else {
                mockTags.forEach(tag => {
                  if (!loadedItems.find(item => item.value === tag.value)) {
                    loadedItems.push(tag);
                    const option = createOptionElementFixed(tag);
                    optionsContainer.appendChild(option);
                  }
                });
              }

              isLoading = false;
            }, 300);
          }

          function generateMockTagsFixed(search, page) {
            const allTags = [
              'javascript', 'python', 'react', 'nodejs', 'php', 'mysql', 'css', 'html',
              'wordpress', 'vue', 'angular', 'typescript', 'mongodb', 'postgresql',
              'docker', 'kubernetes', 'aws', 'azure', 'git', 'github', 'api', 'rest',
              'graphql', 'json', 'xml', 'bootstrap', 'tailwind', 'sass', 'less',
              'webpack', 'babel', 'npm', 'yarn', 'composer', 'laravel', 'symfony',
              'django', 'flask', 'express', 'fastapi', 'spring', 'hibernate'
            ];

            let filteredTags = allTags;
            if (search) {
              filteredTags = allTags.filter(tag => tag.toLowerCase().includes(search));
            }

            const startIndex = (page - 1) * 10;
            const endIndex = startIndex + 10;

            return filteredTags.slice(startIndex, endIndex).map(tag => ({
              value: tag,
              label: tag.charAt(0).toUpperCase() + tag.slice(1),
              count: Math.floor(Math.random() * 50) + 1
            }));
          }

          function createOptionElementFixed(tag) {
            const label = document.createElement('label');
            label.className = 'multi-select-option';
            label.style.cssText = 'display: flex; align-items: center; padding: 6px 8px; cursor: pointer; border-bottom: 1px solid #f0f0f0; font-size: 11px;';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.value = tag.value;
            checkbox.style.cssText = 'margin-right: 6px; transform: scale(0.8);';

            const span = document.createElement('span');
            span.textContent = `${tag.label} (${tag.count})`;

            label.appendChild(checkbox);
            label.appendChild(span);

            checkbox.addEventListener('change', function() {
              updateSelectedItemsFixed();
            });

            return label;
          }

          function updateSelectedItemsFixed() {
            selectedItems = [];
            const checkboxes = ajaxMultiSelectDropdownFixed.querySelectorAll('input[type="checkbox"]:checked');
            checkboxes.forEach(checkbox => {
              const label = checkbox.nextElementSibling.textContent.split(' (')[0];
              selectedItems.push({
                value: checkbox.value,
                label: label
              });
            });
            updateDisplayFixed();
          }

          function updateDisplayFixed() {
            display.innerHTML = '';

            if (selectedItems.length === 0) {
              const placeholderSpan = document.createElement('span');
              placeholderSpan.className = 'multi-select-placeholder';
              placeholderSpan.style.cssText = 'color: #999; font-size: 12px;';
              placeholderSpan.textContent = 'Search tags...';
              display.appendChild(placeholderSpan);
            } else {
              selectedItems.forEach(item => {
                const tag = document.createElement('span');
                tag.style.cssText = 'background: var(--color-primary); color: white; padding: 1px 6px; border-radius: 8px; font-size: 10px; display: flex; align-items: center; gap: 3px;';
                tag.innerHTML = `
                  ${item.label}
                  <button type="button" onclick="removeAjaxItemFixed('${item.value}')" style="background: none; border: none; color: white; cursor: pointer; padding: 0; font-size: 10px; line-height: 1;">×</button>
                `;
                display.appendChild(tag);
              });
            }
          }

          // Global function to remove items
          window.removeAjaxItemFixed = function(value) {
            const checkbox = ajaxMultiSelectDropdownFixed.querySelector(`input[value="${value}"]`);
            if (checkbox) {
              checkbox.checked = false;
              updateSelectedItemsFixed();
            }
          };

          // Close dropdown when clicking outside
          document.addEventListener('click', function(e) {
            if (!ajaxMultiSelectDropdownFixed.contains(e.target)) {
              options.style.display = 'none';
            }
          });
        }

        // Multi-select dropdown functionality
        console.log('Initializing multi-select dropdown...');
        const multiSelectDropdown = document.querySelector('.multi-select-dropdown');
        if (multiSelectDropdown) {
          console.log('Multi-select dropdown found');
          const display = multiSelectDropdown.querySelector('.multi-select-display');
          const placeholder = multiSelectDropdown.querySelector('.multi-select-placeholder');
          const options = multiSelectDropdown.querySelector('.multi-select-options');
          const searchInput = multiSelectDropdown.querySelector('.multi-select-search-input');
          const checkboxes = multiSelectDropdown.querySelectorAll('input[type="checkbox"]');
          const optionLabels = multiSelectDropdown.querySelectorAll('.multi-select-option');

          let selectedItems = [];

          display.addEventListener('click', function() {
            options.style.display = options.style.display === 'block' ? 'none' : 'block';
            if (options.style.display === 'block') {
              searchInput.focus();
            }
          });

          searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            optionLabels.forEach(label => {
              const text = label.textContent.toLowerCase();
              label.style.display = text.includes(searchTerm) ? 'flex' : 'none';
            });
          });

          checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
              updateSelectedItems();
            });
          });

          function updateSelectedItems() {
            selectedItems = [];
            checkboxes.forEach(checkbox => {
              if (checkbox.checked) {
                const label = checkbox.parentElement.querySelector('span').textContent;
                selectedItems.push({
                  value: checkbox.value,
                  label: label
                });
              }
            });

            updateDisplay();
          }

          function updateDisplay() {
            // Clear current display
            display.innerHTML = '';

            if (selectedItems.length === 0) {
              const placeholderSpan = document.createElement('span');
              placeholderSpan.className = 'multi-select-placeholder';
              placeholderSpan.style.cssText = 'color: #999; font-size: 14px;';
              placeholderSpan.textContent = 'Select skills...';
              display.appendChild(placeholderSpan);
            } else {
              selectedItems.forEach(item => {
                const tag = document.createElement('span');
                tag.style.cssText = 'background: var(--color-primary); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; display: flex; align-items: center; gap: 4px;';
                tag.innerHTML = `
                  ${item.label}
                  <button type="button" onclick="removeItem('${item.value}')" style="background: none; border: none; color: white; cursor: pointer; padding: 0; font-size: 12px; line-height: 1;">×</button>
                `;
                display.appendChild(tag);
              });
            }
          }

          // Global function to remove items
          window.removeItem = function(value) {
            const checkbox = multiSelectDropdown.querySelector(`input[value="${value}"]`);
            if (checkbox) {
              checkbox.checked = false;
              updateSelectedItems();
            }
          };

          // Close dropdown when clicking outside
          document.addEventListener('click', function(e) {
            if (!multiSelectDropdown.contains(e.target)) {
              options.style.display = 'none';
            }
          });

          // Initialize display
          updateDisplay();
        }

        // Radio group functionality
        const radioGroups = document.querySelectorAll('.radio-group, .radio-group-compact, .radio-group-buttons');
        radioGroups.forEach(group => {
          const radioInputs = group.querySelectorAll('input[type="radio"]');
          const radioLabels = group.querySelectorAll('label');

          radioLabels.forEach(label => {
            label.addEventListener('click', function() {
              const input = this.querySelector('input[type="radio"]');
              if (input) {
                // Remove selected class from all labels in this group
                radioLabels.forEach(l => {
                  l.classList.remove('radio-option-selected', 'radio-compact-selected', 'radio-button-selected');
                  if (group.classList.contains('radio-group')) {
                    l.style.borderColor = '#e0e0e0';
                    l.style.background = 'white';
                  } else if (group.classList.contains('radio-group-compact')) {
                    l.style.borderColor = '#ddd';
                    l.style.background = 'white';
                    l.style.color = 'inherit';
                  } else if (group.classList.contains('radio-group-buttons')) {
                    l.style.background = 'white';
                    l.style.color = 'inherit';
                  }
                });

                // Add selected class to clicked label
                if (group.classList.contains('radio-group')) {
                  this.classList.add('radio-option-selected');
                  this.style.borderColor = 'var(--color-secondary)';
                  this.style.background = '#f8f9ff';
                } else if (group.classList.contains('radio-group-compact')) {
                  this.classList.add('radio-compact-selected');
                  this.style.borderColor = 'var(--color-secondary)';
                  this.style.background = 'var(--color-secondary)';
                  this.style.color = 'white';
                } else if (group.classList.contains('radio-group-buttons')) {
                  this.classList.add('radio-button-selected');
                  this.style.background = 'var(--color-secondary)';
                  this.style.color = 'white';
                }
              }
            });
          });
        });

        // File drop area functionality
        const fileDropArea = document.querySelector('.file-drop-area');
        if (fileDropArea) {
          const fileInput = fileDropArea.querySelector('.file-input-hidden');
          const fileList = fileDropArea.querySelector('.file-list');
          const fileItems = fileDropArea.querySelector('.file-items');

          fileDropArea.addEventListener('click', () => fileInput.click());

          fileDropArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = 'var(--color-primary)';
            this.style.backgroundColor = '#f0f8ff';
          });

          fileDropArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '#ddd';
            this.style.backgroundColor = '#fafafa';
          });

          fileDropArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#ddd';
            this.style.backgroundColor = '#fafafa';

            const files = e.dataTransfer.files;
            handleFiles(files);
          });

          fileInput.addEventListener('change', function() {
            handleFiles(this.files);
          });

          function handleFiles(files) {
            if (files.length > 0) {
              fileList.style.display = 'block';
              fileItems.innerHTML = '';

              Array.from(files).forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.style.cssText = 'display: flex; align-items: center; gap: 8px; padding: 6px 0; border-bottom: 1px solid #eee;';
                fileItem.innerHTML = `
                  <i class="fas fa-file" style="color: var(--color-secondary);"></i>
                  <span style="flex: 1; font-size: 13px;">${file.name}</span>
                  <span style="font-size: 11px; color: #999;">${(file.size / 1024).toFixed(1)} KB</span>
                `;
                fileItems.appendChild(fileItem);
              });
            }
          }
        }

        // Advanced range slider functionality
        const advancedRange = document.getElementById('advanced-range');
        if (advancedRange) {
          const track = document.querySelector('.advanced-range-track');
          const fill = document.querySelector('.advanced-range-fill');
          const thumb = document.querySelector('.advanced-range-thumb');
          const valueDisplay = document.getElementById('advanced-range-value');

          function updateSlider(value) {
            const percentage = (value / 100) * 100;
            fill.style.width = percentage + '%';
            thumb.style.left = percentage + '%';
            valueDisplay.textContent = value;
          }

          track.addEventListener('click', function(e) {
            const rect = track.getBoundingClientRect();
            const percentage = (e.clientX - rect.left) / rect.width;
            const value = Math.round(percentage * 100);
            advancedRange.value = value;
            updateSlider(value);
          });

          let isDragging = false;
          thumb.addEventListener('mousedown', function(e) {
            isDragging = true;
            thumb.style.cursor = 'grabbing';
          });

          document.addEventListener('mousemove', function(e) {
            if (isDragging) {
              const rect = track.getBoundingClientRect();
              const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
              const value = Math.round(percentage * 100);
              advancedRange.value = value;
              updateSlider(value);
            }
          });

          document.addEventListener('mouseup', function() {
            if (isDragging) {
              isDragging = false;
              thumb.style.cursor = 'grab';
            }
          });
        }

        // Copy code functionality
        document.querySelectorAll('.copy-code-btn').forEach(button => {
          button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const codeElement = document.getElementById(targetId);

            if (codeElement) {
              const text = codeElement.textContent;

              // Use the Clipboard API if available
              if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                  showCopyFeedback(this);
                }).catch(() => {
                  fallbackCopyText(text);
                  showCopyFeedback(this);
                });
              } else {
                fallbackCopyText(text);
                showCopyFeedback(this);
              }
            }
          });
        });

        function fallbackCopyText(text) {
          const textArea = document.createElement('textarea');
          textArea.value = text;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();

          try {
            document.execCommand('copy');
          } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
          }

          document.body.removeChild(textArea);
        }

        function showCopyFeedback(button) {
          const originalText = button.innerHTML;
          button.innerHTML = '<i class="fas fa-check"></i> Copied!';
          button.style.background = '#28a745';

          setTimeout(() => {
            button.innerHTML = originalText;
            button.style.background = '';
          }, 2000);
        }

        // Comprehensive functionality test
        function runFunctionalityTests() {
          const results = [];

          // Test 1: Floating Label
          const floatingInput = document.getElementById('floating-input');
          const floatingLabel = document.querySelector('.floating-label');
          results.push(`✅ Floating Input: ${floatingInput ? 'Found' : 'Missing'}`);
          results.push(`✅ Floating Label: ${floatingLabel ? 'Found' : 'Missing'}`);

          // Test 2: Multi-select Dropdown
          const multiSelect = document.querySelector('.multi-select-dropdown');
          results.push(`✅ Multi-select Dropdown: ${multiSelect ? 'Found' : 'Missing'}`);

          // Test 3: Auto-growing Textarea
          const autoGrowTextarea = document.querySelector('.auto-grow-textarea');
          results.push(`✅ Auto-grow Textarea: ${autoGrowTextarea ? 'Found' : 'Missing'}`);

          // Test 4: Custom Dropdowns
          const customDropdown = document.querySelector('.custom-dropdown');
          results.push(`✅ Custom Dropdown: ${customDropdown ? 'Found' : 'Missing'}`);

          // Test 5: File Drop Area
          const fileDropArea = document.querySelector('.file-drop-area');
          results.push(`✅ File Drop Area: ${fileDropArea ? 'Found' : 'Missing'}`);

          // Test 6: Advanced Range Slider
          const advancedRange = document.getElementById('advanced-range');
          results.push(`✅ Advanced Range: ${advancedRange ? 'Found' : 'Missing'}`);

          // Test 7: Copy Buttons
          const copyButtons = document.querySelectorAll('.copy-code-btn');
          results.push(`✅ Copy Buttons: ${copyButtons.length} found`);

          // Test 8: Tab Navigation
          const tabButtons = document.querySelectorAll('.tab-button');
          results.push(`✅ Tab Buttons: ${tabButtons.length} found`);

          // Test 9: Custom Checkboxes/Radios
          const customCheckboxes = document.querySelectorAll('.custom-checkbox');
          const customRadios = document.querySelectorAll('.custom-radio');
          results.push(`✅ Custom Checkboxes: ${customCheckboxes.length} found`);
          results.push(`✅ Custom Radios: ${customRadios.length} found`);

          // Display results
          const testResultsDiv = document.getElementById('test-results');
          if (testResultsDiv) {
            testResultsDiv.innerHTML = results.join('<br>');
          }

          console.log('Functionality Test Results:', results);
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
          setTimeout(runFunctionalityTests, 1000);
        });

        // Also run tests immediately if DOM is already loaded
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', runFunctionalityTests);
        } else {
          setTimeout(runFunctionalityTests, 100);
        }
        </script>
      </section>

      <!-- =================================================================
           LISTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Lists</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px;">
          <div>
            <h3 style="color: var(--color-body-text);">Unordered List</h3>
            <ul style="list-style: none; padding-left: 0;">
              <li style="position: relative; padding-left: 20px; margin-bottom: 8px; font-size: 16px; line-height: 1.75;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75;">•</span>First item</li>
              <li style="position: relative; padding-left: 20px; margin-bottom: 8px; font-size: 16px; line-height: 1.75;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75;">•</span>Second item with <a href="#" style="color: #000000; font-weight: 700; text-decoration: none;" onmouseover="this.style.color='#333333'; this.style.textDecoration='underline';" onmouseout="this.style.color='#000000'; this.style.textDecoration='none';">a link</a></li>
              <li style="position: relative; padding-left: 20px; margin-bottom: 8px; font-size: 16px; line-height: 1.75;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75;">•</span>Third item</li>
              <li style="position: relative; padding-left: 20px; margin-bottom: 8px; font-size: 16px; line-height: 1.75;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75;">•</span>Fourth item with <strong>bold text</strong></li>
            </ul>
          </div>

          <div>
            <h3 style="color: var(--color-body-text);">Ordered List</h3>
            <ol style="list-style: none; padding-left: 0; counter-reset: list-counter;">
              <li style="position: relative; padding-left: 25px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: list-counter;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 20px;">1.</span>First numbered item</li>
              <li style="position: relative; padding-left: 25px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: list-counter;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 20px;">2.</span>Second numbered item</li>
              <li style="position: relative; padding-left: 25px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: list-counter;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 20px;">3.</span>Third numbered item</li>
              <li style="position: relative; padding-left: 25px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: list-counter;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 20px;">4.</span>Fourth numbered item</li>
            </ol>
          </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 30px;">
          <div>
            <h3 style="color: var(--color-body-text);">Reverse Ordered List</h3>
            <ol style="list-style: none; padding-left: 0; counter-reset: reverse-counter 5;">
              <li style="position: relative; padding-left: 25px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: reverse-counter -1;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 20px;">4.</span>Fourth item (displays as 4)</li>
              <li style="position: relative; padding-left: 25px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: reverse-counter -1;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 20px;">3.</span>Third item (displays as 3)</li>
              <li style="position: relative; padding-left: 25px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: reverse-counter -1;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 20px;">2.</span>Second item (displays as 2)</li>
              <li style="position: relative; padding-left: 25px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: reverse-counter -1;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 20px;">1.</span>First item (displays as 1)</li>
            </ol>
          </div>

          <div>
            <h3 style="color: var(--color-body-text);">Custom Start Ordered List</h3>
            <ol style="list-style: none; padding-left: 0; counter-reset: custom-counter 9;">
              <li style="position: relative; padding-left: 30px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: custom-counter;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 25px;">10.</span>Tenth item</li>
              <li style="position: relative; padding-left: 30px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: custom-counter;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 25px;">11.</span>Eleventh item</li>
              <li style="position: relative; padding-left: 30px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: custom-counter;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 25px;">12.</span>Twelfth item</li>
              <li style="position: relative; padding-left: 30px; margin-bottom: 8px; font-size: 16px; line-height: 1.75; counter-increment: custom-counter;"><span style="color: #4a4a4a; font-weight: 900; font-size: 16px; position: absolute; left: 0; top: 0; line-height: 1.75; min-width: 25px;">13.</span>Thirteenth item</li>
            </ol>
          </div>
        </div>

        <!-- =================================================================
             TIMELINE COMPONENTS
             ================================================================= -->
        <h2 style="color: var(--color-body-text); margin-top: 60px;">Timeline Components</h2>

        <h3 style="color: var(--color-body-text);">Timeline Block</h3>
        <div class="baum-timeline-container" style="max-width: 600px;">
          <div class="baum-timeline-posts">
            <div style="border-left: 3px solid var(--color-secondary); padding-left: 17px; margin-left: 10px;">

              <div style="position: relative; margin-bottom: 30px;">
                <div style="position: absolute; left: -26px; top: 5px; width: 16px; height: 16px; background: var(--color-primary); border-radius: 50%;"></div>
                <div style="background: white; border: 1px solid #ddd; border-radius: var(--border-radius); padding: 15px;">
                  <h4 style="margin: 0 0 5px 0;">Timeline Event 1</h4>
                  <p style="margin: 0; color: #666; font-size: 14px;">This is the first event in our timeline.</p>
                  <small style="color: #999;">2 hours ago</small>
                </div>
              </div>

              <div style="position: relative; margin-bottom: 30px;">
                <div style="position: absolute; left: -26px; top: 5px; width: 16px; height: 16px; background: var(--color-secondary); border-radius: 50%;"></div>
                <div style="background: white; border: 1px solid #ddd; border-radius: var(--border-radius); padding: 15px;">
                  <h4 style="margin: 0 0 5px 0;">Timeline Event 2</h4>
                  <p style="margin: 0; color: #666; font-size: 14px;">This is the second event in our timeline.</p>
                  <small style="color: #999;">1 day ago</small>
                </div>
              </div>

              <div style="position: relative; margin-bottom: 30px;">
                <div style="position: absolute; left: -26px; top: 5px; width: 16px; height: 16px; background: var(--color-accent); border-radius: 50%;"></div>
                <div style="background: white; border: 1px solid #ddd; border-radius: var(--border-radius); padding: 15px;">
                  <h4 style="margin: 0 0 5px 0;">Timeline Event 3</h4>
                  <p style="margin: 0; color: #666; font-size: 14px;">This is the third event in our timeline.</p>
                  <small style="color: #999;">3 days ago</small>
                </div>
              </div>

            </div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Numbered Step Timeline</h3>
        <div style="margin: 20px 0; background: #1a1a1a; color: #e0e0e0; padding: 40px; border-radius: var(--border-radius); max-width: 800px;">

          <!-- Timeline Item 1 -->
          <div style="display: flex; gap: 20px; margin-bottom: 40px; align-items: flex-start;">
            <!-- Number Circle -->
            <div style="width: 32px; height: 32px; background: white; color: #1a1a1a; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px;">
              2
            </div>

            <!-- Content -->
            <div style="flex: 1;">
              <h3 style="color: white; font-size: 24px; font-weight: 600; margin: 0 0 15px; line-height: 1.3;">Create a Clerk application</h3>
              <p style="color: #b0b0b0; font-size: 16px; line-height: 1.6; margin: 0;">
                Create a new Clerk application through the Clerk Dashboard. You can follow the
                <a href="#" style="color: #60a5fa; text-decoration: underline;">setup guide</a> to help you get started.
              </p>
            </div>
          </div>

          <!-- Timeline Item 2 -->
          <div style="display: flex; gap: 20px; margin-bottom: 40px; align-items: flex-start;">
            <!-- Number Circle -->
            <div style="width: 32px; height: 32px; background: white; color: #1a1a1a; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px;">
              3
            </div>

            <!-- Content -->
            <div style="flex: 1;">
              <h3 style="color: white; font-size: 24px; font-weight: 600; margin: 0 0 15px; line-height: 1.3;">Set the environment variables</h3>
              <p style="color: #b0b0b0; font-size: 16px; line-height: 1.6; margin: 0;">
                Set the environment variables in your Clerk application. You can find instructions on how to do so in the appropriate
                <a href="#" style="color: #60a5fa; text-decoration: underline;">quickstart guide</a>.
              </p>
            </div>
          </div>

          <!-- Timeline Item 3 -->
          <div style="display: flex; gap: 20px; margin-bottom: 40px; align-items: flex-start;">
            <!-- Number Circle -->
            <div style="width: 32px; height: 32px; background: white; color: #1a1a1a; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px;">
              4
            </div>

            <!-- Content -->
            <div style="flex: 1;">
              <h3 style="color: white; font-size: 24px; font-weight: 600; margin: 0 0 15px; line-height: 1.3;">Run the app</h3>
              <p style="color: #b0b0b0; font-size: 16px; line-height: 1.6; margin: 0 0 15px;">
                Run the application locally and verify that it's functional. For example, if you used
                <code style="background: #2d2d2d; color: #f8f8f2; padding: 2px 6px; border-radius: 4px; font-family: monospace;">npm</code>
                to install the dependencies, you can run the app's development instance with the command
                <code style="background: #2d2d2d; color: #f8f8f2; padding: 2px 6px; border-radius: 4px; font-family: monospace;">npm run dev</code>.
              </p>
              <p style="color: #b0b0b0; font-size: 16px; line-height: 1.6; margin: 0;">
                The app should then be running on
                <code style="background: #2d2d2d; color: #f8f8f2; padding: 2px 6px; border-radius: 4px; font-family: monospace;">http://localhost:3000</code>
                with no errors.
              </p>
            </div>
          </div>

          <!-- Timeline Item 4 -->
          <div style="display: flex; gap: 20px; margin-bottom: 0; align-items: flex-start;">
            <!-- Number Circle -->
            <div style="width: 32px; height: 32px; background: white; color: #1a1a1a; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px;">
              5
            </div>

            <!-- Content -->
            <div style="flex: 1;">
              <h3 style="color: white; font-size: 24px; font-weight: 600; margin: 0 0 15px; line-height: 1.3;">Add code to reproduce the issue</h3>
              <p style="color: #b0b0b0; font-size: 16px; line-height: 1.6; margin: 0;">
                Adjust your template project by adding code gradually until you get to the point where you are running into the same error.
              </p>
            </div>
          </div>

        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Light Theme Variation</h3>
        <div style="margin: 20px 0; background: white; color: #333; padding: 40px; border-radius: var(--border-radius); border: 1px solid #e0e0e0; max-width: 800px;">

          <!-- Light Timeline Item 1 -->
          <div style="display: flex; gap: 20px; margin-bottom: 40px; align-items: flex-start;">
            <!-- Number Circle -->
            <div style="width: 32px; height: 32px; background: var(--color-secondary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px;">
              1
            </div>

            <!-- Content -->
            <div style="flex: 1;">
              <h3 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin: 0 0 15px; line-height: 1.3;">Install dependencies</h3>
              <p style="color: #6b7280; font-size: 16px; line-height: 1.6; margin: 0 0 15px;">
                Install the required dependencies for your project. You can use your preferred package manager:
              </p>
              <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; font-family: monospace; font-size: 14px; color: #495057;">
                npm install<br>
                # or<br>
                yarn install<br>
                # or<br>
                pnpm install
              </div>
            </div>
          </div>

          <!-- Light Timeline Item 2 -->
          <div style="display: flex; gap: 20px; margin-bottom: 40px; align-items: flex-start;">
            <!-- Number Circle -->
            <div style="width: 32px; height: 32px; background: var(--color-secondary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px;">
              2
            </div>

            <!-- Content -->
            <div style="flex: 1;">
              <h3 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin: 0 0 15px; line-height: 1.3;">Configure environment</h3>
              <p style="color: #6b7280; font-size: 16px; line-height: 1.6; margin: 0;">
                Set up your environment variables in a
                <code style="background: #f1f3f4; color: #d73a49; padding: 2px 6px; border-radius: 4px; font-family: monospace;">.env.local</code>
                file. You can find the required variables in the
                <a href="#" style="color: var(--color-secondary); text-decoration: underline;">documentation</a>.
              </p>
            </div>
          </div>

          <!-- Light Timeline Item 3 -->
          <div style="display: flex; gap: 20px; margin-bottom: 0; align-items: flex-start;">
            <!-- Number Circle -->
            <div style="width: 32px; height: 32px; background: var(--color-secondary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px;">
              3
            </div>

            <!-- Content -->
            <div style="flex: 1;">
              <h3 style="color: var(--color-body-text); font-size: 24px; font-weight: 600; margin: 0 0 15px; line-height: 1.3;">Start development server</h3>
              <p style="color: #6b7280; font-size: 16px; line-height: 1.6; margin: 0 0 15px;">
                Run the development server and open your browser to see the application:
              </p>
              <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; font-family: monospace; font-size: 14px; color: #495057; margin-bottom: 15px;">
                npm run dev
              </div>
              <p style="color: #6b7280; font-size: 16px; line-height: 1.6; margin: 0;">
                Open <a href="#" style="color: var(--color-secondary); text-decoration: underline;">http://localhost:3000</a> in your browser to view the application.
              </p>
            </div>
          </div>

        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Compact Timeline</h3>
        <div style="margin: 20px 0; background: #f8f9fa; padding: 30px; border-radius: var(--border-radius); border: 1px solid #e0e0e0; max-width: 600px;">

          <!-- Compact Timeline Item 1 -->
          <div style="display: flex; gap: 15px; margin-bottom: 25px; align-items: flex-start;">
            <div style="width: 24px; height: 24px; background: var(--color-secondary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; flex-shrink: 0; margin-top: 2px;">
              1
            </div>
            <div style="flex: 1;">
              <h4 style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin: 0 0 5px;">Clone repository</h4>
              <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin: 0;">Clone the project repository to your local machine.</p>
            </div>
          </div>

          <!-- Compact Timeline Item 2 -->
          <div style="display: flex; gap: 15px; margin-bottom: 25px; align-items: flex-start;">
            <div style="width: 24px; height: 24px; background: var(--color-secondary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; flex-shrink: 0; margin-top: 2px;">
              2
            </div>
            <div style="flex: 1;">
              <h4 style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin: 0 0 5px;">Install packages</h4>
              <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin: 0;">Run <code style="background: #e9ecef; padding: 1px 4px; border-radius: 3px; font-size: 12px;">npm install</code> to install dependencies.</p>
            </div>
          </div>

          <!-- Compact Timeline Item 3 -->
          <div style="display: flex; gap: 15px; margin-bottom: 25px; align-items: flex-start;">
            <div style="width: 24px; height: 24px; background: var(--color-secondary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; flex-shrink: 0; margin-top: 2px;">
              3
            </div>
            <div style="flex: 1;">
              <h4 style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin: 0 0 5px;">Configure settings</h4>
              <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin: 0;">Update configuration files with your specific settings.</p>
            </div>
          </div>

          <!-- Compact Timeline Item 4 -->
          <div style="display: flex; gap: 15px; margin-bottom: 0; align-items: flex-start;">
            <div style="width: 24px; height: 24px; background: var(--color-secondary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 12px; flex-shrink: 0; margin-top: 2px;">
              4
            </div>
            <div style="flex: 1;">
              <h4 style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin: 0 0 5px;">Launch application</h4>
              <p style="color: #6b7280; font-size: 14px; line-height: 1.5; margin: 0;">Start the development server and test the application.</p>
            </div>
          </div>

        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Connected Timeline with Lines</h3>
        <div style="margin: 20px 0; background: white; padding: 40px; border-radius: var(--border-radius); border: 1px solid #e0e0e0; max-width: 800px; position: relative;">

          <!-- Timeline Line -->
          <div style="position: absolute; left: 56px; top: 80px; bottom: 40px; width: 2px; background: #e0e0e0;"></div>

          <!-- Connected Timeline Item 1 -->
          <div style="display: flex; gap: 20px; margin-bottom: 40px; align-items: flex-start; position: relative;">
            <div style="width: 32px; height: 32px; background: var(--color-green); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px; position: relative; z-index: 1;">
              ✓
            </div>
            <div style="flex: 1;">
              <h3 style="color: var(--color-body-text); font-size: 20px; font-weight: 600; margin: 0 0 10px;">Project initialized</h3>
              <p style="color: #6b7280; font-size: 16px; line-height: 1.6; margin: 0;">Successfully created new project structure and configuration files.</p>
              <span style="color: #9ca3af; font-size: 12px;">Completed 2 hours ago</span>
            </div>
          </div>

          <!-- Connected Timeline Item 2 -->
          <div style="display: flex; gap: 20px; margin-bottom: 40px; align-items: flex-start; position: relative;">
            <div style="width: 32px; height: 32px; background: var(--color-secondary); color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px; position: relative; z-index: 1;">
              2
            </div>
            <div style="flex: 1;">
              <h3 style="color: var(--color-body-text); font-size: 20px; font-weight: 600; margin: 0 0 10px;">Dependencies installed</h3>
              <p style="color: #6b7280; font-size: 16px; line-height: 1.6; margin: 0;">All required packages and dependencies have been installed successfully.</p>
              <span style="color: #9ca3af; font-size: 12px;">In progress</span>
            </div>
          </div>

          <!-- Connected Timeline Item 3 -->
          <div style="display: flex; gap: 20px; margin-bottom: 0; align-items: flex-start; position: relative;">
            <div style="width: 32px; height: 32px; background: #e0e0e0; color: #9ca3af; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 700; font-size: 16px; flex-shrink: 0; margin-top: 4px; position: relative; z-index: 1;">
              3
            </div>
            <div style="flex: 1;">
              <h3 style="color: #9ca3af; font-size: 20px; font-weight: 600; margin: 0 0 10px;">Deploy to production</h3>
              <p style="color: #9ca3af; font-size: 16px; line-height: 1.6; margin: 0;">Final deployment step will be completed after testing.</p>
              <span style="color: #9ca3af; font-size: 12px;">Pending</span>
            </div>
          </div>

        </div>
      </section>



      <!-- =================================================================
           WORDPRESS COMPONENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2>WordPress Components</h2>

        <h3>WordPress Calendar</h3>
        <div style="max-width: 300px;">
          <?php get_calendar(); ?>
        </div>

        <h3>Small Calendar (Date.php Style)</h3>
        <div style="max-width: 250px; background: var(--color-white); border: 1px solid var(--color-quaternary); border-radius: var(--border-radius); padding: 0px;">
          <div style="text-align: center; padding-bottom: 5px; background: var(--color-secondary); border-radius: var(--border-radius-small) var(--border-radius-small) 0 0; ">
            <strong style="color: var(--color-primary);"><?php echo date('F Y'); ?></strong>
          </div>
          <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 0px; text-align: center; font-size: 11px; color: var(--color-body-text);">
            <div style="font-weight: bold; background: var(--color-secondary); color: white; ">S</div>
            <div style="font-weight: bold; background: var(--color-secondary); color: white; ">M</div>
            <div style="font-weight: bold; background: var(--color-secondary); color: white; ">T</div>
            <div style="font-weight: bold; background: var(--color-secondary); color: white; ">W</div>
            <div style="font-weight: bold; background: var(--color-secondary); color: white; ">T</div>
            <div style="font-weight: bold; background: var(--color-secondary); color: white; ">F</div>
            <div style="font-weight: bold; background: var(--color-secondary); color: white; ">S</div>
            <?php
            $today = date('j');
            $days_in_month = date('t');
            $first_day = date('w', mktime(0, 0, 0, date('m'), 1, date('Y')));

            // Empty cells for days before month starts
            for ($i = 0; $i < $first_day; $i++) {
              echo '<div></div>';
            }

            // Days of the month
            for ($day = 1; $day <= $days_in_month; $day++) {
              $is_today = ($day == $today) ? 'background: var(--color-octonary); color: var(--color-black); border-radius: 5px; font-weight: 900; ' : '';
              echo '<div style="padding: 3px; ' . $is_today . '">' . $day . '</div>';
            }
            ?>
          </div>
        </div>

        <h3>Tag Cloud</h3>
        <div style="line-height: 1.2; font-weight: bold;">
          <?php wp_tag_cloud([
            'smallest' => 12,
            'largest' => 24,
            'number' => 20,
            'format' => 'flat',
            'separator' => ' ',
            'orderby' => 'count',
            'order' => 'DESC'
          ]); ?>
        </div>

        <h3>Recent Posts Widget</h3>
        <div class="widget widget_recent_entries">
          <?php
          $recent_posts = wp_get_recent_posts(['numberposts' => 5]);
          if ($recent_posts) {
            echo '<ul>';
            foreach ($recent_posts as $post) {
              echo '<li><a href="' . get_permalink($post['ID']) . '">' . $post['post_title'] . '</a></li>';
            }
            echo '</ul>';
          }
          ?>
        </div>

        <h3>Categories Widget (Badge Style)</h3>
        <div class="widget widget_categories" style="display: flex; flex-wrap: wrap; gap: 8px;">
          <?php
          $categories = get_categories(['hide_empty' => false, 'number' => 10]);
          foreach ($categories as $category) {
            $count = $category->count;
            echo '<a href="' . get_category_link($category->term_id) . '" class="baum-badge" style="text-decoration: none; background: var(--color-secondary); color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">';
            echo esc_html($category->name);
            if ($count > 0) {
              echo ' <span style="background: rgba(255,255,255,0.3); padding: 1px 4px; border-radius: 8px; margin-left: 4px;">' . $count . '</span>';
            }
            echo '</a>';
          }
          ?>
        </div>

        <h3>Tags Widget (Badge Style)</h3>
        <div class="widget widget_tag_cloud" style="display: flex; flex-wrap: wrap; gap: 8px;">
          <?php
          $tags = get_tags(['hide_empty' => false, 'number' => 15, 'orderby' => 'count', 'order' => 'DESC']);
          $colors = ['--color-blue', '--color-green', '--color-orange', '--color-purple', '--color-teal', '--color-pink'];
          foreach ($tags as $index => $tag) {
            $count = $tag->count;
            $color = $colors[$index % count($colors)];
            echo '<a href="' . get_tag_link($tag->term_id) . '" class="baum-badge" style="text-decoration: none; background: var(' . $color . '); color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">';
            echo esc_html($tag->name);
            if ($count > 0) {
              echo ' <span style="background: rgba(255,255,255,0.3); padding: 1px 4px; border-radius: 8px; margin-left: 4px;">' . $count . '</span>';
            }
            echo '</a>';
          }
          ?>
        </div>

        <h3>Tag Cloud (Word Cloud Style)</h3>
        <div class="widget widget_tag_cloud" style="line-height: 1.8;">
          <?php
          $tags = get_tags(['hide_empty' => false, 'number' => 25, 'orderby' => 'count', 'order' => 'DESC']);
          $colors = ['--color-blue', '--color-green', '--color-orange', '--color-purple', '--color-teal', '--color-pink', '--color-red'];
          foreach ($tags as $index => $tag) {
            $count = $tag->count;
            $color = $colors[$index % count($colors)];
            // Size based on count (12px to 24px)
            $size = min(24, max(12, 12 + ($count * 2)));
            echo '<a href="' . get_tag_link($tag->term_id) . '" style="text-decoration: none; color: var(' . $color . '); font-size: ' . $size . 'px; font-weight: bold; margin: 0 8px 8px 0; display: inline-block; transition: all 0.2s ease;" onmouseover="this.style.transform=\'scale(1.1)\'" onmouseout="this.style.transform=\'scale(1)\'">';
            echo esc_html($tag->name);
            echo '</a>';
          }
          ?>
        </div>

        <h3>Apple Menu Dropdowns</h3>
        <div style="margin: 20px 0;">
          <p style="margin-bottom: 15px; font-weight: 600;">Interactive Apple-style menu dropdowns with notifications and developer tools:</p>

          <!-- Notifications Menu -->
          <div style="margin-bottom: 30px;">
            <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Notifications Menu</h4>
            <div class="apple-menu-demo" style="position: relative; display: inline-block;">
              <button class="notifications-trigger" style="background: rgba(240, 240, 240, 0.95); border: 1px solid rgba(128, 128, 128, 0.5); border-radius: var(--border-radius); padding: 8px 12px; cursor: pointer; font-size: 12px; font-weight: 800; color: var(--color-tertiary); position: relative;">
                <i class="fas fa-bell" style="margin-right: 6px;"></i>
                Notifications
                <span class="notification-badge" style="position: absolute; top: -5px; right: -5px; background: #ff4444; color: white; border-radius: 50%; width: 18px; height: 18px; font-size: 10px; display: flex; align-items: center; justify-content: center; font-weight: bold;">3</span>
              </button>

              <div class="notifications-dropdown" style="display: none; position: absolute; top: 100%; left: 0; background: rgba(240, 240, 240, 0.95); border: 1px solid rgba(128, 128, 128, 0.5); border-radius: var(--border-radius); box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.25); min-width: 350px; max-width: 400px; z-index: 1000; margin-top: 5px;">
                <div style="padding: 15px; border-bottom: 1px solid rgba(128, 128, 128, 0.3);">
                  <h4 style="margin: 0; font-size: 16px; color: var(--color-body-text);">Notifications</h4>
                </div>

                <div class="notifications-list" style="max-height: 400px; overflow-y: auto;">
                  <!-- Notification with link -->
                  <div class="notification-item" data-link="/new-post" style="padding: 12px 15px; border-bottom: 1px solid rgba(128, 128, 128, 0.2); cursor: pointer; transition: background 0.2s ease; display: flex; align-items: flex-start; gap: 12px;" onmouseover="this.style.background='rgba(0,0,0,0.05)'" onmouseout="this.style.background='transparent'">
                    <div class="notification-icon" style="width: 32px; height: 32px; background: var(--color-blue); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                      <i class="fas fa-file-alt" style="color: white; font-size: 14px;"></i>
                    </div>
                    <div class="notification-content" style="flex: 1; min-width: 0;">
                      <div class="notification-text" style="font-size: 13px; color: var(--color-body-text); line-height: 1.4; margin-bottom: 4px;">
                        <strong>New post published:</strong> "Advanced WordPress Development Techniques" is now live
                      </div>
                      <div class="notification-time" style="font-size: 11px; color: #666;">2 minutes ago</div>
                    </div>
                    <button class="notification-delete" onclick="deleteNotification(this, event)" style="background: none; border: none; color: #999; cursor: pointer; padding: 4px; border-radius: 3px; opacity: 0; transition: opacity 0.2s ease;" onmouseover="this.style.opacity='1'; this.style.color='#ff4444'" onmouseout="this.style.opacity='0.5'; this.style.color='#999'">
                      <i class="fas fa-times" style="font-size: 12px;"></i>
                    </button>
                  </div>

                  <!-- Notification without link -->
                  <div class="notification-item" style="padding: 12px 15px; border-bottom: 1px solid rgba(128, 128, 128, 0.2); display: flex; align-items: flex-start; gap: 12px;">
                    <div class="notification-icon" style="width: 32px; height: 32px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                      <i class="fas fa-check" style="color: white; font-size: 14px;"></i>
                    </div>
                    <div class="notification-content" style="flex: 1; min-width: 0;">
                      <div class="notification-text" style="font-size: 13px; color: var(--color-body-text); line-height: 1.4; margin-bottom: 4px;">
                        <strong>Backup completed:</strong> Your site backup has been successfully created
                      </div>
                      <div class="notification-time" style="font-size: 11px; color: #666;">1 hour ago</div>
                    </div>
                    <button class="notification-delete" onclick="deleteNotification(this, event)" style="background: none; border: none; color: #999; cursor: pointer; padding: 4px; border-radius: 3px; opacity: 0; transition: opacity 0.2s ease;" onmouseover="this.style.opacity='1'; this.style.color='#ff4444'" onmouseout="this.style.opacity='0.5'; this.style.color='#999'">
                      <i class="fas fa-times" style="font-size: 12px;"></i>
                    </button>
                  </div>

                  <!-- Notification with link -->
                  <div class="notification-item" data-link="/comments" style="padding: 12px 15px; border-bottom: 1px solid rgba(128, 128, 128, 0.2); cursor: pointer; transition: background 0.2s ease; display: flex; align-items: flex-start; gap: 12px;" onmouseover="this.style.background='rgba(0,0,0,0.05)'" onmouseout="this.style.background='transparent'">
                    <div class="notification-icon" style="width: 32px; height: 32px; background: var(--color-orange); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                      <i class="fas fa-comment" style="color: white; font-size: 14px;"></i>
                    </div>
                    <div class="notification-content" style="flex: 1; min-width: 0;">
                      <div class="notification-text" style="font-size: 13px; color: var(--color-body-text); line-height: 1.4; margin-bottom: 4px;">
                        <strong>New comment:</strong> John Doe commented on "WordPress Security Best Practices"
                      </div>
                      <div class="notification-time" style="font-size: 11px; color: #666;">3 hours ago</div>
                    </div>
                    <button class="notification-delete" onclick="deleteNotification(this, event)" style="background: none; border: none; color: #999; cursor: pointer; padding: 4px; border-radius: 3px; opacity: 0; transition: opacity 0.2s ease;" onmouseover="this.style.opacity='1'; this.style.color='#ff4444'" onmouseout="this.style.opacity='0.5'; this.style.color='#999'">
                      <i class="fas fa-times" style="font-size: 12px;"></i>
                    </button>
                  </div>
                </div>

                <div style="padding: 12px 15px; border-top: 1px solid rgba(128, 128, 128, 0.3); text-align: center;">
                  <button onclick="clearAllNotifications()" style="background: none; border: none; color: var(--color-secondary); font-size: 12px; cursor: pointer; text-decoration: underline;">
                    Clear all notifications
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Developer Tools Menu -->
          <div style="margin-bottom: 30px;">
            <h4 style="color: var(--color-body-text); margin-bottom: 15px;">Developer Tools Menu</h4>
            <div class="apple-menu-demo" style="position: relative; display: inline-block;">
              <button class="developer-trigger" style="background: rgba(240, 240, 240, 0.95); border: 1px solid rgba(128, 128, 128, 0.5); border-radius: var(--border-radius); padding: 8px 12px; cursor: pointer; font-size: 12px; font-weight: 800; color: var(--color-tertiary);">
                <i class="fas fa-code" style="margin-right: 6px;"></i>
                Developers
                <i class="fas fa-chevron-down" style="margin-left: 6px; font-size: 10px;"></i>
              </button>

              <div class="developer-dropdown" style="display: none; position: absolute; top: 100%; left: 0; background: #1a1a1a; border: 1px solid #333; border-radius: var(--border-radius); box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.5); min-width: 480px; z-index: 1000; margin-top: 5px; color: #e0e0e0;">

                <!-- Left Column -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0;">
                  <div style="padding: 20px; border-right: 1px solid #333;">

                    <!-- Database Section -->
                    <div style="margin-bottom: 25px;">
                      <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: #2d2d2d; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                          <i class="fas fa-database" style="color: #4a9eff; font-size: 18px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 16px; font-weight: 600; color: white; margin-bottom: 2px;">Database</div>
                          <div style="font-size: 12px; color: #999;">Fully portable Postgres database</div>
                        </div>
                      </div>
                    </div>

                    <!-- Authentication Section -->
                    <div style="margin-bottom: 25px;">
                      <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: #2d2d2d; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                          <i class="fas fa-lock" style="color: #4a9eff; font-size: 18px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 16px; font-weight: 600; color: white; margin-bottom: 2px;">Authentication</div>
                          <div style="font-size: 12px; color: #999;">User Management out of the box</div>
                        </div>
                      </div>
                    </div>

                    <!-- Storage Section -->
                    <div style="margin-bottom: 25px;">
                      <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: #2d2d2d; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                          <i class="fas fa-folder" style="color: #4a9eff; font-size: 18px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 16px; font-weight: 600; color: white; margin-bottom: 2px;">Storage</div>
                          <div style="font-size: 12px; color: #999;">Serverless storage for any media</div>
                        </div>
                      </div>
                    </div>

                    <!-- Edge Functions Section -->
                    <div style="margin-bottom: 25px;">
                      <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: #2d2d2d; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                          <i class="fas fa-cog" style="color: #4a9eff; font-size: 18px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 16px; font-weight: 600; color: white; margin-bottom: 2px;">Edge Functions</div>
                          <div style="font-size: 12px; color: #999;">Deploy code globally on the edge</div>
                        </div>
                      </div>
                    </div>

                    <!-- Realtime Section -->
                    <div>
                      <div style="display: flex; align-items: center; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; background: #2d2d2d; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                          <i class="fas fa-bolt" style="color: #4a9eff; font-size: 18px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 16px; font-weight: 600; color: white; margin-bottom: 2px;">Realtime</div>
                          <div style="font-size: 12px; color: #999;">Synchronize and broadcast events</div>
                        </div>
                      </div>
                    </div>

                  </div>

                  <!-- Right Column -->
                  <div style="padding: 20px;">

                    <!-- Modules Header -->
                    <div style="margin-bottom: 20px;">
                      <div style="font-size: 11px; color: #666; font-weight: 600; letter-spacing: 1px; text-transform: uppercase;">MODULES</div>
                    </div>

                    <!-- Vector Section -->
                    <div style="margin-bottom: 20px;">
                      <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div style="width: 32px; height: 32px; background: #2d2d2d; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                          <i class="fas fa-vector-square" style="color: #4a9eff; font-size: 14px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 14px; font-weight: 600; color: white; margin-bottom: 1px;">Vector</div>
                          <div style="font-size: 11px; color: #999;">AI toolkit to manage embeddings</div>
                        </div>
                      </div>
                    </div>

                    <!-- Cron Section -->
                    <div style="margin-bottom: 20px;">
                      <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div style="width: 32px; height: 32px; background: #2d2d2d; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                          <i class="fas fa-clock" style="color: #4a9eff; font-size: 14px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 14px; font-weight: 600; color: white; margin-bottom: 1px;">Cron</div>
                          <div style="font-size: 11px; color: #999;">Schedule and manage recurring Jobs</div>
                        </div>
                      </div>
                    </div>

                    <!-- Queues Section -->
                    <div style="margin-bottom: 20px;">
                      <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div style="width: 32px; height: 32px; background: #2d2d2d; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                          <i class="fas fa-list" style="color: #4a9eff; font-size: 14px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 14px; font-weight: 600; color: white; margin-bottom: 1px;">Queues</div>
                          <div style="font-size: 11px; color: #999;">Durable Message Queues with<br>guaranteed delivery</div>
                        </div>
                      </div>
                    </div>

                    <!-- Features Section -->
                    <div>
                      <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div style="width: 32px; height: 32px; background: #2d2d2d; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 10px;">
                          <i class="fas fa-star" style="color: #4a9eff; font-size: 14px;"></i>
                        </div>
                        <div>
                          <div style="font-size: 14px; font-weight: 600; color: white; margin-bottom: 1px;">Features</div>
                          <div style="font-size: 11px; color: #999;">Explore everything you can do with<br>Supabase.</div>
                        </div>
                      </div>
                    </div>

                  </div>
                </div>

                <!-- Bottom Section -->
                <div style="border-top: 1px solid #333; padding: 15px 20px;">

                  <!-- Customer Stories -->
                  <div style="margin-bottom: 15px;">
                    <div style="font-size: 11px; color: #666; font-weight: 600; letter-spacing: 1px; text-transform: uppercase; margin-bottom: 10px;">CUSTOMER STORIES ></div>
                    <div style="display: flex; align-items: center;">
                      <div style="width: 40px; height: 40px; background: #2d2d2d; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-right: 12px; font-size: 12px; font-weight: bold; color: #999;">
                        kayhan.<br>space
                      </div>
                      <div style="font-size: 13px; color: #ccc;">
                        Kayhan Space saw 8x improvement in developer speed when moving to Supabase
                      </div>
                    </div>
                  </div>

                  <!-- Bottom Links -->
                  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; font-size: 12px;">
                    <div>
                      <div style="font-size: 11px; color: #666; font-weight: 600; letter-spacing: 1px; text-transform: uppercase; margin-bottom: 8px;">COMPARE SUPABASE</div>
                      <div style="color: #ccc; margin-bottom: 4px;">Supabase vs Firebase</div>
                      <div style="color: #ccc; margin-bottom: 4px;">Supabase vs Heroku Postgres</div>
                      <div style="color: #ccc;">Supabase vs Auth0</div>
                    </div>
                    <div>
                      <div style="font-size: 11px; color: #666; font-weight: 600; letter-spacing: 1px; text-transform: uppercase; margin-bottom: 8px;">SOLUTIONS</div>
                      <div style="color: #ccc;">AI Builders</div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>

        </div>

        <script>
          document.addEventListener('DOMContentLoaded', function() {

            // Notifications dropdown functionality
            const notificationsBtn = document.querySelector('.notifications-trigger');
            const notificationsDropdown = document.querySelector('.notifications-dropdown');
            const notificationBadge = document.querySelector('.notification-badge');

            if (notificationsBtn && notificationsDropdown) {
              notificationsBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const isVisible = notificationsDropdown.style.display === 'block';

                // Close all dropdowns first
                document.querySelectorAll('.notifications-dropdown, .developer-dropdown').forEach(dropdown => {
                  dropdown.style.display = 'none';
                });

                // Toggle this dropdown
                notificationsDropdown.style.display = isVisible ? 'none' : 'block';
              });

              // Handle notification clicks
              document.querySelectorAll('.notification-item[data-link]').forEach(item => {
                item.addEventListener('click', function(e) {
                  if (!e.target.closest('.notification-delete')) {
                    const link = this.getAttribute('data-link');
                    if (link) {
                      console.log('Navigating to:', link);
                      // window.location.href = link; // Uncomment for actual navigation
                    }
                  }
                });
              });

              // Show delete buttons on hover
              document.querySelectorAll('.notification-item').forEach(item => {
                const deleteBtn = item.querySelector('.notification-delete');
                if (deleteBtn) {
                  item.addEventListener('mouseenter', function() {
                    deleteBtn.style.opacity = '0.5';
                  });
                  item.addEventListener('mouseleave', function() {
                    deleteBtn.style.opacity = '0';
                  });
                }
              });
            }

            // Developer dropdown functionality
            const developerBtn = document.querySelector('.developer-trigger');
            const developerDropdown = document.querySelector('.developer-dropdown');

            if (developerBtn && developerDropdown) {
              developerBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const isVisible = developerDropdown.style.display === 'block';

                // Close all dropdowns first
                document.querySelectorAll('.notifications-dropdown, .developer-dropdown').forEach(dropdown => {
                  dropdown.style.display = 'none';
                });

                // Toggle this dropdown
                developerDropdown.style.display = isVisible ? 'none' : 'block';
              });
            }

            // Close dropdowns when clicking outside
            document.addEventListener('click', function() {
              document.querySelectorAll('.notifications-dropdown, .developer-dropdown').forEach(dropdown => {
                dropdown.style.display = 'none';
              });
            });

            // Prevent dropdown from closing when clicking inside
            document.querySelectorAll('.notifications-dropdown, .developer-dropdown').forEach(dropdown => {
              dropdown.addEventListener('click', function(e) {
                e.stopPropagation();
              });
            });

          });

          /**
           * Delete a notification
           * @param {HTMLElement} deleteBtn - The delete button element
           * @param {Event} event - The click event
           */
          function deleteNotification(deleteBtn, event) {
            event.stopPropagation();
            const notificationItem = deleteBtn.closest('.notification-item');
            const notificationBadge = document.querySelector('.notification-badge');

            if (notificationItem) {
              // Animate removal
              notificationItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
              notificationItem.style.opacity = '0';
              notificationItem.style.transform = 'translateX(100%)';

              setTimeout(() => {
                notificationItem.remove();

                // Update badge count
                if (notificationBadge) {
                  const currentCount = parseInt(notificationBadge.textContent) || 0;
                  const newCount = Math.max(0, currentCount - 1);
                  notificationBadge.textContent = newCount;

                  if (newCount === 0) {
                    notificationBadge.style.display = 'none';
                  }
                }

                // Check if no notifications left
                const remainingNotifications = document.querySelectorAll('.notification-item');
                if (remainingNotifications.length === 0) {
                  const notificationsList = document.querySelector('.notifications-list');
                  if (notificationsList) {
                    notificationsList.innerHTML = '<div style="padding: 20px; text-align: center; color: #999; font-size: 13px;">No notifications</div>';
                  }
                }
              }, 300);
            }
          }

          /**
           * Clear all notifications
           */
          function clearAllNotifications() {
            const notificationsList = document.querySelector('.notifications-list');
            const notificationBadge = document.querySelector('.notification-badge');

            if (notificationsList) {
              // Animate all notifications
              const notifications = notificationsList.querySelectorAll('.notification-item');
              notifications.forEach((notification, index) => {
                setTimeout(() => {
                  notification.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                  notification.style.opacity = '0';
                  notification.style.transform = 'translateX(100%)';
                }, index * 100);
              });

              setTimeout(() => {
                notificationsList.innerHTML = '<div style="padding: 20px; text-align: center; color: #999; font-size: 13px;">No notifications</div>';

                // Hide badge
                if (notificationBadge) {
                  notificationBadge.style.display = 'none';
                }
              }, notifications.length * 100 + 300);
            }
          }
        </script>
      </section>

      <!-- =================================================================
           TABLES
           ================================================================= -->
      <section class="style-guide-section">
        <h2>Tables</h2>

        <h3>Mini Striped Table</h3>
        <div style="max-width: 400px; margin-bottom: 30px;">
          <table style="width: 100%; border-collapse: separate; border-spacing: 0; border: 1px solid #444; border-radius: var(--border-radius-small); overflow: hidden; font-size: 11px;">
            <thead>
              <tr>
                <th style="padding: 5px 10px; background: var(--color-secondary); color: white; font-size: 11px; border-right: 1px solid #444;">Name</th>
                <th style="padding: 5px 10px; background: var(--color-secondary); color: white; font-size: 11px; border-right: 1px solid #444;">Position</th>
                <th style="padding: 5px 10px; background: var(--color-secondary); color: white; font-size: 11px;">Dept</th>
              </tr>
            </thead>
            <tbody>
              <tr style="background: #fff;">
                <td style="padding: 5px 10px; border-bottom: 1px solid #444; border-right: 1px solid #444; font-size: 11px;">John Doe</td>
                <td style="padding: 5px 10px; border-bottom: 1px solid #444; border-right: 1px solid #444; font-size: 11px;">Developer</td>
                <td style="padding: 5px 10px; border-bottom: 1px solid #444; font-size: 11px;">Engineering</td>
              </tr>
              <tr>
                <td style="padding: 5px 10px; border-bottom: 1px solid #444; border-right: 1px solid #444; font-size: 11px;">Jane Smith</td>
                <td style="padding: 5px 10px; border-bottom: 1px solid #444; border-right: 1px solid #444; font-size: 11px;">Designer</td>
                <td style="padding: 5px 10px; border-bottom: 1px solid #444; font-size: 11px;">Creative</td>
              </tr>
              <tr style="background: #fff;">
                <td style="padding: 5px 10px; border-right: 1px solid #444; font-size: 11px;">Bob Johnson</td>
                <td style="padding: 5px 10px; border-right: 1px solid #444; font-size: 11px;">Manager</td>
                <td style="padding: 5px 10px; font-size: 11px;">Operations</td>
              </tr>
            </tbody>
          </table>
        </div>

        <h3>Mini Minimal Table</h3>
        <div style="max-width: 350px; margin-bottom: 30px;">
          <table style="width: 100%; border-collapse: separate; border-spacing: 0; border: 1px solid #ddd; border-radius: var(--border-radius); overflow: hidden; font-size: 12px;">
            <thead>
              <tr>
                <th style="padding: 8px 6px; border-bottom: 2px solid var(--color-primary); text-align: left; font-size: 11px;">Feature</th>
                <th style="padding: 8px 6px; border-bottom: 2px solid var(--color-primary); text-align: left; font-size: 11px;">Status</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td style="padding: 6px; border-bottom: 1px solid #eee; font-size: 11px;">Responsive Design</td>
                <td style="padding: 6px; border-bottom: 1px solid #eee; font-size: 11px;">✅ Complete</td>
              </tr>
              <tr>
                <td style="padding: 6px; border-bottom: 1px solid #eee; font-size: 11px;">Dark Mode</td>
                <td style="padding: 6px; border-bottom: 1px solid #eee; font-size: 11px;">🚧 In Progress</td>
              </tr>
              <tr>
                <td style="padding: 6px; border-bottom: 1px solid #eee; font-size: 11px;">Email Integration</td>
                <td style="padding: 6px; border-bottom: 1px solid #eee; font-size: 11px;">✅ Complete</td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <!-- =================================================================
           MODAL WINDOW EXAMPLE
           ================================================================= -->
      <section class="style-guide-section">
        <h2>Modal Window</h2>

        <button onclick="openModal()" style="padding: 10px 20px; background: var(--color-primary); color: white; border: none; border-radius: var(--border-radius); cursor: pointer;">Open Modal</button>

        <div id="sampleModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
          <div style="background-color: white; margin: 15% auto; padding: 20px; border-radius: var(--border-radius); width: 80%; max-width: 500px; position: relative;">
            <span onclick="closeModal()" style="color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer;">&times;</span>
            <h2>Modal Title</h2>
            <p>This is a sample modal window. It demonstrates how modals appear in the BaumPress theme.</p>
            <p>Modal content can include text, forms, images, or any other HTML elements.</p>
            <button onclick="closeModal()" style="padding: 8px 16px; background: var(--color-secondary); color: white; border: none; border-radius: var(--border-radius); cursor: pointer;">Close</button>
          </div>
        </div>

        <script>
        function openModal() {
          document.getElementById('sampleModal').style.display = 'block';
        }
        function closeModal() {
          document.getElementById('sampleModal').style.display = 'none';
        }
        </script>
      </section>

      <!-- =================================================================
           APPLE MENU DEMO
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Apple Menu Demo</h2>

        <div style="background: #f8f9fa; padding: 20px; border-radius: var(--border-radius); margin-bottom: 20px;">
          <p>Interactive Apple-style menu with dropdown functionality. Click on menu items to see submenus.</p>
          <p style="text-align: center; color: #666; font-size: 14px;">The menu includes: Home, Blog, Contact, Admin, About, and Search</p>
        </div>

        <!-- Apple Menu Demo -->
        <div style="display: flex; justify-content: center; margin: 20px 0;">
          <ul class="baum-apple-menu" style="background: rgba(240, 240, 240, 0.95); border-radius: var(--border-radius); padding: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
            <li>
              <a href="<?php echo home_url(); ?>"><i class="fas fa-home"></i> Home</a>
            </li>
            <li>
              <a href="#"><i class="fas fa-newspaper"></i> Blog</a>
              <ul class="sub-menu">
                <li><a href="#"><i class="fas fa-star"></i> Featured Posts</a></li>
                <li><a href="#"><i class="fas fa-clock"></i> Recent Posts</a></li>
                <li><a href="#"><i class="fas fa-tags"></i> Categories</a></li>
              </ul>
            </li>
            <li>
              <a href="#"><i class="fas fa-envelope"></i> Contact</a>
              <ul class="sub-menu">
                <li><a href="#"><i class="fas fa-phone"></i> Phone</a></li>
                <li><a href="#"><i class="fas fa-envelope"></i> Email</a></li>
                <li><a href="#"><i class="fas fa-map-marker-alt"></i> Location</a></li>
              </ul>
            </li>
            <li>
              <a href="<?php echo admin_url(); ?>"><i class="fas fa-cog"></i> Admin</a>
            </li>
            <li>
              <a href="#"><i class="fas fa-user"></i> About</a>
            </li>
            <li>
              <a href="#"><i class="fas fa-search"></i> Search</a>
            </li>
          </ul>
        </div>
      </section>


      <!-- =================================================================
           PORTFOLIO & STOCK CARDS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Portfolio & Stock Cards</h2>
        <div class="baum-grid-1-1-1" style="grid-gap: 10px; ">
          <div style="max-width: 400px; margin: 20px 0;">
            <h3 style="color: var(--color-body-text);">Portfolio Card</h3>
            <div class="portfolio-card" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid #f0f0f0;">
              <!-- Header -->
              <div style="margin-bottom: 24px;">
                <h2 style="margin: 0 0 8px 0; color: var(--color-body-text); font-size: 24px; font-weight: 700; letter-spacing: -0.02em;">PORTFOLIO</h2>
              </div>

              <!-- Search Bar -->
              <div style="margin-bottom: 24px; position: relative;">
                <input type="text" placeholder="Search..." style="width: 100%; padding: 12px 16px 12px 44px; border: 1px solid #e8e8e8; border-radius: 12px; font-size: 16px; color: var(--color-body-text); background: #f8f9fa; box-sizing: border-box; transition: all 0.2s ease;">
                <i class="fas fa-search" style="position: absolute; left: 16px; top: 50%; transform: translateY(-50%); color: #999; font-size: 14px;"></i>
              </div>

              <!-- Stock Items -->
              <div style="display: flex; flex-direction: column; gap: 16px;">
                <!-- ZM Stock -->
                <div class="stock-item" style="display: flex; align-items: center; justify-content: space-between; padding: 16px 0; border-bottom: 1px solid #f5f5f5;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 4px;">
                      <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">ZM</span>
                      <div style="width: 60px; height: 20px;"></div>
                    </div>
                    <div style="color: #666; font-size: 14px; font-weight: 400;">Zoom Video...</div>
                  </div>
                  <div style="text-align: right;">
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 4px;">492.60</div>
                    <div style="background: #2c2c2c; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 600;">+24.13</div>
                  </div>
                </div>

                <!-- SPY Stock -->
                <div class="stock-item" style="display: flex; align-items: center; justify-content: space-between; padding: 16px 0; border-bottom: 1px solid #f5f5f5;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 4px;">
                      <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">SPY</span>
                      <div style="width: 60px; height: 20px;"></div>
                    </div>
                    <div style="color: #666; font-size: 14px; font-weight: 400;">SPDR S&P...</div>
                  </div>
                  <div style="text-align: right;">
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 4px;">330.30</div>
                    <div style="background: #2c2c2c; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 600;">+3.33</div>
                  </div>
                </div>

                <!-- NFLX Stock -->
                <div class="stock-item" style="display: flex; align-items: center; justify-content: space-between; padding: 16px 0; border-bottom: 1px solid #f5f5f5;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 4px;">
                      <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">NFLX</span>
                      <div style="width: 60px; height: 20px; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 60 20\"><path d=\"M5 8 Q15 12 25 10 T45 14 L55 16\" stroke=\"%23ccc\" stroke-width=\"2\" fill=\"none\"/></svg>') no-repeat center; background-size: contain;"></div>
                    </div>
                    <div style="color: #666; font-size: 14px; font-weight: 400;">Netflix Inc...</div>
                  </div>
                  <div style="text-align: right;">
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 4px;">482.47</div>
                    <div style="background: #e8e8e8; color: #666; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 600;">-4.3</div>
                  </div>
                </div>

                <!-- XG/USD Stock -->
                <div class="stock-item" style="display: flex; align-items: center; justify-content: space-between; padding: 16px 0; border-bottom: 1px solid #f5f5f5;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 4px;">
                      <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">XG/USD</span>
                      <div style="width: 60px; height: 20px; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 60 20\"><path d=\"M5 15 Q15 10 25 12 T45 8 L55 5\" stroke=\"%234285f4\" stroke-width=\"2\" fill=\"none\"/></svg>') no-repeat center; background-size: contain;"></div>
                    </div>
                    <div style="color: #666; font-size: 14px; font-weight: 400;">Coppe Po...</div>
                  </div>
                  <div style="text-align: right;">
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 4px;">318.05</div>
                    <div style="background: #2c2c2c; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 600;">+3.25%</div>
                  </div>
                </div>

                <!-- USD/JPY Stock -->
                <div class="stock-item" style="display: flex; align-items: center; justify-content: space-between; padding: 16px 0; border-bottom: 1px solid #f5f5f5;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 4px;">
                      <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">USD/JPY</span>
                      <div style="width: 60px; height: 20px; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 60 20\"><path d=\"M5 12 Q15 8 25 10 T45 6 L55 4\" stroke=\"%234285f4\" stroke-width=\"2\" fill=\"none\"/></svg>') no-repeat center; background-size: contain;"></div>
                    </div>
                    <div style="color: #666; font-size: 14px; font-weight: 400;">US Dollar Jap...</div>
                  </div>
                  <div style="text-align: right;">
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 4px;">104.93</div>
                    <div style="background: #2c2c2c; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 600;">+0.29</div>
                  </div>
                </div>

                <!-- ETH/USD Stock -->
                <div class="stock-item" style="display: flex; align-items: center; justify-content: space-between; padding: 16px 0;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 4px;">
                      <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">ETH/USD</span>
                      <div style="width: 60px; height: 20px; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 60 20\"><path d=\"M5 15 Q15 10 25 12 T45 8 L55 5\" stroke=\"%234285f4\" stroke-width=\"2\" fill=\"none\"/></svg>') no-repeat center; background-size: contain;"></div>
                    </div>
                    <div style="color: #666; font-size: 14px; font-weight: 400;">Ethereum US...</div>
                  </div>
                  <div style="text-align: right;">
                    <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600; margin-bottom: 4px;">344.19</div>
                    <div style="background: #2c2c2c; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 600;">+3.96</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div style="max-width: 400px; margin: 20px 0;">
            <h3 style="color: var(--color-body-text);">Stock Detail Card</h3>
            <div class="stock-detail-card" style="background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); border: 1px solid #f0f0f0;">
              <!-- Header -->
              <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 20px;">
                <div style="width: 60px; height: 60px; background: #000; border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                  🍎
                </div>
                <div>
                  <h3 style="margin: 0 0 4px 0; color: var(--color-body-text); font-size: 20px; font-weight: 700;">Apple Inc.</h3>
                  <div style="color: #666; font-size: 14px; font-weight: 500;">AAPL</div>
                </div>
              </div>

              <!-- Price and Change -->
              <div style="margin-bottom: 24px;">
                <div style="display: flex; align-items: baseline; gap: 12px; margin-bottom: 8px;">
                  <span style="color: #4285f4; font-size: 28px; font-weight: 700;">111.81 USD</span>
                  <div style="display: flex; align-items: center; gap: 6px;">
                    <span style="color: #34c759; font-size: 16px; font-weight: 600;">+1.73</span>
                    <span style="color: #34c759; font-size: 16px; font-weight: 600;">+1.57%</span>
                    <i class="fas fa-caret-up" style="color: #34c759; font-size: 14px;"></i>
                  </div>
                </div>
              </div>

              <!-- Stats Grid -->
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 24px;">
                <div>
                  <div style="color: #666; font-size: 14px; margin-bottom: 4px;">Open</div>
                  <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">112.68</div>
                </div>
                <div>
                  <div style="color: #666; font-size: 14px; margin-bottom: 4px;">Prev Close</div>
                  <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">110.08</div>
                </div>
                <div>
                  <div style="color: #666; font-size: 14px; margin-bottom: 4px;">Volume</div>
                  <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">183,055,376</div>
                </div>
                <div>
                  <div style="color: #666; font-size: 14px; margin-bottom: 4px;">Market Cap</div>
                  <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">1.883T</div>
                </div>
                <div>
                  <div style="color: #666; font-size: 14px; margin-bottom: 4px;">Day Range</div>
                  <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">109.16 - 112.86</div>
                </div>
                <div>
                  <div style="color: #666; font-size: 14px; margin-bottom: 4px;">52 Week Range</div>
                  <div style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">53.15 - 137.98</div>
                </div>
              </div>

              <!-- Chart -->
              <div style="margin-bottom: 16px;">
                <div style="height: 120px; position: relative; background: linear-gradient(to bottom, rgba(66, 133, 244, 0.1) 0%, rgba(66, 133, 244, 0.05) 100%); border-radius: 8px; overflow: hidden;">
                  <!-- Chart Line -->
                  <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" viewBox="0 0 400 120">
                    <path d="M20 80 Q80 60 140 70 T260 50 L380 30" stroke="#4285f4" stroke-width="3" fill="none"/>
                    <circle cx="380" cy="30" r="6" fill="#4285f4"/>
                    <line x1="380" y1="30" x2="380" y2="120" stroke="#4285f4" stroke-width="1" stroke-dasharray="2,2" opacity="0.5"/>
                  </svg>
                </div>
                <!-- Chart Labels -->
                <div style="display: flex; justify-content: space-between; margin-top: 8px; padding: 0 20px;">
                  <span style="color: #666; font-size: 12px;">Fri 18</span>
                  <span style="color: #666; font-size: 12px;">Mon 21</span>
                  <span style="color: #666; font-size: 12px; font-weight: 600;">Tue 22</span>
                </div>
              </div>
            </div>
          </div>

          <div style="max-width: 400px; margin: 20px 0;">
            <h3 style="color: var(--color-body-text);">Company Profile Card</h3>
            <div class="company-profile-card" style="background: white; border: 1px solid #e0e0e0; border-radius: var(--border-radius); padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
              <!-- Company Header -->
              <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px;">
                <div style="width: 60px; height: 60px; background: #000; border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; color: white; font-size: 24px; font-weight: bold;">
                  🍎
                </div>
                <div>
                  <h3 style="margin: 0 0 5px 0; color: var(--color-body-text); font-size: 18px; font-weight: 600;">Apple Inc.</h3>
                  <div style="display: flex; gap: 10px; align-items: center;">
                    <span style="background: #f0f0f0; color: #666; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 600;">AAPL</span>
                    <span style="color: #666; font-size: 14px;">NASDAQ</span>
                  </div>
                </div>
              </div>

              <!-- Description -->
              <div style="margin-bottom: 20px;">
                <h4 style="margin: 0 0 8px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">Description</h4>
                <p style="margin: 0; color: #666; font-size: 13px; line-height: 1.4;">
                  Apple Inc. designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories worldwide.
                  <a href="#" style="color: var(--color-primary); text-decoration: none;">Read more</a>
                </p>
              </div>

              <!-- About Section -->
              <div style="margin-bottom: 20px;">
                <h4 style="margin: 0 0 12px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">About</h4>
                <div style="display: grid; gap: 8px;">
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: #666; font-size: 13px;">CEO</span>
                    <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">Mr. Timothy D. Cook</span>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: #666; font-size: 13px;">Employees</span>
                    <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">161,000</span>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <span style="color: #666; font-size: 13px;">Address</span>
                    <div style="text-align: right;">
                      <div style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">One Apple Park Way</div>
                      <div style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">Cupertino, 95014, CA</div>
                      <div style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">United States</div>
                    </div>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: #666; font-size: 13px;">Website</span>
                    <a href="#" style="color: var(--color-primary); font-size: 13px; text-decoration: none;">apple.com</a>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: #666; font-size: 13px;">Sector</span>
                    <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">Technology</span>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: #666; font-size: 13px;">Industry</span>
                    <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">Consumer electronics</span>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="color: #666; font-size: 13px;">MIC code</span>
                    <span style="color: var(--color-body-text); font-size: 13px; font-weight: 500;">XNAS</span>
                  </div>
                </div>
              </div>

              <!-- Earnings Chart -->
              <div>
                <h4 style="margin: 0 0 12px 0; color: var(--color-body-text); font-size: 14px; font-weight: 600;">Earnings</h4>
                <div style="display: flex; align-items: end; gap: 8px; height: 80px;">
                  <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                    <div style="background: #007aff; height: 45px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.19</div>
                    <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-06-29</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                    <div style="background: #34c759; height: 50px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.26</div>
                    <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-09-29</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                    <div style="background: #007aff; height: 55px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.39</div>
                    <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-09-29</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                    <div style="background: #34c759; height: 58px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.46</div>
                    <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-12-30</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                    <div style="background: #007aff; height: 65px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">1.92</div>
                    <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-12-30</div>
                  </div>
                  <div style="display: flex; flex-direction: column; align-items: center; flex: 1;">
                    <div style="background: #34c759; height: 70px; width: 100%; border-radius: 4px 4px 0 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 11px; font-weight: 600;">2.00</div>
                    <div style="font-size: 10px; color: #666; margin-top: 4px; text-align: center;">2023-12-30</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div style="max-width: 300px; margin: 20px 0;">
            <h3 style="color: var(--color-body-text);">Compact Stock Card</h3>
            <div class="compact-stock-card" style="background: white; border-radius: 12px; padding: 16px; box-shadow: 0 2px 12px rgba(0,0,0,0.06); border: 1px solid #f0f0f0;">
              <div style="display: flex; align-items: center; gap: 12px;">
                <div style="width: 40px; height: 40px; background: #000; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; font-weight: 600;">
                  ₿
                </div>
                <div style="flex: 1;">
                  <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 2px;">
                    <span style="color: var(--color-body-text); font-size: 16px; font-weight: 600;">BTC</span>
                    <span style="color: #4285f4; font-size: 16px; font-weight: 600;">10,617.94 USD</span>
                  </div>
                  <div style="display: flex; align-items: center; justify-content: space-between;">
                    <span style="color: #666; font-size: 14px;">Bitcoin - Binance</span>
                    <div style="display: flex; align-items: center; gap: 4px;">
                      <span style="color: #34c759; font-size: 14px; font-weight: 600;">+85.32</span>
                      <span style="color: #34c759; font-size: 14px; font-weight: 600;">+0.81%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>

      </section>

      <!-- =================================================================
           NAVIGATION COMPONENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2>Navigation Components</h2>

        <h3>Breadcrumbs</h3>
        <nav style="margin-bottom: 30px;">
          <ol style="display: flex; list-style: none; padding: 0; margin: 0; background: var(--color-quaternary); padding: 10px 15px; border-radius: var(--border-radius);">
            <li><a href="#" style="color: var(--color-primary); text-decoration: none;">Home</a></li>
            <li style="margin: 0 10px; color: #666;">/</li>
            <li><a href="#" style="color: var(--color-primary); text-decoration: none;">Category</a></li>
            <li style="margin: 0 10px; color: #666;">/</li>
            <li style="color: #666;">Current Page</li>
          </ol>
        </nav>

        <h3>Tab Navigation (Functional)</h3>
        <div style="margin-bottom: 30px;">
          <div class="tab-navigation" style="display: flex; border-bottom: 1px solid var(--color-quinary);">
            <button class="tab-button active" data-tab="tab1" style="padding: 10px 20px; color: black; border-radius: var(--border-radius-small) var(--border-radius-small) 0 0; cursor: pointer;">Tab 1</button>
            <button class="tab-button" data-tab="tab2" style="padding: 10px 20px; color: #666; border-radius: var(--border-radius-small) var(--border-radius-small) 0 0; cursor: pointer;">Tab 2</button>
            <button class="tab-button" data-tab="tab3" style="padding: 10px 20px; color: #666; border-radius: var(--border-radius-small) var(--border-radius-small) 0 0; cursor: pointer;">Tab 3</button>
          </div>
          <div class="baum-tab-content">
            <div id="tab1" class="tab-panel active" style="padding: 20px;">
              <h4>🌟 Introduction to Lorem Ipsum</h4>
              <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
              <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>

              <h5>Key Features:</h5>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: var(--border-radius); border-left: 4px solid var(--color-primary);">
                  <strong>📝 Text Generation</strong><br>
                  <small>Automatic placeholder text creation for design mockups and prototypes.</small>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: var(--border-radius); border-left: 4px solid var(--color-secondary);">
                  <strong>🎨 Design Focus</strong><br>
                  <small>Allows designers to focus on layout without being distracted by content.</small>
                </div>
              </div>

              <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
              <button class="baum-button-small">Learn More</button>
            </div>
            <div id="tab2" class="tab-panel" style="padding: 20px; display: none;">
              <h4>📚 Historical Background</h4>
              <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>

              <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: var(--border-radius); margin: 20px 0;">
                <h5 style="margin: 0 0 10px 0; color: white;">📖 Did You Know?</h5>
                <p style="margin: 0; font-size: 14px;">Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.</p>
              </div>

              <h5>Timeline of Usage:</h5>
              <ul style="list-style: none; padding: 0;">
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">📅 <strong>1500s:</strong> First known use in printing industry</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">📅 <strong>1960s:</strong> Popularized with Letraset sheets</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">📅 <strong>1980s:</strong> Adopted by desktop publishing software</li>
                <li style="padding: 8px 0; border-bottom: 1px solid #eee;">📅 <strong>1990s:</strong> Became standard in web design</li>
                <li style="padding: 8px 0;">📅 <strong>2000s+:</strong> Universal placeholder text standard</li>
              </ul>

              <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
            </div>
            <div id="tab3" class="tab-panel" style="padding: 20px; display: none;">
              <h4>🔧 Modern Applications</h4>
              <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>

              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                <div style="background: white; border: 1px solid #ddd; border-radius: var(--border-radius); padding: 15px;">
                  <h6 style="margin: 0 0 10px 0; color: var(--color-primary);">💻 Web Development</h6>
                  <p style="margin: 0; font-size: 13px; color: #666;">Used extensively in wireframes, mockups, and content management systems during the development phase.</p>
                </div>
                <div style="background: white; border: 1px solid #ddd; border-radius: var(--border-radius); padding: 15px;">
                  <h6 style="margin: 0 0 10px 0; color: var(--color-secondary);">🎨 Graphic Design</h6>
                  <p style="margin: 0; font-size: 13px; color: #666;">Essential for creating layouts, brochures, and marketing materials before final content is available.</p>
                </div>
                <div style="background: white; border: 1px solid #ddd; border-radius: var(--border-radius); padding: 15px;">
                  <h6 style="margin: 0 0 10px 0; color: var(--color-accent);">📱 App Design</h6>
                  <p style="margin: 0; font-size: 13px; color: #666;">Critical for mobile app prototyping and user interface testing across different screen sizes.</p>
                </div>
              </div>

              <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: var(--border-radius); margin: 20px 0;">
                <strong>⚠️ Best Practice:</strong> Always replace Lorem Ipsum with real content before launching any project to ensure proper SEO and user experience.
              </div>

              <p>Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.</p>

              <div style="text-align: center; margin-top: 20px;">
                <button class="baum-button" style="margin-right: 10px;">Download Guide</button>
                <button class="baum-button-small">Share Article</button>
              </div>
            </div>
          </div>
        </div>

        <script>
        // Functional tab navigation
        document.addEventListener('DOMContentLoaded', function() {
          const tabButtons = document.querySelectorAll('.tab-button');
          const tabPanels = document.querySelectorAll('.tab-panel');

          tabButtons.forEach(button => {
            button.addEventListener('click', function() {
              const targetTab = this.getAttribute('data-tab');

              // Remove active class from all buttons and panels
              tabButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.style.background = '#f8f9fa';
                btn.style.color = '#666';
              });
              tabPanels.forEach(panel => {
                panel.classList.remove('active');
                panel.style.display = 'none';
              });

              // Add active class to clicked button and corresponding panel
              this.classList.add('active');
              this.style.background = 'var(--color-primary)';
              this.style.color = 'white';
              document.getElementById(targetTab).classList.add('active');
              document.getElementById(targetTab).style.display = 'block';
            });
          });
        });
        </script>
      </section>



      <!-- =================================================================
           STAR RATINGS
           ================================================================= -->
      <section class="style-guide-section">
        <h2>Star Ratings</h2>

        <h3>Star Rating Examples</h3>
        <div style="display: flex; flex-direction: column; gap: 15px;">
          <div>
            <strong>5 Stars:</strong>
            <?php echo display_stars(5.0); ?>
            <span style="margin-left: 10px; color: #666;">(5.0)</span>
          </div>
          <div>
            <strong>4.5 Stars:</strong>
            <?php echo display_stars(4.5); ?>
            <span style="margin-left: 10px; color: #666;">(4.5)</span>
          </div>
          <div>
            <strong>4 Stars:</strong>
            <?php echo display_stars(4.0); ?>
            <span style="margin-left: 10px; color: #666;">(4.0)</span>
          </div>
          <div>
            <strong>3.5 Stars:</strong>
            <?php echo display_stars(3.5); ?>
            <span style="margin-left: 10px; color: #666;">(3.5)</span>
          </div>
          <div>
            <strong>2 Stars:</strong>
            <?php echo display_stars(2.0); ?>
            <span style="margin-left: 10px; color: #666;">(2.0)</span>
          </div>
          <div>
            <strong>1 Star:</strong>
            <?php echo display_stars(1.0); ?>
            <span style="margin-left: 10px; color: #666;">(1.0)</span>
          </div>
          <div>
            <strong>0 Stars:</strong>
            <?php echo display_stars(0.0); ?>
            <span style="margin-left: 10px; color: #666;">(0.0)</span>
          </div>
        </div>
      </section>

      <!-- =================================================================
           FACT CHECK COMPONENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Fact Check Components</h2>

        <h3 style="color: var(--color-body-text);">Fact Check Template Part - Full Display</h3>
        <div style="width: 66.67%; margin: 20px 0;">
          <?php
          // Simulate fact check data for display
          global $post;
          $original_post = $post;

          // Create a temporary post object with fact check fields
          $temp_post = new stdClass();
          $temp_post->ID = 999999; // Fake ID for demo

          // Mock ACF fields for fact check display
          add_filter('acf/load_value/name=the_ruling', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'true';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=the_ruling_text', function($value, $post_id, $field) {
            if ($post_id == 999999) return '<p>This claim has been thoroughly investigated and found to be factually correct based on multiple reliable sources and expert analysis.</p>';
            return $value;
          }, 10, 3);

          $post = $temp_post;
          get_template_part('parts/baum-single-factcheck');
          $post = $original_post;
          ?>
        </div>

        <h3 style="color: var(--color-body-text);">Fact Check Template Part - Line Display</h3>
        <div style="width: 66.67%; margin: 20px 0;">
          <?php
          $post = $temp_post;
          get_template_part('parts/baum-single-factcheck', null, ['return_value' => 'line']);
          $post = $original_post;
          ?>
        </div>
      </section>

      <!-- =================================================================
           REVIEW COMPONENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Review Components</h2>

        <h3 style="color: var(--color-body-text);">Review Template Part - Full Display</h3>
        <div style="margin: 20px 0; width: 66.67%; ">
          <?php
          // Mock ACF fields for review display
          add_filter('acf/load_value/name=review_title', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Excellent Product Review';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=review_type', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Product';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=summary', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'This product exceeded our expectations with excellent build quality, great performance, and outstanding value for money. Highly recommended for anyone looking for a reliable solution.';
            return $value;
          }, 10, 3);

          // Mock rating fields
          add_filter('acf/load_value/name=rating_1_label', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Quality';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=rating_1', function($value, $post_id, $field) {
            if ($post_id == 999999) return 4.5;
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=rating_2_label', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Performance';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=rating_2', function($value, $post_id, $field) {
            if ($post_id == 999999) return 4.0;
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=rating_3_label', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Value';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=rating_3', function($value, $post_id, $field) {
            if ($post_id == 999999) return 4.8;
            return $value;
          }, 10, 3);

          $post = $temp_post;
          get_template_part('parts/baum-single-review');
          $post = $original_post;
          ?>
        </div>

        <h3 style="color: var(--color-body-text);">Review Template Part - Line Display</h3>
        <div style="margin: 20px 0;">
          <?php
          $post = $temp_post;
          get_template_part('parts/baum-single-review', null, ['return_value' => 'line']);
          $post = $original_post;
          ?>
        </div>
      </section>

      <!-- =================================================================
           COMPARISON COMPONENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Comparison Components</h2>

        <h3 style="color: var(--color-body-text);">Comparison Template Part - Full Display</h3>
        <div style="margin: 20px 0; width: 66.7%; ">
          <?php
          // Mock ACF fields for comparison display
          add_filter('acf/load_value/name=comparison_title', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Smartphone Comparison 2024';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=comparison_type', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Product';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=comparison_summary', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'A comprehensive comparison of the latest flagship smartphones, evaluating performance, camera quality, battery life, and overall value.';
            return $value;
          }, 10, 3);

          ?>
        </div>


        <div style="margin: 20px 0; width: 66.7%; ">
        <?php

          add_filter('acf/load_value/name=comparison_winner', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'iPhone 15 Pro';
            return $value;
          }, 10, 3);

          // Mock item data
          add_filter('acf/load_value/name=item_1_name', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'iPhone 15 Pro';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=item_1_rating', function($value, $post_id, $field) {
            if ($post_id == 999999) return 4.8;
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=item_1_price', function($value, $post_id, $field) {
            if ($post_id == 999999) return '$999';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=item_2_name', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Samsung Galaxy S24';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=item_2_rating', function($value, $post_id, $field) {
            if ($post_id == 999999) return 4.6;
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=item_2_price', function($value, $post_id, $field) {
            if ($post_id == 999999) return '$899';
            return $value;
          }, 10, 3);

          // Mock criteria
          add_filter('acf/load_value/name=criterion_1_label', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Camera Quality';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=criterion_1_item_1', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Excellent';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=criterion_1_item_2', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Very Good';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=criterion_2_label', function($value, $post_id, $field) {
            if ($post_id == 999999) return 'Battery Life';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=criterion_2_item_1', function($value, $post_id, $field) {
            if ($post_id == 999999) return '24 hours';
            return $value;
          }, 10, 3);

          add_filter('acf/load_value/name=criterion_2_item_2', function($value, $post_id, $field) {
            if ($post_id == 999999) return '26 hours';
            return $value;
          }, 10, 3);

          $post = $temp_post;
          get_template_part('parts/baum-comparison');
          $post = $original_post;

        ?>
        </div>

        <h3 style="color: var(--color-body-text);">
          Comparison Template Part - Line Display
        </h3>
        <div style="margin: 20px 0;">
          <?php
          $post = $temp_post;
          get_template_part('parts/baum-single-comparison', null, ['return_value' => 'line']);
          $post = $original_post;
          ?>
        </div>




































    <div class="comparison-table-container" style="overflow-x: auto; margin-bottom: 20px;">
      <table class="baum-comparison-table" style="width: 100%; max-width: 600px; border-collapse: separate; border-spacing: 0; border: 1px solid var(--color-secondary); border-radius: var(--border-radius); overflow: hidden; font-size: 14px;">
        <thead>
          <tr style="background: var(--color-secondary); color: white;">
            <th style="padding: 12px; text-align: left; font-weight: 600; border-right: 1px solid rgba(255,255,255,0.2);">Feature</th>
                          <th style="padding: 12px; text-align: center; font-weight: 600; border-right: 1px solid rgba(255,255,255,0.2);">
                iPhone 15 Pro              </th>
                                      <th style="padding: 12px; text-align: center; font-weight: 600; ">
                Samsung Galaxy S24              </th>
                                  </tr>
        </thead>
        <tbody>
          <!-- Rating Row -->
                      <tr style="background: #f8f9fa;">
              <td style="padding: 10px 12px; font-weight: 600; border-right: 1px solid #e0e0e0; border-bottom: 1px solid #e0e0e0;">Rating</td>
                              <td style="padding: 10px 12px; text-align: center; border-right: 1px solid #e0e0e0; border-bottom: 1px solid #e0e0e0;">
                  <i class="fas fa-star gold"></i><i class="fas fa-star gold"></i><i class="fas fa-star gold"></i><i class="fas fa-star gold"></i><i class="fas fa-star-half-alt gold"></i>                </td>
                                            <td style="padding: 10px 12px; text-align: center;  border-bottom: 1px solid #e0e0e0;">
                  <i class="fas fa-star gold"></i><i class="fas fa-star gold"></i><i class="fas fa-star gold"></i><i class="fas fa-star gold"></i><i class="fas fa-star-half-alt gold"></i>                </td>
                                        </tr>

          <!-- Price Row -->
                      <tr>
              <td style="padding: 10px 12px; font-weight: 600; border-right: 1px solid #e0e0e0; border-bottom: 1px solid #e0e0e0;">Price</td>
                              <td style="padding: 10px 12px; text-align: center; border-right: 1px solid #e0e0e0; border-bottom: 1px solid #e0e0e0; font-weight: 600; color: var(--color-secondary);">
                  $999                </td>
                                            <td style="padding: 10px 12px; text-align: center;  border-bottom: 1px solid #e0e0e0; font-weight: 600; color: var(--color-secondary);">
                  $899                </td>
                                        </tr>

          <!-- Custom Criteria Rows -->
                      <tr style="background: #f8f9fa;">
              <td style="padding: 10px 12px; font-weight: 600; border-right: 1px solid #e0e0e0; border-bottom: 1px solid #e0e0e0;">
                Camera Quality              </td>
                              <td style="padding: 10px 12px; text-align: center; border-right: 1px solid #e0e0e0; border-bottom: 1px solid #e0e0e0;">
                  Excellent                </td>
                                            <td style="padding: 10px 12px; text-align: center;  border-bottom: 1px solid #e0e0e0;">
                  Very Good                </td>
                                        </tr>
                      <tr style="">
              <td style="padding: 10px 12px; font-weight: 600; border-right: 1px solid #e0e0e0; ">
                Battery Life              </td>
                              <td style="padding: 10px 12px; text-align: center; border-right: 1px solid #e0e0e0; ">
                  24 hours                </td>
                                            <td style="padding: 10px 12px; text-align: center;  ">
                  26 hours                </td>
                                        </tr>
                  </tbody>
      </table>
    </div>




      </section>

      <!-- =================================================================
           SOCIAL FOLLOWING COMPONENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Social Following Components</h2>

        <h3 style="color: var(--color-body-text);">Following Interface</h3>
        <div style="margin: 20px 0; display: grid; grid-template-columns: 1fr 1fr; gap: 40px;">

          <!-- Left Side - Empty State -->
          <div class="baum-following-container" style="background: #1a1a1a; color: white; padding: 40px 30px; border-radius: var(--border-radius); text-align: center; min-height: 400px; display: flex; flex-direction: column; justify-content: center;">
            <h2 style="color: white; font-size: 24px; margin-bottom: 30px; font-weight: 600;">Following</h2>

            <div class="empty-state" style="margin-bottom: 30px;">
              <div class="empty-avatar" style="width: 80px; height: 80px; background: #333; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-user" style="font-size: 30px; color: #666;"></i>
              </div>
              <h3 style="color: #ccc; font-size: 18px; margin-bottom: 15px; font-weight: 500;">Not following anyone yet...</h3>
              <p style="color: #888; font-size: 14px; line-height: 1.5; max-width: 280px; margin: 0 auto;">Follow creators to stay updated and inspired with the latest content!</p>
            </div>
          </div>

          <!-- Right Side - Recommended Creators -->
          <div class="baum-recommendations-container" style="background: #1a1a1a; color: white; padding: 30px; border-radius: var(--border-radius); min-height: 400px;">
            <h3 style="color: white; font-size: 18px; margin-bottom: 25px; font-weight: 600;">Recommended Creators</h3>

            <div class="creators-list" style="display: flex; flex-direction: column; gap: 15px;">

              <!-- Creator 1 -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#2a2a2a'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: #1e90ff; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">d</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: white; font-weight: 600; font-size: 14px; margin-bottom: 2px;">david jakenontom</div>
                  <div class="creator-followers" style="color: #888; font-size: 12px;">7 Followers</div>
                </div>
                <button class="follow-btn" style="background: #333; color: white; border: 1px solid #555; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <!-- Creator 2 -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#2a2a2a'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; border-radius: 50%;"></div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: white; font-weight: 600; font-size: 14px; margin-bottom: 2px;">Zoey Riley</div>
                  <div class="creator-followers" style="color: #888; font-size: 12px;">11 Followers</div>
                </div>
                <button class="follow-btn button" style="display: flex; align-items: center; gap: 5px; " onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <!-- Creator 3 -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#2a2a2a'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: linear-gradient(45deg, #ff6b9d, #c44569); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">H</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: white; font-weight: 600; font-size: 14px; margin-bottom: 2px;">Hasawi Noa</div>
                  <div class="creator-followers" style="color: #888; font-size: 12px;">7 Followers</div>
                </div>
                <button class="follow-btn" style="background: #333; color: white; border: 1px solid #555; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <!-- Creator 4 -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#2a2a2a'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">B</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: white; font-weight: 600; font-size: 14px; margin-bottom: 2px;">Brendyn Paradise</div>
                  <div class="creator-followers" style="color: #888; font-size: 12px;">4 Followers</div>
                </div>
                <button class="follow-btn" style="background: #333; color: white; border: 1px solid #555; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <!-- Creator 5 -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#2a2a2a'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: #9b59b6; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">J</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: white; font-weight: 600; font-size: 14px; margin-bottom: 2px;">Burlap</div>
                  <div class="creator-followers" style="color: #888; font-size: 12px;">9 Followers</div>
                </div>
                <button class="follow-btn" style="background: #333; color: white; border: 1px solid #555; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <!-- Creator 6 -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#2a2a2a'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: linear-gradient(45deg, #ffa726, #ff7043); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">A</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: white; font-weight: 600; font-size: 14px; margin-bottom: 2px;">Annie</div>
                  <div class="creator-followers" style="color: #888; font-size: 12px;">7 Followers</div>
                </div>
                <button class="follow-btn" style="background: #333; color: white; border: 1px solid #555; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <!-- Creator 7 -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#2a2a2a'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: linear-gradient(45deg, #42a5f5, #1e88e5); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">C</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: white; font-weight: 600; font-size: 14px; margin-bottom: 2px;">Cheney</div>
                  <div class="creator-followers" style="color: #888; font-size: 12px;">2 Followers</div>
                </div>
                <button class="follow-btn" style="background: #333; color: white; border: 1px solid #555; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <!-- Creator 8 -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#2a2a2a'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: linear-gradient(45deg, #8bc34a, #689f38); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">D</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: white; font-weight: 600; font-size: 14px; margin-bottom: 2px;">DunFLY</div>
                  <div class="creator-followers" style="color: #888; font-size: 12px;">7 Followers</div>
                </div>
                <button class="follow-btn" style="background: #333; color: white; border: 1px solid #555; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.background='#444'" onmouseout="this.style.background='#333'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

            </div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Light Theme Variation</h3>
        <div style="margin: 20px 0; display: grid; grid-template-columns: 1fr 1fr; gap: 40px;">

          <!-- Light Theme - Empty State -->
          <div class="baum-following-container-light" style="background: white; color: #333; padding: 40px 30px; border-radius: var(--border-radius); border: 1px solid #e0e0e0; text-align: center; min-height: 400px; display: flex; flex-direction: column; justify-content: center;">
            <h2 style="color: var(--color-body-text); font-size: 24px; margin-bottom: 30px; font-weight: 600;">Following</h2>

            <div class="empty-state" style="margin-bottom: 30px;">
              <div class="empty-avatar" style="width: 80px; height: 80px; background: #f5f5f5; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
                <i class="fas fa-user" style="font-size: 30px; color: #ccc;"></i>
              </div>
              <h3 style="color: #666; font-size: 18px; margin-bottom: 15px; font-weight: 500;">Not following anyone yet...</h3>
              <p style="color: #888; font-size: 14px; line-height: 1.5; max-width: 280px; margin: 0 auto;">Follow creators to stay updated and inspired with the latest content!</p>
            </div>
          </div>

          <!-- Light Theme - Recommended Creators -->
          <div class="baum-recommendations-container-light" style="background: white; color: #333; padding: 30px; border-radius: var(--border-radius); border: 1px solid #e0e0e0; min-height: 400px;">
            <h3 style="color: var(--color-body-text); font-size: 18px; margin-bottom: 25px; font-weight: 600;">Recommended Creators</h3>

            <div class="creators-list" style="display: flex; flex-direction: column; gap: 15px;">

              <!-- Light Creator Items (showing first 4) -->
              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: #1e90ff; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">d</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: var(--color-body-text); font-weight: 600; font-size: 14px; margin-bottom: 2px;">david jakenontom</div>
                  <div class="creator-followers" style="color: #666; font-size: 12px;">7 Followers</div>
                </div>
                <button class="follow-btn" style="background: var(--color-secondary); color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: linear-gradient(45deg, #ff6b9d, #c44569); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">Z</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: var(--color-body-text); font-weight: 600; font-size: 14px; margin-bottom: 2px;">Zoey Riley</div>
                  <div class="creator-followers" style="color: #666; font-size: 12px;">11 Followers</div>
                </div>
                <button class="follow-btn" style="background: var(--color-secondary); color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">H</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: var(--color-body-text); font-weight: 600; font-size: 14px; margin-bottom: 2px;">Hasawi Noa</div>
                  <div class="creator-followers" style="color: #666; font-size: 12px;">7 Followers</div>
                </div>
                <button class="follow-btn" style="background: var(--color-secondary); color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

              <div class="creator-item" style="display: flex; align-items: center; gap: 12px; padding: 12px; border-radius: 8px; transition: background 0.2s ease;" onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">
                <div class="creator-avatar" style="width: 40px; height: 40px; background: #9b59b6; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 16px;">B</div>
                <div class="creator-info" style="flex: 1;">
                  <div class="creator-name" style="color: var(--color-body-text); font-weight: 600; font-size: 14px; margin-bottom: 2px;">Brendyn Paradise</div>
                  <div class="creator-followers" style="color: #666; font-size: 12px;">4 Followers</div>
                </div>
                <button class="follow-btn" style="background: var(--color-secondary); color: white; border: none; padding: 6px 12px; border-radius: 6px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.2s ease;" onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">
                  <i class="fas fa-user-plus" style="font-size: 10px;"></i>
                  Follow
                </button>
              </div>

            </div>
          </div>
        </div>
      </section>

      <!-- =================================================================
           CONVERSATION STARTERS & CAPABILITIES
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Conversation Starters & Capabilities</h2>

        <h3 style="color: var(--color-body-text);">AI Assistant Interface</h3>
        <div style="margin: 20px 0; background: #2a2a2a; color: white; padding: 40px; border-radius: var(--border-radius); max-width: 800px;">

          <!-- Conversation Starters Section -->
          <div style="margin-bottom: 50px;">
            <h2 style="color: white; font-size: 28px; margin-bottom: 30px; font-weight: 600;">Conversation Starters</h2>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
              <!-- Starter 1 -->
              <div class="conversation-starter" style="background: #3a3a3a; border: 1px solid #555; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='#4a4a4a'; this.style.borderColor='#666'" onmouseout="this.style.background='#3a3a3a'; this.style.borderColor='#555'">
                <p style="color: #e0e0e0; font-size: 16px; line-height: 1.4; margin: 0;">Can you analyze this news article's bias?</p>
              </div>

              <!-- Starter 2 -->
              <div class="conversation-starter" style="background: #3a3a3a; border: 1px solid #555; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='#4a4a4a'; this.style.borderColor='#666'" onmouseout="this.style.background='#3a3a3a'; this.style.borderColor='#555'">
                <p style="color: #e0e0e0; font-size: 16px; line-height: 1.4; margin: 0;">What's the latest on the global economy?</p>
              </div>
            </div>

            <!-- Starter 3 (single, bottom) -->
            <div class="conversation-starter" style="background: #3a3a3a; border: 1px solid #555; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease; max-width: 48%;" onmouseover="this.style.background='#4a4a4a'; this.style.borderColor='#666'" onmouseout="this.style.background='#3a3a3a'; this.style.borderColor='#555'">
              <p style="color: #e0e0e0; font-size: 16px; line-height: 1.4; margin: 0;">How do you fact-check reports?</p>
            </div>
          </div>

          <!-- Capabilities Section -->
          <div style="margin-bottom: 50px;">
            <h2 style="color: white; font-size: 28px; margin-bottom: 30px; font-weight: 600;">Capabilities</h2>

            <div style="display: flex; flex-direction: column; gap: 15px;">
              <!-- Capability 1 -->
              <div class="capability-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="capability-icon" style="width: 24px; height: 24px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <i class="fas fa-check" style="color: white; font-size: 12px; font-weight: bold;"></i>
                </div>
                <span style="color: #e0e0e0; font-size: 18px; font-weight: 500;">Web Search</span>
              </div>

              <!-- Capability 2 -->
              <div class="capability-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="capability-icon" style="width: 24px; height: 24px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <i class="fas fa-check" style="color: white; font-size: 12px; font-weight: bold;"></i>
                </div>
                <span style="color: #e0e0e0; font-size: 18px; font-weight: 500;">DALL-E Images</span>
              </div>

              <!-- Capability 3 -->
              <div class="capability-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="capability-icon" style="width: 24px; height: 24px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <i class="fas fa-check" style="color: white; font-size: 12px; font-weight: bold;"></i>
                </div>
                <span style="color: #e0e0e0; font-size: 18px; font-weight: 500;">Code Interpreter & Data Analysis</span>
              </div>
            </div>
          </div>

          <!-- Ratings Section -->
          <div>
            <h2 style="color: white; font-size: 28px; margin-bottom: 30px; font-weight: 600;">Ratings</h2>

            <div style="display: flex; flex-direction: column; gap: 20px;">

              <!-- Rating 5 -->
              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">5</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #4a4a4a; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 85%; height: 100%; background: #22c55e; border-radius: 6px;"></div>
                </div>
              </div>

              <!-- Rating 4 -->
              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">4</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #4a4a4a; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 25%; height: 100%; background: #22c55e; border-radius: 6px;"></div>
                </div>
              </div>

              <!-- Rating 3 -->
              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">3</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #4a4a4a; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 15%; height: 100%; background: #22c55e; border-radius: 6px;"></div>
                </div>
              </div>

              <!-- Rating 2 -->
              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">2</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #4a4a4a; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 8%; height: 100%; background: #22c55e; border-radius: 6px;"></div>
                </div>
              </div>

              <!-- Rating 1 -->
              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">1</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #4a4a4a; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 12%; height: 100%; background: #22c55e; border-radius: 6px;"></div>
                </div>
              </div>

            </div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Light Theme Variation</h3>
        <div style="margin: 20px 0; background: white; color: #333; padding: 40px; border-radius: var(--border-radius); border: 1px solid #e0e0e0; max-width: 800px;">

          <!-- Light Theme Conversation Starters -->
          <div style="margin-bottom: 50px;">
            <h2 style="color: var(--color-body-text); font-size: 28px; margin-bottom: 30px; font-weight: 600;">Conversation Starters</h2>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
              <!-- Light Starter 1 -->
              <div class="conversation-starter" style="background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='#e9ecef'; this.style.borderColor='#ced4da'" onmouseout="this.style.background='#f8f9fa'; this.style.borderColor='#e0e0e0'">
                <p style="color: var(--color-body-text); font-size: 16px; line-height: 1.4; margin: 0;">Can you analyze this news article's bias?</p>
              </div>

              <!-- Light Starter 2 -->
              <div class="conversation-starter" style="background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='#e9ecef'; this.style.borderColor='#ced4da'" onmouseout="this.style.background='#f8f9fa'; this.style.borderColor='#e0e0e0'">
                <p style="color: var(--color-body-text); font-size: 16px; line-height: 1.4; margin: 0;">What's the latest on the global economy?</p>
              </div>
            </div>

            <!-- Light Starter 3 -->
            <div class="conversation-starter" style="background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.3s ease; max-width: 48%;" onmouseover="this.style.background='#e9ecef'; this.style.borderColor='#ced4da'" onmouseout="this.style.background='#f8f9fa'; this.style.borderColor='#e0e0e0'">
              <p style="color: var(--color-body-text); font-size: 16px; line-height: 1.4; margin: 0;">How do you fact-check reports?</p>
            </div>
          </div>

          <!-- Light Theme Capabilities -->
          <div style="margin-bottom: 50px;">
            <h2 style="color: var(--color-body-text); font-size: 28px; margin-bottom: 30px; font-weight: 600;">Capabilities</h2>

            <div style="display: flex; flex-direction: column; gap: 15px;">
              <div class="capability-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="capability-icon" style="width: 24px; height: 24px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <i class="fas fa-check" style="color: white; font-size: 12px; font-weight: bold;"></i>
                </div>
                <span style="color: var(--color-body-text); font-size: 18px; font-weight: 500;">Web Search</span>
              </div>

              <div class="capability-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="capability-icon" style="width: 24px; height: 24px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <i class="fas fa-check" style="color: white; font-size: 12px; font-weight: bold;"></i>
                </div>
                <span style="color: var(--color-body-text); font-size: 18px; font-weight: 500;">DALL-E Images</span>
              </div>

              <div class="capability-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="capability-icon" style="width: 24px; height: 24px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <i class="fas fa-check" style="color: white; font-size: 12px; font-weight: bold;"></i>
                </div>
                <span style="color: var(--color-body-text); font-size: 18px; font-weight: 500;">Code Interpreter & Data Analysis</span>
              </div>
            </div>
          </div>

          <!-- Light Theme Ratings -->
          <div>
            <h2 style="color: var(--color-body-text); font-size: 28px; margin-bottom: 30px; font-weight: 600;">Ratings</h2>

            <div style="display: flex; flex-direction: column; gap: 20px;">

              <!-- Light Rating Items -->
              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">5</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #e9ecef; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 85%; height: 100%; background: var(--color-green); border-radius: 6px;"></div>
                </div>
              </div>

              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">4</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #e9ecef; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 25%; height: 100%; background: var(--color-green); border-radius: 6px;"></div>
                </div>
              </div>

              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">3</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #e9ecef; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 15%; height: 100%; background: var(--color-green); border-radius: 6px;"></div>
                </div>
              </div>

              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">2</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #e9ecef; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 8%; height: 100%; background: var(--color-green); border-radius: 6px;"></div>
                </div>
              </div>

              <div class="rating-item" style="display: flex; align-items: center; gap: 15px;">
                <div class="rating-number" style="width: 32px; height: 32px; background: var(--color-green); border-radius: 50%; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                  <span style="color: white; font-size: 16px; font-weight: bold;">1</span>
                </div>
                <div class="rating-bar-container" style="flex: 1; height: 12px; background: #e9ecef; border-radius: 6px; overflow: hidden;">
                  <div class="rating-bar-fill" style="width: 12%; height: 100%; background: var(--color-green); border-radius: 6px;"></div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </section>

      <!-- =================================================================
           FEEDBACK COMPONENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Feedback Components</h2>

        <h3 style="color: var(--color-body-text);">Content Feedback Interface</h3>
        <div style="margin: 20px 0; background: #2a2a2a; color: white; padding: 40px; border-radius: var(--border-radius); max-width: 800px;">

          <!-- Feedback Question -->
          <h2 style="color: white; font-size: 24px; margin-bottom: 30px; font-weight: 600;">What did you think of this content?</h2>

          <!-- Feedback Buttons -->
          <div style="display: flex; gap: 20px; margin-bottom: 30px; flex-wrap: wrap;">

            <!-- Thumbs Up Button -->
            <button class="feedback-btn feedback-helpful" style="background: transparent; border: 1px solid #555; color: #ccc; padding: 12px 20px; border-radius: 8px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#777'; this.style.color='white'" onmouseout="this.style.borderColor='#555'; this.style.color='#ccc'" onclick="this.style.borderColor='#22c55e'; this.style.color='#22c55e';">
              <i class="fas fa-thumbs-up" style="font-size: 18px;"></i>
              <span>It was helpful</span>
            </button>

            <!-- Thumbs Down Button -->
            <button class="feedback-btn feedback-not-helpful" style="background: transparent; border: 1px solid #555; color: #ccc; padding: 12px 20px; border-radius: 8px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#777'; this.style.color='white'" onmouseout="this.style.borderColor='#555'; this.style.color='#ccc'" onclick="this.style.borderColor='#ef4444'; this.style.color='#ef4444';">
              <i class="fas fa-thumbs-down" style="font-size: 18px;"></i>
              <span>It was not helpful</span>
            </button>

            <!-- Feedback Button -->
            <button class="feedback-btn feedback-general" style="background: transparent; border: 1px solid #555; color: #ccc; padding: 12px 20px; border-radius: 8px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#777'; this.style.color='white'" onmouseout="this.style.borderColor='#555'; this.style.color='#ccc'" onclick="document.getElementById('feedbackTextarea').style.display='block'; this.style.borderColor='#3b82f6'; this.style.color='#3b82f6';">
              <i class="fas fa-comment" style="font-size: 18px;"></i>
              <span>I have feedback</span>
            </button>

          </div>

          <!-- Feedback Textarea (initially hidden) -->
          <div id="feedbackTextarea" style="display: none; margin-bottom: 30px;">
            <div style="background: #3a3a3a; border: 1px solid #555; border-radius: 12px; padding: 20px; position: relative;">
              <textarea placeholder="We'd love to hear your feedback..." style="width: 100%; min-height: 120px; background: transparent; border: none; color: #e0e0e0; font-size: 16px; line-height: 1.5; resize: vertical; outline: none; font-family: inherit;" onmouseover="this.parentElement.style.borderColor='#777'" onmouseout="this.parentElement.style.borderColor='#555'" onfocus="this.parentElement.style.borderColor='#3b82f6'" onblur="this.parentElement.style.borderColor='#555'"></textarea>

              <!-- Send Button -->
              <div style="display: flex; justify-content: flex-end; margin-top: 15px;">
                <button style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='#4b5563'" onmouseout="this.style.background='#6b7280'">
                  Send
                </button>
              </div>
            </div>
          </div>

          <!-- Support Link -->
          <div style="margin-top: 30px;">
            <p style="color: #888; font-size: 16px; margin: 0;">
              Did you mean to reach out to
              <a href="#" style="color: #3b82f6; text-decoration: underline; transition: color 0.3s ease;" onmouseover="this.style.color='#60a5fa'" onmouseout="this.style.color='#3b82f6'">support</a>?
            </p>
          </div>

        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Light Theme Variation</h3>
        <div style="margin: 20px 0; background: white; color: #333; padding: 40px; border-radius: var(--border-radius); border: 1px solid #e0e0e0; max-width: 800px;">

          <!-- Light Theme Feedback Question -->
          <h2 style="color: var(--color-body-text); font-size: 24px; margin-bottom: 30px; font-weight: 600;">What did you think of this content?</h2>

          <!-- Light Theme Feedback Buttons -->
          <div style="display: flex; gap: 20px; margin-bottom: 30px; flex-wrap: wrap;">

            <!-- Light Thumbs Up Button -->
            <button class="feedback-btn feedback-helpful-light" style="background: transparent; border: 1px solid #d1d5db; color: #6b7280; padding: 12px 20px; border-radius: 8px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#9ca3af'; this.style.color='var(--color-body-text)'" onmouseout="this.style.borderColor='#d1d5db'; this.style.color='#6b7280'" onclick="this.style.borderColor='var(--color-green)'; this.style.color='var(--color-green)';">
              <i class="fas fa-thumbs-up" style="font-size: 18px;"></i>
              <span>It was helpful</span>
            </button>

            <!-- Light Thumbs Down Button -->
            <button class="feedback-btn feedback-not-helpful-light" style="background: transparent; border: 1px solid #d1d5db; color: #6b7280; padding: 12px 20px; border-radius: 8px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#9ca3af'; this.style.color='var(--color-body-text)'" onmouseout="this.style.borderColor='#d1d5db'; this.style.color='#6b7280'" onclick="this.style.borderColor='#ef4444'; this.style.color='#ef4444';">
              <i class="fas fa-thumbs-down" style="font-size: 18px;"></i>
              <span>It was not helpful</span>
            </button>

            <!-- Light Feedback Button -->
            <button class="feedback-btn feedback-general-light" style="background: transparent; border: 1px solid #d1d5db; color: #6b7280; padding: 12px 20px; border-radius: 8px; font-size: 16px; cursor: pointer; display: flex; align-items: center; gap: 10px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#9ca3af'; this.style.color='var(--color-body-text)'" onmouseout="this.style.borderColor='#d1d5db'; this.style.color='#6b7280'" onclick="document.getElementById('feedbackTextareaLight').style.display='block'; this.style.borderColor='var(--color-secondary)'; this.style.color='var(--color-secondary)';">
              <i class="fas fa-comment" style="font-size: 18px;"></i>
              <span>I have feedback</span>
            </button>

          </div>

          <!-- Light Theme Feedback Textarea -->
          <div id="feedbackTextareaLight" style="display: none; margin-bottom: 30px;">
            <div style="background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 12px; padding: 20px; position: relative;">
              <textarea placeholder="We'd love to hear your feedback..." style="width: 100%; min-height: 120px; background: transparent; border: none; color: var(--color-body-text); font-size: 16px; line-height: 1.5; resize: vertical; outline: none; font-family: inherit;" onmouseover="this.parentElement.style.borderColor='#ced4da'" onmouseout="this.parentElement.style.borderColor='#e0e0e0'" onfocus="this.parentElement.style.borderColor='var(--color-secondary)'" onblur="this.parentElement.style.borderColor='#e0e0e0'"></textarea>

              <!-- Light Send Button -->
              <div style="display: flex; justify-content: flex-end; margin-top: 15px;">
                <button style="background: var(--color-secondary); color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 600; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">
                  Send
                </button>
              </div>
            </div>
          </div>

          <!-- Light Theme Support Link -->
          <div style="margin-top: 30px;">
            <p style="color: #6b7280; font-size: 16px; margin: 0;">
              Did you mean to reach out to
              <a href="#" style="color: var(--color-secondary); text-decoration: underline; transition: color 0.3s ease;" onmouseover="this.style.opacity='0.8'" onmouseout="this.style.opacity='1'">support</a>?
            </p>
          </div>

        </div>

        <h3 style="color: var(--color-body-text); margin-top: 40px;">Compact Variation</h3>
        <div style="margin: 20px 0; background: #2a2a2a; color: white; padding: 30px; border-radius: var(--border-radius); max-width: 600px;">

          <!-- Compact Feedback -->
          <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 20px; flex-wrap: wrap;">
            <span style="color: #ccc; font-size: 16px; font-weight: 500;">Was this helpful?</span>

            <button style="background: transparent; border: 1px solid #555; color: #ccc; padding: 8px 12px; border-radius: 6px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#22c55e'; this.style.color='#22c55e'" onmouseout="this.style.borderColor='#555'; this.style.color='#ccc'">
              <i class="fas fa-thumbs-up" style="font-size: 14px;"></i>
            </button>

            <button style="background: transparent; border: 1px solid #555; color: #ccc; padding: 8px 12px; border-radius: 6px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.borderColor='#ef4444'; this.style.color='#ef4444'" onmouseout="this.style.borderColor='#555'; this.style.color='#ccc'">
              <i class="fas fa-thumbs-down" style="font-size: 14px;"></i>
            </button>

            <button style="background: transparent; border: 1px solid #555; color: #ccc; padding: 8px 12px; border-radius: 6px; cursor: pointer; transition: all 0.3s ease; font-size: 14px;" onmouseover="this.style.borderColor='#3b82f6'; this.style.color='#3b82f6'" onmouseout="this.style.borderColor='#555'; this.style.color='#ccc'">
              Feedback
            </button>
          </div>

          <!-- Compact Support Link -->
          <p style="color: #888; font-size: 14px; margin: 0;">
            Need help? <a href="#" style="color: #3b82f6; text-decoration: underline;">Contact support</a>
          </p>

        </div>
      </section>

      <!-- =================================================================
           SHORTCODES
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Shortcodes</h2>
        <p style="color: #6b7280; margin-bottom: 30px;">All available shortcodes from functions-shortcodes.php with examples and usage.</p>

        <!-- Author & User Shortcodes -->
        <h3 style="color: var(--color-body-text);">Author & User Shortcodes</h3>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Author Tooltip</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Usage:</strong> <code>[baum_author_tooltip id="1"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_author_tooltip id="1"]'); ?>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">User Tooltip</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Usage:</strong> <code>[baum_user_tooltip id="1"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_user_tooltip id="1"]'); ?>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Person Cards</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Format 1:</strong> <code>[baum_person_card id="1" format="1"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_person_card id="1" format="1"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Format 2 (Mini):</strong> <code>[baum_person_card id="1" format="2"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_person_card id="1" format="2"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Format 3 (Substack Style):</strong> <code>[baum_person_card id="1" format="3"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_person_card id="1" format="3"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Format 4 (Avatar Only):</strong> <code>[baum_person_card id="1" format="4" size="64"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_person_card id="1" format="4" size="64"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Format 5 (Stacked Avatars):</strong> <code>[baum_person_card id="1,2,3" format="5" title="Team"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_person_card id="1,2,3" format="5" title="Team"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Format 6 (Names List):</strong> <code>[baum_person_card id="1,2,3" format="6"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_person_card id="1,2,3" format="6"]'); ?>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Authentication Card</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Usage:</strong> <code>[baum_auth_card title="true"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_auth_card title="true"]'); ?>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">User Roles</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Usage:</strong> <code>[baum_user_roles]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            Current user roles: <?php echo do_shortcode('[baum_user_roles]'); ?>
          </div>
        </div>

        <!-- Logo & Branding Shortcodes -->
        <h3 style="color: var(--color-body-text); margin-top: 40px;">Logo & Branding Shortcodes</h3>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Site Logo</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Usage:</strong> <code>[baum_logo date="false" theme="auto"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_logo date="false" theme="auto"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Site Logo (Alternative):</strong> <code>[baum_site_logo size="full" darkmode="auto"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_site_logo size="full" darkmode="auto"]'); ?>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">App Logo</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Usage:</strong> <code>[baum_app_logo width="100" height="100" theme="auto"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_app_logo width="100" height="100" theme="auto"]'); ?>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Site Icon</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Usage:</strong> <code>[baum_icon width="64" height="64" theme="auto"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_icon width="64" height="64" theme="auto"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Site Icon (Simple):</strong> <code>[site_icon]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            Site icon inline: <?php echo do_shortcode('[site_icon]'); ?>
          </div>
        </div>

        <!-- Content Shortcodes -->
        <h3 style="color: var(--color-body-text); margin-top: 40px;">Content Shortcodes</h3>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Story Collection</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Current Post:</strong> <code>[baum_story_collection query="current_post" title="Related Stories" columns="2"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_story_collection query="current_post" title="Related Stories" columns="2"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Specific Term:</strong> <code>[baum_story_collection query="specific_term" slug="example-slug" title="Featured Collection" quantity="6"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_story_collection query="specific_term" slug="example-slug" title="Featured Collection" quantity="6"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>List Terms:</strong> <code>[baum_story_collection query="list_terms" title="All Collections" quantity="9" columns="3"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_story_collection query="list_terms" title="All Collections" quantity="9" columns="3"]'); ?>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Featured Images</h4>
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Format 1 (Large + Thumbnails):</strong> <code>[baum_featured_images format="1" column="2" thumb="1-1"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_featured_images format="1" column="2" thumb="1-1"]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Format 3 (Grid):</strong> <code>[baum_featured_images format="3" column="3" thumb="1-1"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_featured_images format="3" column="3" thumb="1-1"]'); ?>
          </div>
        </div>

        <!-- Site Information Shortcodes -->
        <h3 style="color: var(--color-body-text); margin-top: 40px;">Site Information Shortcodes</h3>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Basic Site Info</h4>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div>
              <p style="color: #6b7280; margin: 0 0 5px;"><strong>Site Name:</strong> <code>[sitename]</code> or <code>[baum_sitename]</code></p>
              <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                <?php echo do_shortcode('[sitename]'); ?>
              </div>
            </div>

            <div>
              <p style="color: #6b7280; margin: 0 0 5px;"><strong>Site Tagline:</strong> <code>[baum_site_tagline]</code></p>
              <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                <?php echo do_shortcode('[baum_site_tagline]'); ?>
              </div>
            </div>

            <div>
              <p style="color: #6b7280; margin: 0 0 5px;"><strong>Site URL:</strong> <code>[site_url]</code> or <code>[baum_site_url]</code></p>
              <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                <?php echo do_shortcode('[site_url]'); ?>
              </div>
            </div>

            <div>
              <p style="color: #6b7280; margin: 0 0 5px;"><strong>Content Title:</strong> <code>[baum_title]</code></p>
              <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                <?php echo do_shortcode('[baum_title]'); ?>
              </div>
            </div>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Admin Information</h4>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div>
              <p style="color: #6b7280; margin: 0 0 5px;"><strong>Admin Name:</strong> <code>[baum_name]</code></p>
              <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                <?php echo do_shortcode('[baum_name]'); ?>
              </div>
            </div>

            <div>
              <p style="color: #6b7280; margin: 0 0 5px;"><strong>Admin Email:</strong> <code>[baum_email]</code></p>
              <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                <?php echo do_shortcode('[baum_email]'); ?>
              </div>
            </div>

            <div>
              <p style="color: #6b7280; margin: 0 0 5px;"><strong>Admin Position:</strong> <code>[baum_position]</code></p>
              <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                <?php echo do_shortcode('[baum_position]'); ?>
              </div>
            </div>

            <div>
              <p style="color: #6b7280; margin: 0 0 5px;"><strong>Admin Address:</strong> <code>[baum_address]</code></p>
              <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">
                <?php echo do_shortcode('[baum_address]'); ?>
              </div>
            </div>
          </div>
        </div>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Contact & About</h4>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Contact Info:</strong> <code>[baum_contact]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd; margin-bottom: 15px;">
            <?php echo do_shortcode('[baum_contact]'); ?>
          </div>

          <p style="color: #6b7280; margin: 0 0 10px;"><strong>About Us:</strong> <code>[baum_about]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_about]'); ?>
          </div>
        </div>

        <!-- Conditional Content Shortcodes -->
        <h3 style="color: var(--color-body-text); margin-top: 40px;">Conditional Content Shortcodes</h3>

        <div style="margin: 20px 0; padding: 20px; background: #f8f9fa; border: 1px solid #e0e0e0; border-radius: 8px;">
          <h4 style="color: var(--color-body-text); margin: 0 0 15px;">Role-Based Content</h4>
          <p style="color: #6b7280; margin: 0 0 15px;">Display different content based on user roles with support for multiple conditions.</p>

          <div style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 6px; font-family: monospace; font-size: 14px; margin-bottom: 15px; overflow-x: auto;">
<pre style="margin: 0; white-space: pre-wrap;">[if_role role=editor]
  You are an editor! Welcome to the admin area.
[elseif role=author]
  You are an author! You can create and edit posts.
[elseif role=subscriber]
  You are a subscriber! Thanks for joining us.
[else]
  Please log in to access member content.
[/if_role]</pre>
          </div>

          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[if_role role=editor]You are an editor! Welcome to the admin area.[elseif role=author]You are an author! You can create and edit posts.[elseif role=subscriber]You are a subscriber! Thanks for joining us.[else]Please log in to access member content.[/if_role]'); ?>
          </div>
        </div>

      </section>





      <!-- =================================================================
           AVATARS
           ================================================================= -->
      <section class="style-guide-section">
        <h2>Avatars</h2>

        <h3>Avatar Sizes</h3>
        <div style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
          <?php
          $current_user = wp_get_current_user();
          $user_id = $current_user->ID ?: 1; // Fallback to user ID 1
          ?>
          <div style="text-align: center;">
            <?php echo get_avatar($user_id, 16); ?>
            <div style="font-size: 12px; margin-top: 5px;">16px</div>
          </div>
          <div style="text-align: center;">
            <?php echo get_avatar($user_id, 24); ?>
            <div style="font-size: 12px; margin-top: 5px;">24px</div>
          </div>
          <div style="text-align: center;">
            <?php echo get_avatar($user_id, 32); ?>
            <div style="font-size: 12px; margin-top: 5px;">32px</div>
          </div>
          <div style="text-align: center;">
            <?php echo get_avatar($user_id, 48); ?>
            <div style="font-size: 12px; margin-top: 5px;">48px</div>
          </div>
          <div style="text-align: center;">
            <?php echo get_avatar($user_id, 64); ?>
            <div style="font-size: 12px; margin-top: 5px;">64px</div>
          </div>
          <div style="text-align: center;">
            <?php echo get_avatar($user_id, 96); ?>
            <div style="font-size: 12px; margin-top: 5px;">96px</div>
          </div>
          <div style="text-align: center;">
            <?php echo get_avatar($user_id, 128); ?>
            <div style="font-size: 12px; margin-top: 5px;">128px</div>
          </div>
        </div>


      </section>





      <!-- =================================================================
           VIDEO AND AUDIO PLAYERS
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Video and Audio Players</h2>

        <h3 style="color: var(--color-body-text);">Baum Video Player</h3>
        <div style="max-width: 600px; margin: 20px 0;">
          <div class="baum-featured baum-video-container">
            <figure class="custom-video-player" style="background: var(--color-black); border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
              <video class="video" controls preload="metadata" style="width: 100%; display: block;">
                <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                <p>Your browser does not support the video tag.</p>
              </video>
              <div class="custom-controls" style="display: grid; grid-template-columns: 42px 42px 128px 128px 1fr; align-items: center; gap: 10px; padding: 12px; background: var(--color-black); color: var(--color-white);">
                <button class="play-pause video-control-btn" style="background: var(--color-secondary); border: none; border-radius: var(--border-radius); color: var(--color-white); width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease;">
                  <i class="fa-solid fa-play fa-fw"></i>
                </button>
                <button class="mute-unmute video-control-btn" style="background: var(--color-tertiary); border: none; border-radius: var(--border-radius); color: var(--color-white); width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease;">
                  <i class="fa-solid fa-volume-high fa-fw"></i>
                </button>
                <input type="range" class="volume-slider" min="0" max="1" step="0.01" value="1" style="width: 100%; height: 6px; background: var(--color-gray); border-radius: var(--border-radius); outline: none; cursor: pointer;">
                <input type="range" class="progress-bar" min="0" max="100" value="0" style="width: 100%; height: 6px; background: var(--color-gray); border-radius: var(--border-radius); outline: none; cursor: pointer;">
                <div style="display: flex; align-items: center; justify-content: space-between; gap: 10px;">
                  <span class="time-display" style="font-size: 12px; color: var(--color-white); min-width: 80px;">0:00 / 0:00</span>
                  <button class="fullscreen-toggle video-control-btn" style="background: var(--color-quaternary); border: none; border-radius: var(--border-radius); color: var(--color-white); width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease;">
                    <i class="fa-solid fa-expand fa-fw"></i>
                  </button>
                </div>
              </div>
            </figure>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Baum Audio Player</h3>
        <div style="max-width: 600px; margin: 20px 0;">
          <div class="baum-audio-player" style="background: var(--color-background); border: 1px solid var(--color-gray); border-radius: var(--border-radius); padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <div class="audio-info" style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
              <div class="audio-artwork" style="width: 60px; height: 60px; background: var(--color-tertiary); border-radius: var(--border-radius); display: flex; align-items: center; justify-content: center; color: var(--color-white); font-size: 24px;">
                <i class="fa-solid fa-music"></i>
              </div>
              <div class="audio-details">
                <div style="font-weight: 600; color: var(--color-text); margin-bottom: 4px;">Sample Audio Track</div>
                <div style="font-size: 14px; color: var(--color-gray);">Artist Name - Album Name</div>
              </div>
            </div>

            <div class="audio-controls" style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
              <button class="audio-play-btn" style="background: var(--color-secondary); border: none; border-radius: 50%; color: var(--color-white); width: 48px; height: 48px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 18px; transition: all 0.2s ease;">
                <i class="fa-solid fa-play fa-fw"></i>
              </button>
              <div class="audio-progress" style="flex: 1; display: flex; align-items: center; gap: 10px;">
                <span class="current-time" style="font-size: 12px; color: var(--color-gray); min-width: 35px;">0:00</span>
                <input type="range" class="audio-progress-bar" min="0" max="100" value="0" style="flex: 1; height: 6px; background: var(--color-gray); border-radius: var(--border-radius); outline: none; cursor: pointer;">
                <span class="total-time" style="font-size: 12px; color: var(--color-gray); min-width: 35px;">0:00</span>
              </div>
              <button class="audio-volume-btn" style="background: var(--color-quaternary); border: none; border-radius: var(--border-radius); color: var(--color-white); width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease;">
                <i class="fa-solid fa-volume-high fa-fw"></i>
              </button>
            </div>

            <audio class="audio-element" style="display: none;">
              <source src="https://www.w3schools.com/html/horse.ogg" type="audio/ogg">
              <source src="https://www.w3schools.com/html/horse.mp3" type="audio/mpeg">
              Your browser does not support the audio element.
            </audio>

            <div class="audio-volume-control" style="display: flex; align-items: center; gap: 10px; opacity: 0.7;">
              <i class="fa-solid fa-volume-low" style="color: var(--color-gray); font-size: 14px;"></i>
              <input type="range" class="audio-volume-slider" min="0" max="1" step="0.01" value="1" style="flex: 1; height: 4px; background: var(--color-gray); border-radius: var(--border-radius); outline: none; cursor: pointer;">
              <i class="fa-solid fa-volume-high" style="color: var(--color-gray); font-size: 14px;"></i>
            </div>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Compact Audio Player</h3>
        <div style="max-width: 400px; margin: 20px 0;">
          <div class="baum-audio-compact" style="background: var(--color-background); border: 1px solid var(--color-gray); border-radius: var(--border-radius); padding: 15px; display: flex; align-items: center; gap: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <button class="compact-play-btn" style="background: var(--color-tertiary); border: none; border-radius: 50%; color: var(--color-white); width: 40px; height: 40px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 14px; transition: all 0.2s ease;">
              <i class="fa-solid fa-play fa-fw"></i>
            </button>
            <div style="flex: 1;">
              <div style="font-weight: 600; font-size: 14px; color: var(--color-text); margin-bottom: 2px;">Podcast Episode</div>
              <div style="font-size: 12px; color: var(--color-gray);">2:34 / 15:42</div>
            </div>
            <button style="background: none; border: none; color: var(--color-gray); cursor: pointer; font-size: 16px;">
              <i class="fa-solid fa-ellipsis-vertical"></i>
            </button>
          </div>
        </div>

        <h3 style="color: var(--color-body-text);">Media Playlist</h3>
        <div style="max-width: 600px; margin: 20px 0;">
          <p style="color: #6b7280; margin: 0 0 10px;"><strong>Usage:</strong> <code>[baum_media_playlist type="video" id="7479"]</code></p>
          <div style="background: white; padding: 15px; border-radius: 6px; border: 1px solid #ddd;">
            <?php echo do_shortcode('[baum_media_playlist type="video" id="7479"]'); ?>
          </div>
        </div>
      </section>

      <!-- =================================================================
           SYNTAX HIGHLIGHTING
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Syntax Highlighting</h2>

        <h3 style="color: var(--color-body-text);">Code Blocks with Copy Buttons</h3>
        <div style="margin: 20px 0;">
          <h4 style="color: var(--color-body-text);">PHP Code:</h4>
          <div style="position: relative;">
            <pre style="padding: 15px; border-radius: var(--border-radius); overflow-x: auto; margin: 0;"><code id="php-code">&lt;?php
function display_stars($rating) {
  $rating = floatval($rating);
  $rating = max(0, min(5, $rating));

  $output = '';
  $full_stars = floor($rating);
  $half_star = ($rating - $full_stars) >= 0.5 ? true : false;

  return $output;
}
?&gt;</code></pre>
            <button class="copy-code-btn" data-target="php-code" style="position: absolute; top: 10px; right: 10px; background: var(--color-primary); color: white; border: none; padding: 5px 10px; border-radius: 5px; font-size: 11px; cursor: pointer; opacity: 0.8; ">
              <i class="fas fa-copy"></i> Copy
            </button>
          </div>

          <h4>JavaScript Code:</h4>
          <div style="position: relative;">
            <pre style="padding: 15px; border-radius: var(--border-radius); overflow-x: auto; margin: 0;"><code id="js-code">function updateDateDisplays() {
  const dateElements = document.querySelectorAll("[data-format]");

  dateElements.forEach(element => {
    const format = element.getAttribute("data-format") || "F j, Y";
    const formattedDate = formatCurrentDate(format);
    element.textContent = formattedDate;
  });
}</code></pre>
            <button class="copy-code-btn" data-target="js-code" style="position: absolute; top: 10px; right: 10px; background: var(--color-secondary); color: white; border: none; padding: 5px 10px; border-radius: 5px; font-size: 11px; cursor: pointer; opacity: 0.8; ">
              <i class="fas fa-copy"></i> Copy
            </button>
          </div>

          <h4>CSS Code:</h4>
          <div style="position: relative;">
            <pre style="padding: 15px; border-radius: var(--border-radius); overflow-x: auto; margin: 0;"><code id="css-code">.baum-card {
  background: var(--color-body);
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}</code></pre>
            <button class="copy-code-btn" data-target="css-code" style="position: absolute; top: 10px; right: 10px; background: var(--color-accent); color: white; border: none; padding: 5px 10px; border-radius: 5px; font-size: 11px; cursor: pointer; opacity: 0.8; ">
              <i class="fas fa-copy"></i> Copy
            </button>
          </div>
        </div>
      </section>

      <!-- =================================================================
           NEWSLETTER AND EMAIL
           ================================================================= -->
      <section class="style-guide-section">
        <h2>Newsletter and Email</h2>

        <h3>Newsletter Signup</h3>
        <div style="margin: 20px 0;">
          <?php if (shortcode_exists('newsletter_form')) : ?>
            <?php echo do_shortcode('[newsletter_form]'); ?>
          <?php endif; ?>
        </div>

        <h3>Email List Management</h3>
        <div style="margin: 20px 0;">
          <?php if (shortcode_exists('baum_email_lists')) : ?>
            <?php echo do_shortcode('[baum_email_lists]'); ?>
          <?php endif; ?>
        </div>

        <h3>Sample Email Links</h3>
        <div style="margin: 20px 0;">
          <div style="background: #f8f9fa; padding: 20px; border-radius: var(--border-radius);">
            <h4>📬 Email Templates</h4>
            <ul>
              <li><a href="<?php echo home_url('/email'); ?>" target="_blank">Newsletter Template</a></li>
              <li><a href="<?php echo home_url('/my-feed/email'); ?>" target="_blank">Weekly Digest Template</a></li>
              <li><a href="<?php echo home_url('/category/politics/email'); ?>" target="_blank">Breaking News Alert</a></li>
            </ul>
          </div>
        </div>
      </section>

      <!-- =================================================================
           IMAGE GALLERY WITH POPUP
           ================================================================= -->
      <section class="style-guide-section">
        <h2>Image Gallery with Popup</h2>

        <h3>Gallery Grid</h3>
        <div style="margin: 20px 0;">
          <div class="image-gallery" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div class="gallery-item" style="cursor: pointer; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" onclick="openImagePopup('https://picsum.photos/800/600?random=1', 'Sample Image 1')">
              <img src="https://picsum.photos/300/200?random=1" alt="Sample Image 1" style="width: 100%; height: 200px; object-fit: cover; display: block;">
            </div>
            <div class="gallery-item" style="cursor: pointer; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" onclick="openImagePopup('https://picsum.photos/800/600?random=2', 'Sample Image 2')">
              <img src="https://picsum.photos/300/200?random=2" alt="Sample Image 2" style="width: 100%; height: 200px; object-fit: cover; display: block;">
            </div>
            <div class="gallery-item" style="cursor: pointer; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" onclick="openImagePopup('https://picsum.photos/800/600?random=3', 'Sample Image 3')">
              <img src="https://picsum.photos/300/200?random=3" alt="Sample Image 3" style="width: 100%; height: 200px; object-fit: cover; display: block;">
            </div>
            <div class="gallery-item" style="cursor: pointer; border-radius: var(--border-radius); overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" onclick="openImagePopup('https://picsum.photos/800/600?random=4', 'Sample Image 4')">
              <img src="https://picsum.photos/300/200?random=4" alt="Sample Image 4" style="width: 100%; height: 200px; object-fit: cover; display: block;">
            </div>
          </div>
        </div>

        <!-- Image Popup Modal -->
        <div id="imagePopup" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); z-index: 9999; justify-content: center; align-items: center;">
          <div style="position: relative; max-width: 90%; max-height: 90%;">
            <img id="popupImage" src="" alt="" style="max-width: 100%; max-height: 100%; border-radius: var(--border-radius);">
            <button onclick="closeImagePopup()" style="position: absolute; top: -40px; right: 0; background: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; font-size: 18px;">×</button>
            <div id="popupCaption" style="color: white; text-align: center; margin-top: 10px; font-size: 16px;"></div>
          </div>
        </div>

        <script>
        function openImagePopup(src, caption) {
          document.getElementById('popupImage').src = src;
          document.getElementById('popupCaption').textContent = caption;
          document.getElementById('imagePopup').style.display = 'flex';
          document.body.style.overflow = 'hidden';
        }

        function closeImagePopup() {
          document.getElementById('imagePopup').style.display = 'none';
          document.body.style.overflow = 'auto';
        }

        // Close popup when clicking outside the image
        document.getElementById('imagePopup').addEventListener('click', function(e) {
          if (e.target === this) {
            closeImagePopup();
          }
        });

        // Close popup with Escape key
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape') {
            closeImagePopup();
          }
        });
        </script>

        <script>
        // Credit card formatting function
        function formatCreditCard(input) {
          // Remove all non-digit characters
          let value = input.value.replace(/\D/g, '');

          // Add spaces every 4 digits
          value = value.replace(/(\d{4})(?=\d)/g, '$1 ');

          // Update the input value
          input.value = value;
        }
        </script>
      </section>

      <!-- =================================================================
           REVIEW AND COMPARISON COMPONENTS
           ================================================================= -->
      <section class="style-guide-section">
        <h2>Review and Comparison Components</h2>

        <h3>Comparison Table</h3>
        <div style="width: 66.67%; margin: 20px 0; overflow-x: auto;">
          <table style="width: 100%; border-collapse: collapse; border: 1px solid var(--color-secondary); background: white; border-radius: var(--border-radius); overflow: hidden; font-size: 11px;">
            <thead>
              <tr style="background: var(--color-secondary); color: white;">
                <th style="padding: 5px; text-align: left; font-size: 11px;">Feature</th>
                <th style="padding: 5px; text-align: center; font-size: 11px;">Product A</th>
                <th style="padding: 5px; text-align: center; font-size: 11px;">Product B</th>
                <th style="padding: 5px; text-align: center; font-size: 11px;">Product C</th>
              </tr>
            </thead>
            <tbody>
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 5px; font-weight: bold; font-size: 11px;">Price</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">$99</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">$149</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">$199</td>
              </tr>
              <tr style="border-bottom: 1px solid #eee; background: #f8f9fa;">
                <td style="padding: 5px; font-weight: bold; font-size: 11px;">Rating</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">★★★★☆</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">★★★★★</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">★★★☆☆</td>
              </tr>
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 5px; font-weight: bold; font-size: 11px;">Warranty</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">1 Year</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">2 Years</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">3 Years</td>
              </tr>
              <tr style="background: #f8f9fa;">
                <td style="padding: 5px; font-weight: bold; font-size: 11px;">Best For</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">Budget</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">Performance</td>
                <td style="padding: 5px; text-align: center; font-size: 11px;">Premium</td>
              </tr>
            </tbody>
          </table>
        </div>

        <h3>Comparison Template Part</h3>
        <div style="width: 66.67%; margin: 20px 0;">
          <?php
          get_template_part('parts/baum-comparison', null, [
            'item_1_name' => 'iPhone 15 Pro',
            'item_2_name' => 'Samsung Galaxy S24',
            'item_1_description' => 'Apple\'s flagship smartphone with titanium design, A17 Pro chip, and advanced camera system.',
            'item_2_description' => 'Samsung\'s premium Android device with S Pen support, versatile camera setup, and long battery life.',
            'criteria_1_name' => 'Performance',
            'criteria_2_name' => 'Camera Quality',
            'criteria_1_score_for_item_1' => 5,
            'criteria_2_score_for_item_1' => 4,
            'criteria_1_score_for_item_2' => 4,
            'criteria_2_score_for_item_2' => 5,
            'final_verdict' => 'Both phones excel in different areas. The iPhone 15 Pro offers superior performance and build quality, while the Samsung Galaxy S24 provides better camera versatility and battery life. Choose based on your ecosystem preference and specific needs.',
            'call_to_action' => '#'
          ]);
          ?>
        </div>

        <h3>Data Flip Card Component</h3>
        <div style="width: 66.67%; margin: 20px 0;">
          <?php
          get_template_part('parts/baum-flip-card', null, [
            'title' => 'Monthly Sales Data 2024',
            'table_data' => [
              'headers' => ['Month', 'Sales', 'Profit', 'Growth'],
              'rows' => [
                ['January', '$12,500', '$5,200', '+8%'],
                ['February', '$15,800', '$6,900', '+12%'],
                ['March', '$18,200', '$8,100', '+15%'],
                ['April', '$21,600', '$9,800', '+18%'],
                ['May', '$25,400', '$11,200', '+22%'],
                ['June', '$28,900', '$13,500', '+25%']
              ]
            ],
            'chart_data' => [
              'labels' => ['January', 'February', 'March', 'April', 'May', 'June'],
              'datasets' => [[
                'label' => 'Sales ($)',
                'data' => [12500, 15800, 18200, 21600, 25400, 28900],
                'backgroundColor' => 'rgba(54, 162, 235, 0.8)',
                'borderColor' => 'rgba(54, 162, 235, 1)',
                'borderWidth' => 2
              ], [
                'label' => 'Profit ($)',
                'data' => [5200, 6900, 8100, 9800, 11200, 13500],
                'backgroundColor' => 'rgba(255, 99, 132, 0.8)',
                'borderColor' => 'rgba(255, 99, 132, 1)',
                'borderWidth' => 2
              ]]
            ],
            'chart_type' => 'bar',
            'download_url' => '#'
          ]);
          ?>
        </div>
      </section>




      <!-- =================================================================
           TIME CAPSULE COMPONENT
           ================================================================= -->
      <section class="style-guide-section">
        <h2 style="color: var(--color-body-text);">Time Capsule Component</h2>

        <p>The Time Capsule component displays historical events and facts in an engaging purple-themed card format.</p>

        <h3 style="color: var(--color-body-text);">Time Capsule Variations</h3>
        <div class="baum-time-capsule-demo" style="margin: 20px 0;">
          <?php
          // Historical Science Event
          get_template_part('parts/baum-time-capsule', null, [
            'title' => 'SCIENCE MILESTONE',
            'subtitle' => 'From Scientific American Archives',
            'date' => 'July 20, 1969',
            'content' => '<p>Apollo 11 astronauts Neil Armstrong and Buzz Aldrin become the first humans to walk on the Moon, marking a historic achievement in space exploration.</p>',
            'button_text' => 'DISCOVER MORE SCIENCE',
            'css_class' => 'animate-in'
          ]);

          // Cultural Event
          get_template_part('parts/baum-time-capsule', null, [
            'title' => 'CULTURAL MOMENT',
            'subtitle' => 'From History Today',
            'date' => 'August 15, 1969',
            'content' => '<p>The Woodstock Music Festival begins in upstate New York, featuring legendary performances and becoming a defining moment of the 1960s counterculture movement.</p>',
            'button_text' => 'EXPLORE CULTURE',
            'show_icon' => false
          ]);
          ?>
        </div>

        <h3 style="color: var(--color-body-text);">Custom Content Example</h3>
        <div style="margin: 20px 0;">
          <?php
          get_template_part('parts/baum-time-capsule', null, [
            'title' => 'TECH BREAKTHROUGH',
            'subtitle' => 'From Computer History Museum',
            'date' => 'April 30, 1993',
            'content' => '<p>CERN announces that the World Wide Web will be free for everyone to use, revolutionizing global communication and information sharing forever.</p><p>This decision by Tim Berners-Lee and CERN laid the foundation for the modern internet as we know it today.</p>',
            'button_text' => 'NEXT TECH MILESTONE',
            'source' => 'Computer History Museum',
            'data_attrs' => [
              'category' => 'technology',
              'decade' => '1990s'
            ]
          ]);
          ?>
        </div>
      </section>




      <!-- Enhanced API Responses Section -->
      <section style="margin: 60px 0 40px;">
        <h2 style="color: var(--color-body-text); margin-bottom: 30px;">Enhanced API Responses</h2>
        <div style="max-width: 600px; margin: 20px 0;">
          <div class="api-responses-enhanced" style="background: #f8f9fa; border-radius: 12px; padding: 24px;">
            <h4 style="color: #666; font-size: 14px; font-weight: 600; margin: 0 0 20px 0; text-transform: uppercase; letter-spacing: 0.5px;">HTTP STATUS CODES</h4>

            <div style="display: flex; flex-direction: column; gap: 12px;">
              <!-- 200 OK -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="width: 8px; height: 8px; background: #34c759; border-radius: 50%;"></div>
                    <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">200</div>
                    <div style="color: #666; font-size: 16px;">OK</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">The request was successful and the server returned the requested data.</p>
                </div>
              </div>

              <!-- 400 Bad Request -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                      <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">400</div>
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.4;">One of the request parameters is invalid. See the returned message for details.</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; margin-top: 4px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">Bad Request - The server could not understand the request due to invalid syntax.</p>
                </div>
              </div>

              <!-- 403 Forbidden -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                      <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">403</div>
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.4;">Authentication headers are missing or invalid. Make sure you authenticate your request with a valid API key.</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; margin-top: 4px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">Forbidden - The client does not have access rights to the content.</p>
                </div>
              </div>

              <!-- 429 Rate Limit -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                      <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">429</div>
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.4;">Too many requests. You hit the rate limit. Use the X-RateLimit-... response headers to make sure you're under the rate limit.</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; margin-top: 4px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">Too Many Requests - The user has sent too many requests in a given amount of time.</p>
                </div>
              </div>

              <!-- 500 Internal Server Error -->
              <div class="api-response-enhanced" style="background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s ease;" onclick="this.querySelector('.response-details').style.display = this.querySelector('.response-details').style.display === 'none' ? 'block' : 'none'">
                <div style="display: flex; align-items: flex-start; justify-content: space-between;">
                  <div style="flex: 1;">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                      <div style="width: 8px; height: 8px; background: #ff3b30; border-radius: 50%;"></div>
                      <div style="color: var(--color-body-text); font-size: 18px; font-weight: 600;">500</div>
                    </div>
                    <div style="color: #666; font-size: 14px; line-height: 1.4;">Internal server error. We recommend retrying these later. If the issue persists, please contact us on Slack or on the Community Forum.</div>
                  </div>
                  <i class="fas fa-chevron-right" style="color: #ccc; font-size: 12px; margin-top: 4px; transition: transform 0.2s ease;"></i>
                </div>
                <div class="response-details" style="display: none; margin-top: 12px; padding-top: 12px; border-top: 1px solid #f0f0f0;">
                  <p style="margin: 0; color: #666; font-size: 14px;">Internal Server Error - The server has encountered a situation it doesn't know how to handle.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


    </div>
  </div>
</div>

<style>
/* Import Portfolio Styles */
@import url('<?php echo get_template_directory_uri(); ?>/css/style-portfolio.css');

.style-guide-section {
  margin-bottom: 60px;
  padding-bottom: 40px;
  border-bottom: 2px solid var(--color-septenary);
}

.style-guide-section:last-child {
  border-bottom: none;
}

.style-guide-section h2 {
  color: var(--color-secondary);
  margin-bottom: 30px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--color-secondary);
}

.style-guide-section h3 {
  margin-top: 30px;
  margin-bottom: 15px;
  color: var(--color-secondary);
}

/* Button Size Variations */
.baum-button-huge {
  padding: 20px 60px;
  font-size: 18px;
  font-weight: 700;
  border-radius: var(--border-radius);
  border: none;
  background: var(--color-secondary);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}



.baum-button-large {
  padding: 14px 45px;
  font-size: 14px;
  font-weight: 700;
  border-radius: var(--border-radius);
  border: none;
  background: var(--color-secondary);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.baum-button-medium {
  padding: 12px 40px;
  font-size: 13px;
  font-weight: 700;
  border-radius: var(--border-radius);
  border: none;
  background: var(--color-secondary);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.baum-button {
  padding: 8px 30px;
  font-size: 12px;
  font-weight: 700;
  border-radius: var(--border-radius);
  border: none;
  background: var(--color-secondary);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.baum-button-small {
  padding: 6px 24px;
  font-size: 11px;
  font-weight: 700;
  border-radius: var(--border-radius);
  border: none;
  background: var(--color-secondary);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.baum-button-small:hover {
  background: var(--color-tertiary);
}



.baum-button-micro {
  padding: 2px 12px;
  font-size: 9px;
  font-weight: 700;
  border-radius: var(--border-radius);
  border: none;
  background: var(--color-secondary);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  line-height: 1.2;
}

/* Button Color Variations */
.baum-button-secondary {
  background: var(--color-secondary);
  color: white;
}

.baum-button-success {
  background: var(--color-green);
  color: white;
}

.baum-button-warning {
  background: var(--color-orange);
  color: white;
}

.baum-button-danger {
  background: var(--color-red);
  color: white;
}

/* Badge Styles */
.baum-badge {
  display: inline-block;
  padding: 3px 10px;
  font-size: 8px;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 10px;
  background: var(--color-gray);
  color: white;
  letter-spacing: 0.5px;
}

.baum-badge-primary {
  background: var(--color-secondary);
}

.baum-badge-success {
  background: var(--color-green);
}

.baum-badge-warning {
  background: var(--color-orange);
  color: white;
}

.baum-badge-danger {
  background: var(--color-red);
}

/* Hover Effects for All Buttons */
.baum-button-huge:hover, .baum-button-large:hover,
.baum-button-medium:hover, .baum-button:hover, .baum-button-small:hover,
.baum-button-micro:hover, .baum-button-secondary:hover,
.baum-button-success:hover, .baum-button-warning:hover, .baum-button-danger:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Form Input Improvements */
input[type="text"], input[type="email"], input[type="password"],
input[type="url"], input[type="search"], input[type="number"],
input[type="date"], input[type="datetime-local"], input[type="time"],
textarea, select {
  min-width: 200px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: border-color 0.3s ease;
}

input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus,
input[type="url"]:focus, input[type="search"]:focus, input[type="number"]:focus,
input[type="date"]:focus, input[type="datetime-local"]:focus, input[type="time"]:focus,
textarea:focus, select:focus {
  outline: none;
  border-color: var(--color-secondary);
  box-shadow: 0 0 2.5px 5px rgba(170, 170, 170, 0.2);
}

/* List Styling - Gray bullets and numbers */
ul {
  color: #666;
}

ul li {
  color: var(--color-body-text);
}

ol {
  color: #666;
}

ol li {
  color: var(--color-body-text);
}

/* Gallery Hover Effects */
.gallery-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.2) !important;
  transition: all 0.3s ease;
}

/* Floating Label Styles */
.floating-label-container {
  position: relative;
}

.floating-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #aaa;
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.floating-label {
  position: absolute;
  left: 6px;
  top: 6px;
  font-size: 14px;
  color: #999;
  transition: all 0.3s ease;
  pointer-events: none;
  background: transparent;
  padding: 0 4px;
  transform-origin: left top;
}

.floating-input:focus + .floating-label,
.floating-input:not(:placeholder-shown) + .floating-label {
top: 35px;
left: 7.5px;
font-size: 11px;
color: var(--color-secondary);
font-weight: 600;
transform: scale(1);
}

/* .floating-input:focus + .floating-label,
.floating-input:not(:placeholder-shown) + .floating-label {
  top: 0px;
  left: 5px;
  font-size: 11px;
  color: var(--color-primary);
  font-weight: 600;
  transform: scale(1);
} */

.floating-input:focus {
  outline: none;
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 2px rgba(21, 105, 243, 0.2);
}

/* Custom Checkbox Styles */
.custom-checkbox {
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  position: relative;
  margin-right: 8px;
  transition: all 0.2s ease;
}

.custom-checkbox:checked {
  background: var(--color-secondary);
  border-color: var(--color-secondary);
}

.custom-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.custom-checkbox:hover {
  border-color: var(--color-secondary);
}

.custom-checkbox-label {
  cursor: pointer;
  font-size: 14px;
  user-select: none;
}

/* Custom Radio Styles (Checkbox-like) */
.custom-radio {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 3px solid #ddd;
  border-radius: 4px; /* Square like checkbox */
  background: white;
  cursor: pointer;
  position: relative;
  margin-right: 10px;
  transition: all 0.2s ease;
  vertical-align: middle;
}

.custom-radio:checked {
  background: var(--color-secondary);
  border-color: var(--color-secondary);
  border-width: 3px;
}

.custom-radio:checked::after {
  content: '●';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.custom-radio:hover {
  border-color: var(--color-secondary);
  border-width: 3px;
}

.custom-radio-label {
  cursor: pointer;
  font-size: 14px;
  user-select: none;
}

/* Dropdown Hover Effects */
.dropdown-option:hover {
  background: #f0f8ff;
  color: var(--color-secondary);
}

/* File Drop Area Hover Effects */
.file-drop-area:hover {
  border-color: var(--color-secondary);
  background: #f8f9fa;
}

.file-drop-area.dragover {
  border-color: var(--color-secondary);
  background: #f0f8ff;
  transform: scale(1.02);
}

/* Review Card Styles */
.baum-review-card {
  background: white;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.baum-review-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.baum-review-card .review-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.baum-review-card .review-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  object-fit: cover;
}

.baum-review-card .review-info h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-secondary);
}

.baum-review-card .review-info .review-date {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.baum-review-card .review-rating {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.baum-review-card .review-stars {
  color: var(--color-accent);
  font-size: 18px;
  margin-right: 8px;
}

.baum-review-card .review-score {
  font-weight: 600;
  color: var(--color-primary);
}

.baum-review-card .review-content {
  line-height: 1.6;
  color: var(--color-secondary);
}

.baum-review-card .review-content p {
  margin: 0 0 15px 0;
}

.baum-review-card .review-content p:last-child {
  margin-bottom: 0;
}

.baum-review-card .review-pros-cons {
  margin-top: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.baum-review-card .review-pros,
.baum-review-card .review-cons {
  padding: 15px;
  border-radius: var(--border-radius);
}

.baum-review-card .review-pros {
  background: #f0f9ff;
  border-left: 4px solid var(--color-green);
}

.baum-review-card .review-cons {
  background: #fef2f2;
  border-left: 4px solid var(--color-red);
}

.baum-review-card .review-pros h5,
.baum-review-card .review-cons h5 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
}

.baum-review-card .review-pros h5 {
  color: var(--color-green);
}

.baum-review-card .review-cons h5 {
  color: var(--color-red);
}

.baum-review-card .review-pros ul,
.baum-review-card .review-cons ul {
  margin: 0;
  padding-left: 20px;
  font-size: 14px;
}

.baum-review-card .review-pros li,
.baum-review-card .review-cons li {
  margin-bottom: 5px;
}

.baum-review-card .review-footer {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.baum-review-card .review-helpful {
  font-size: 12px;
  color: #666;
}

.baum-review-card .review-helpful button {
  background: none;
  border: 1px solid #ddd;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 5px;
  font-size: 11px;
}

.baum-review-card .review-helpful button:hover {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.baum-review-card .review-verified {
  background: var(--color-green);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

/* Audio/Video Player Styles */
.video-control-btn:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.audio-play-btn:hover, .compact-play-btn:hover {
  opacity: 0.9;
  transform: scale(1.05);
}

.audio-volume-btn:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

/* Range Slider Styling for Audio/Video */
input[type="range"].volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--color-tertiary);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input[type="range"].progress-bar::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--color-secondary);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input[type="range"].audio-progress-bar::-webkit-slider-thumb {
  appearance: none;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: var(--color-secondary);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input[type="range"].audio-volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--color-quaternary);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Credit Card Form Styles */
.amount-btn:hover, .amount-btn.selected {
  border-color: var(--color-secondary);
  background: var(--color-secondary);
  color: var(--color-white);
}

.radio-option:hover, .radio-option:has(input:checked) {
  border-color: var(--color-secondary);
  background: rgba(var(--color-secondary-rgb), 0.05);
}

.plan-option:hover, .plan-option:has(input:checked) {
  border-color: var(--color-secondary);
  background: rgba(var(--color-secondary-rgb), 0.05);
}

.donate-btn:hover, .subscribe-btn:hover {
  background: var(--color-tertiary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Search Modal Styles */
.search-modal {
  animation: fadeIn 0.2s ease-out;
}

.search-modal-content {
  animation: slideDown 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.suggestion-item:hover, .suggestion-item.selected {
  background: var(--color-background);
  border: 1px solid var(--color-gray);
}

.search-input:focus {
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(var(--color-secondary-rgb), 0.1);
}

/* Card Input Formatting */
#card-number, #sub-card-number {
  letter-spacing: 2px;
}

#card-expiry, #sub-card-expiry, #card-cvc, #sub-card-cvc {
  text-align: center;
}

/* Focus States */
.baum-checkout-form input:focus,
.baum-subscription-form input:focus {
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(var(--color-secondary-rgb), 0.1);
}

/* Organization Creation Form Styles */
.logo-upload-area:hover,
.logo-upload-area-standalone:hover {
  border-color: var(--color-secondary);
  background: rgba(var(--color-secondary-rgb), 0.05);
}

.logo-upload-area.has-image,
.logo-upload-area-standalone.has-image {
  border-style: solid;
  border-color: var(--color-secondary);
}

.upload-btn:hover,
.upload-btn-standalone:hover {
  border-color: var(--color-secondary);
  color: var(--color-secondary);
  background: rgba(var(--color-secondary-rgb), 0.05);
}

.create-org-btn:hover,
.create-org-btn-standalone:hover {
  background: #1a1a1a;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

#org-name:focus,
#org-slug:focus,
#org-name-standalone:focus,
#org-slug-standalone:focus {
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(var(--color-secondary-rgb), 0.1);
  outline: none;
}

#close-org-modal:hover {
  background: #f0f0f0;
  color: #666;
}

.org-creation-form {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo preview styles */
.logo-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: calc(var(--border-radius) - 2px);
}

.upload-placeholder.hidden {
  display: none;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Audio Player Functionality
  const audioPlayers = document.querySelectorAll('.baum-audio-player');

  audioPlayers.forEach(player => {
    const audio = player.querySelector('.audio-element');
    const playBtn = player.querySelector('.audio-play-btn');
    const progressBar = player.querySelector('.audio-progress-bar');
    const volumeBtn = player.querySelector('.audio-volume-btn');
    const volumeSlider = player.querySelector('.audio-volume-slider');
    const currentTime = player.querySelector('.current-time');
    const totalTime = player.querySelector('.total-time');

    if (!audio || !playBtn) return;

    let isPlaying = false;

    // Play/Pause functionality
    playBtn.addEventListener('click', () => {
      if (isPlaying) {
        audio.pause();
        playBtn.innerHTML = '<i class="fa-solid fa-play fa-fw"></i>';
        isPlaying = false;
      } else {
        audio.play();
        playBtn.innerHTML = '<i class="fa-solid fa-pause fa-fw"></i>';
        isPlaying = true;
      }
    });

    // Update progress and time
    audio.addEventListener('timeupdate', () => {
      if (audio.duration) {
        const progress = (audio.currentTime / audio.duration) * 100;
        progressBar.value = progress;

        if (currentTime) currentTime.textContent = formatTime(audio.currentTime);
        if (totalTime) totalTime.textContent = formatTime(audio.duration);
      }
    });

    // Seek functionality
    if (progressBar) {
      progressBar.addEventListener('input', () => {
        const seekTime = (progressBar.value / 100) * audio.duration;
        audio.currentTime = seekTime;
      });
    }

    // Volume control
    if (volumeBtn) {
      volumeBtn.addEventListener('click', () => {
        audio.muted = !audio.muted;
        if (audio.muted) {
          volumeBtn.innerHTML = '<i class="fa-solid fa-volume-xmark fa-fw"></i>';
        } else {
          volumeBtn.innerHTML = '<i class="fa-solid fa-volume-high fa-fw"></i>';
        }
      });
    }

    if (volumeSlider) {
      volumeSlider.addEventListener('input', () => {
        audio.volume = volumeSlider.value;
        if (audio.volume === 0) {
          volumeBtn.innerHTML = '<i class="fa-solid fa-volume-xmark fa-fw"></i>';
        } else if (audio.volume < 0.5) {
          volumeBtn.innerHTML = '<i class="fa-solid fa-volume-low fa-fw"></i>';
        } else {
          volumeBtn.innerHTML = '<i class="fa-solid fa-volume-high fa-fw"></i>';
        }
      });
    }
  });

  // Compact Audio Player
  const compactPlayers = document.querySelectorAll('.baum-audio-compact');

  compactPlayers.forEach(player => {
    const playBtn = player.querySelector('.compact-play-btn');
    let isPlaying = false;

    if (playBtn) {
      playBtn.addEventListener('click', () => {
        if (isPlaying) {
          playBtn.innerHTML = '<i class="fa-solid fa-play fa-fw"></i>';
          isPlaying = false;
        } else {
          playBtn.innerHTML = '<i class="fa-solid fa-pause fa-fw"></i>';
          isPlaying = true;
        }
      });
    }
  });

  // Format time helper function
  function formatTime(seconds) {
    if (isNaN(seconds)) return '0:00';

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // Credit Card Form Functionality
  const amountButtons = document.querySelectorAll('.amount-btn');
  const customAmountInput = document.getElementById('custom-amount');
  const donateBtn = document.querySelector('.donate-btn .btn-text');
  const subscribeBtn = document.querySelector('.subscribe-btn .sub-btn-text');

  // Amount button selection
  amountButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      amountButtons.forEach(b => b.classList.remove('selected'));
      btn.classList.add('selected');

      const amount = btn.dataset.amount;
      customAmountInput.value = amount;
      if (donateBtn) donateBtn.textContent = `Donate $${amount}`;
    });
  });

  // Custom amount input
  if (customAmountInput) {
    customAmountInput.addEventListener('input', () => {
      amountButtons.forEach(b => b.classList.remove('selected'));
      const amount = customAmountInput.value;
      if (donateBtn && amount) {
        donateBtn.textContent = `Donate $${amount}`;
      }
    });
  }

  // Plan selection for subscription
  const planOptions = document.querySelectorAll('input[name="plan"]');
  planOptions.forEach(option => {
    option.addEventListener('change', () => {
      const selectedPlan = document.querySelector('input[name="plan"]:checked');
      if (selectedPlan && subscribeBtn) {
        const planText = selectedPlan.value;
        const planPrices = { basic: '$9/mo', pro: '$19/mo', enterprise: '$49/mo' };
        const planNames = { basic: 'Basic Plan', pro: 'Pro Plan', enterprise: 'Enterprise' };
        subscribeBtn.textContent = `Start ${planNames[planText]} - ${planPrices[planText]}`;
      }
    });
  });

  // Card number formatting
  function formatCardNumber(input) {
    let value = input.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
    input.value = formattedValue;
  }

  // Expiry date formatting
  function formatExpiryDate(input) {
    let value = input.value.replace(/\D/g, '');
    if (value.length >= 2) {
      value = value.substring(0, 2) + '/' + value.substring(2, 4);
    }
    input.value = value;
  }

  // Apply formatting to card inputs
  const cardNumberInputs = document.querySelectorAll('#card-number, #sub-card-number');
  const expiryInputs = document.querySelectorAll('#card-expiry, #sub-card-expiry');

  cardNumberInputs.forEach(input => {
    input.addEventListener('input', () => formatCardNumber(input));
  });

  expiryInputs.forEach(input => {
    input.addEventListener('input', () => formatExpiryDate(input));
  });

  // Search Modal Functionality
  const openSearchBtn = document.getElementById('open-search');
  const closeSearchBtn = document.getElementById('close-search');
  const searchModal = document.getElementById('search-modal');
  const searchInput = document.getElementById('search-input');
  const searchSuggestions = document.querySelector('.search-suggestions');
  const searchContent = document.querySelector('.search-content');
  const noResults = document.querySelector('.no-results');
  const suggestionItems = document.querySelectorAll('.suggestion-item');

  let selectedIndex = 0;

  // Open search modal
  function openSearch() {
    searchModal.style.display = 'block';
    setTimeout(() => searchInput.focus(), 100);
    selectedIndex = 0;
    updateSelection();
  }

  // Close search modal
  function closeSearch() {
    searchModal.style.display = 'none';
    searchInput.value = '';
    searchSuggestions.style.display = 'block';
    searchContent.style.display = 'none';
    noResults.style.display = 'none';
  }

  // Update selection highlighting
  function updateSelection() {
    suggestionItems.forEach((item, index) => {
      item.classList.toggle('selected', index === selectedIndex);
    });
  }

  // Execute selected action
  function executeAction() {
    const selectedItem = suggestionItems[selectedIndex];
    if (selectedItem) {
      const action = selectedItem.dataset.action;
      console.log(`Executing action: ${action}`);
      closeSearch();
    }
  }

  // Event listeners
  if (openSearchBtn) {
    openSearchBtn.addEventListener('click', openSearch);
  }

  if (closeSearchBtn) {
    closeSearchBtn.addEventListener('click', closeSearch);
  }

  // Click outside to close
  if (searchModal) {
    searchModal.addEventListener('click', (e) => {
      if (e.target === searchModal) {
        closeSearch();
      }
    });
  }

  // Search input functionality
  if (searchInput) {
    searchInput.addEventListener('input', (e) => {
      const query = e.target.value.trim();

      if (query.length === 0) {
        searchSuggestions.style.display = 'block';
        searchContent.style.display = 'none';
        noResults.style.display = 'none';
      } else if (query.length >= 2) {
        // Simulate search results
        searchSuggestions.style.display = 'none';
        searchContent.style.display = 'block';
        noResults.style.display = 'none';

        // Mock search results
        const resultsContainer = document.getElementById('search-results-list');
        if (resultsContainer) {
          resultsContainer.innerHTML = `
            <div class="suggestion-item" style="display: flex; align-items: center; padding: 12px; border-radius: var(--border-radius); cursor: pointer; transition: all 0.2s ease; margin-bottom: 4px;">
              <i class="fa-solid fa-file-text" style="width: 20px; color: var(--color-secondary); margin-right: 12px;"></i>
              <div>
                <div style="color: var(--color-body-text); font-weight: 600;">Search result for "${query}"</div>
                <div style="font-size: 12px; color: var(--color-gray);">This is a sample search result</div>
              </div>
            </div>
          `;
        }
      }
    });
  }

  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    // Ctrl+K to open search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
      e.preventDefault();
      openSearch();
    }

    // Handle modal keyboard navigation
    if (searchModal.style.display === 'block') {
      switch (e.key) {
        case 'Escape':
          closeSearch();
          break;
        case 'ArrowUp':
          e.preventDefault();
          selectedIndex = Math.max(0, selectedIndex - 1);
          updateSelection();
          break;
        case 'ArrowDown':
          e.preventDefault();
          selectedIndex = Math.min(suggestionItems.length - 1, selectedIndex + 1);
          updateSelection();
          break;
        case 'Enter':
          e.preventDefault();
          executeAction();
          break;
      }
    }
  });

  // Click handlers for suggestion items
  suggestionItems.forEach((item, index) => {
    item.addEventListener('click', () => {
      selectedIndex = index;
      executeAction();
    });

    item.addEventListener('mouseenter', () => {
      selectedIndex = index;
      updateSelection();
    });
  });

  // Account Modal Functionality
  const openAccountModalBtn = document.getElementById('open-account-modal');
  const closeAccountModalBtn = document.getElementById('close-account-modal');
  const accountModal = document.getElementById('account-modal');
  const accountNavItems = document.querySelectorAll('.account-nav-item');
  const accountTabContents = document.querySelectorAll('.account-tab-content');

  // Open account modal
  if (openAccountModalBtn) {
    openAccountModalBtn.addEventListener('click', () => {
      accountModal.style.display = 'block';
    });
  }

  // Close account modal
  if (closeAccountModalBtn) {
    closeAccountModalBtn.addEventListener('click', () => {
      accountModal.style.display = 'none';
    });
  }

  // Close modal when clicking outside
  if (accountModal) {
    accountModal.addEventListener('click', (e) => {
      if (e.target === accountModal) {
        accountModal.style.display = 'none';
      }
    });
  }

  // Account navigation tabs
  accountNavItems.forEach(navItem => {
    navItem.addEventListener('click', () => {
      const targetTab = navItem.dataset.tab;

      // Remove active class from all nav items
      accountNavItems.forEach(item => {
        item.classList.remove('active');
        item.style.background = 'transparent';
        item.style.color = '#666';
      });

      // Add active class to clicked nav item
      navItem.classList.add('active');
      navItem.style.background = 'var(--color-secondary)';
      navItem.style.color = 'white';

      // Hide all tab contents
      accountTabContents.forEach(content => {
        content.style.display = 'none';
      });

      // Show target tab content
      const targetContent = document.getElementById(`${targetTab}-tab`);
      if (targetContent) {
        targetContent.style.display = 'block';
      }
    });
  });

  // Account Switcher Dropdown Functionality
  const openAccountSwitcherBtn = document.getElementById('open-account-switcher');
  const accountSwitcherDropdown = document.getElementById('account-switcher-dropdown');

  if (openAccountSwitcherBtn) {
    openAccountSwitcherBtn.addEventListener('click', () => {
      const isVisible = accountSwitcherDropdown.style.display === 'block';
      accountSwitcherDropdown.style.display = isVisible ? 'none' : 'block';
    });
  }

  // Close account switcher when clicking outside
  document.addEventListener('click', (e) => {
    if (accountSwitcherDropdown &&
        !accountSwitcherDropdown.contains(e.target) &&
        !openAccountSwitcherBtn.contains(e.target)) {
      accountSwitcherDropdown.style.display = 'none';
    }
  });

  // API Response Items Click Functionality
  const apiResponseItems = document.querySelectorAll('.api-response-item');

  apiResponseItems.forEach(item => {
    item.addEventListener('click', () => {
      const details = item.querySelector('.response-details');
      const chevron = item.querySelector('.fas.fa-chevron-right');

      if (details) {
        const isVisible = details.style.display === 'block';
        details.style.display = isVisible ? 'none' : 'block';

        // Rotate chevron
        if (chevron) {
          chevron.style.transform = isVisible ? 'rotate(0deg)' : 'rotate(90deg)';
          chevron.style.transition = 'transform 0.2s ease';
        }
      }
    });
  });

  // Add hover effects for account nav items
  accountNavItems.forEach(navItem => {
    if (!navItem.classList.contains('active')) {
      navItem.addEventListener('mouseenter', () => {
        if (!navItem.classList.contains('active')) {
          navItem.style.background = '#f0f0f0';
        }
      });

      navItem.addEventListener('mouseleave', () => {
        if (!navItem.classList.contains('active')) {
          navItem.style.background = 'transparent';
        }
      });
    }
  });

  // Organization Creation Form Functionality
  const openOrgModalBtn = document.getElementById('open-org-modal');
  const closeOrgModalBtn = document.getElementById('close-org-modal');
  const orgModal = document.getElementById('org-modal');
  const logoUpload = document.getElementById('logo-upload');
  const logoUploadStandalone = document.getElementById('logo-upload-standalone');
  const uploadBtn = document.querySelector('.upload-btn');
  const uploadBtnStandalone = document.querySelector('.upload-btn-standalone');
  const orgNameInput = document.getElementById('org-name');
  const orgSlugInput = document.getElementById('org-slug');
  const orgNameStandaloneInput = document.getElementById('org-name-standalone');
  const orgSlugStandaloneInput = document.getElementById('org-slug-standalone');

  // Open organization modal
  if (openOrgModalBtn) {
    openOrgModalBtn.addEventListener('click', () => {
      orgModal.style.display = 'block';
    });
  }

  // Close organization modal
  if (closeOrgModalBtn) {
    closeOrgModalBtn.addEventListener('click', () => {
      orgModal.style.display = 'none';
    });
  }

  // Close modal when clicking outside
  if (orgModal) {
    orgModal.addEventListener('click', (e) => {
      if (e.target === orgModal) {
        orgModal.style.display = 'none';
      }
    });
  }

  // Logo upload functionality
  function setupLogoUpload(fileInput, uploadButton, uploadArea) {
    if (!fileInput || !uploadArea) return;

    // Handle file input change
    fileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        handleLogoUpload(file, uploadArea);
      }
    });

    // Handle upload button click
    if (uploadButton) {
      uploadButton.addEventListener('click', () => {
        fileInput.click();
      });
    }

    // Handle drag and drop
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.style.borderColor = 'var(--color-secondary)';
      uploadArea.style.background = 'rgba(var(--color-secondary-rgb), 0.1)';
    });

    uploadArea.addEventListener('dragleave', (e) => {
      e.preventDefault();
      uploadArea.style.borderColor = '#d0d0d0';
      uploadArea.style.background = '#fafafa';
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.style.borderColor = '#d0d0d0';
      uploadArea.style.background = '#fafafa';

      const files = e.dataTransfer.files;
      if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
          handleLogoUpload(file, uploadArea);
          fileInput.files = files;
        }
      }
    });

    // Handle click on upload area
    uploadArea.addEventListener('click', () => {
      fileInput.click();
    });
  }

  // Handle logo upload
  function handleLogoUpload(file, uploadArea) {
    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      const placeholder = uploadArea.querySelector('.upload-placeholder');

      // Hide placeholder
      placeholder.classList.add('hidden');

      // Create or update image preview
      let preview = uploadArea.querySelector('.logo-preview');
      if (!preview) {
        preview = document.createElement('img');
        preview.className = 'logo-preview';
        uploadArea.appendChild(preview);
      }

      preview.src = e.target.result;
      uploadArea.classList.add('has-image');
    };

    reader.readAsDataURL(file);
  }

  // Setup logo upload for both modal and standalone forms
  setupLogoUpload(logoUpload, uploadBtn, document.querySelector('.logo-upload-area'));
  setupLogoUpload(logoUploadStandalone, uploadBtnStandalone, document.querySelector('.logo-upload-area-standalone'));

  // Auto-generate slug from organization name
  function generateSlug(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  }

  // Organization name to slug conversion (modal)
  if (orgNameInput && orgSlugInput) {
    orgNameInput.addEventListener('input', (e) => {
      const name = e.target.value;
      const slug = generateSlug(name);
      orgSlugInput.value = slug;
      orgSlugInput.style.color = slug ? 'var(--color-body-text)' : '#999';
    });
  }

  // Organization name to slug conversion (standalone)
  if (orgNameStandaloneInput && orgSlugStandaloneInput) {
    orgNameStandaloneInput.addEventListener('input', (e) => {
      const name = e.target.value;
      const slug = generateSlug(name);
      orgSlugStandaloneInput.value = slug;
      orgSlugStandaloneInput.style.color = slug ? 'var(--color-body-text)' : '#999';
    });
  }

  // Manual slug editing
  if (orgSlugInput) {
    orgSlugInput.addEventListener('input', (e) => {
      const slug = generateSlug(e.target.value);
      e.target.value = slug;
      e.target.style.color = slug ? 'var(--color-body-text)' : '#999';
    });
  }

  if (orgSlugStandaloneInput) {
    orgSlugStandaloneInput.addEventListener('input', (e) => {
      const slug = generateSlug(e.target.value);
      e.target.value = slug;
      e.target.style.color = slug ? 'var(--color-body-text)' : '#999';
    });
  }

  // Form submission handling
  const createOrgBtn = document.querySelector('.create-org-btn');
  const createOrgBtnStandalone = document.querySelector('.create-org-btn-standalone');

  function handleOrgCreation(formType) {
    const nameInput = formType === 'modal' ? orgNameInput : orgNameStandaloneInput;
    const slugInput = formType === 'modal' ? orgSlugInput : orgSlugStandaloneInput;
    const fileInput = formType === 'modal' ? logoUpload : logoUploadStandalone;

    const name = nameInput?.value.trim();
    const slug = slugInput?.value.trim();
    const logo = fileInput?.files[0];

    // Basic validation
    if (!name) {
      alert('Please enter an organization name');
      nameInput?.focus();
      return;
    }

    if (!slug) {
      alert('Please enter a slug URL');
      slugInput?.focus();
      return;
    }

    // Simulate form submission
    console.log('Creating organization:', {
      name,
      slug,
      logo: logo ? logo.name : 'No logo uploaded',
      formType
    });

    // Show success message
    alert(`Organization "${name}" would be created with slug "${slug}"`);

    // Close modal if it's the modal form
    if (formType === 'modal' && orgModal) {
      orgModal.style.display = 'none';
    }

    // Reset form (optional)
    // resetForm(formType);
  }

  // Attach form submission handlers
  if (createOrgBtn) {
    createOrgBtn.addEventListener('click', (e) => {
      e.preventDefault();
      handleOrgCreation('modal');
    });
  }

  if (createOrgBtnStandalone) {
    createOrgBtnStandalone.addEventListener('click', (e) => {
      e.preventDefault();
      handleOrgCreation('standalone');
    });
  }

  // Reset form function
  function resetForm(formType) {
    const nameInput = formType === 'modal' ? orgNameInput : orgNameStandaloneInput;
    const slugInput = formType === 'modal' ? orgSlugInput : orgSlugStandaloneInput;
    const fileInput = formType === 'modal' ? logoUpload : logoUploadStandalone;
    const uploadArea = formType === 'modal' ?
      document.querySelector('.logo-upload-area') :
      document.querySelector('.logo-upload-area-standalone');

    // Reset inputs
    if (nameInput) nameInput.value = '';
    if (slugInput) {
      slugInput.value = 'my-org';
      slugInput.style.color = '#999';
    }
    if (fileInput) fileInput.value = '';

    // Reset upload area
    if (uploadArea) {
      uploadArea.classList.remove('has-image');
      const placeholder = uploadArea.querySelector('.upload-placeholder');
      const preview = uploadArea.querySelector('.logo-preview');

      if (placeholder) placeholder.classList.remove('hidden');
      if (preview) preview.remove();
    }
  }
});
</script>


<script>
// Frontend Gutenberg Editor Initialization
document.addEventListener('DOMContentLoaded', function() {
  // Check if user has permission
  if (!document.getElementById('frontend-editor-container')) return;

  // Initialize Gutenberg editor
  initializeGutenbergEditor();

  // Setup form handlers
  setupEditorHandlers();
});

function initializeGutenbergEditor() {
  const editorContainer = document.getElementById('gutenberg-editor');
  if (!editorContainer) return;

  // Show loading message while lightweight editor initializes
  editorContainer.innerHTML = `
    <div style="padding: 40px; text-align: center; color: #666;">
      <div style="font-size: 18px; margin-bottom: 10px;">🖋️</div>
      <p style="margin: 0;">Initializing lightweight rich text editor...</p>
    </div>
  `;

  // The lightweight editor initialization happens in frontend-gutenberg.js
  // This placeholder will be replaced with the actual editor
}

function setupEditorHandlers() {
  // Save Draft
  const saveDraftBtn = document.getElementById('save-draft');
  if (saveDraftBtn) {
    saveDraftBtn.addEventListener('click', function() {
      saveArticle('draft');
    });
  }

  // Preview Article
  const previewBtn = document.getElementById('preview-article');
  if (previewBtn) {
    previewBtn.addEventListener('click', function() {
      previewArticle();
    });
  }

  // Publish/Submit for Review
  const publishBtn = document.getElementById('publish-article') || document.getElementById('submit-review');
  if (publishBtn) {
    publishBtn.addEventListener('click', function() {
      const status = document.getElementById('article-status').value;
      saveArticle(status);
    });
  }

  // Auto-save functionality
  let autoSaveTimeout;
  const titleInput = document.getElementById('article-title');
  const contentEditor = document.getElementById('content-editor');

  function triggerAutoSave() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
      if (titleInput.value.trim() || (contentEditor && contentEditor.value.trim())) {
        saveArticle('auto-draft', true);
      }
    }, 30000); // Auto-save every 30 seconds
  }

  if (titleInput) titleInput.addEventListener('input', triggerAutoSave);
  if (contentEditor) contentEditor.addEventListener('input', triggerAutoSave);
}

function saveArticle(status = 'draft', isAutoSave = false) {
  const title = document.getElementById('article-title').value.trim();

  // Get content from Gutenberg editor if available, otherwise fallback to textarea
  let content = '';
  if (window.frontendGutenberg && typeof window.frontendGutenberg.getBlocksContent === 'function') {
    content = window.frontendGutenberg.getBlocksContent();
  } else {
    content = document.getElementById('content-editor')?.value || '';
  }

  // Get selected values from multi-select dropdowns
  const categories = getMultiSelectValues('categories-dropdown');
  const tags = getMultiSelectValues('tags-dropdown');
  const coAuthors = getMultiSelectValues('co-authors-dropdown');

  if (!title && !isAutoSave) {
    alert('Please enter a title for your article.');
    document.getElementById('article-title').focus();
    return;
  }

  // Show saving indicator
  const saveBtn = document.getElementById('save-draft');
  const originalText = saveBtn?.textContent;
  if (saveBtn && !isAutoSave) {
    saveBtn.textContent = '💾 Saving...';
    saveBtn.disabled = true;
  }

  // Prepare article data
  const articleData = {
    title: title || 'Untitled Draft',
    content: content,
    status: status,
    categories: categories,
    tags: tags,
    coAuthors: <AUTHORS>
    isAutoSave: isAutoSave
  };

  // Try to save via Gutenberg API first, then fallback to simulation
  if (window.frontendGutenberg && typeof window.frontendGutenberg.saveArticleViaAPI === 'function') {
    window.frontendGutenberg.saveArticleViaAPI(articleData)
      .then(response => {
        console.log('Article saved successfully:', response);

        if (!isAutoSave) {
          showNotification(`Article ${status === 'draft' ? 'saved as draft' : status === 'publish' ? 'published' : 'submitted for review'}!`, 'success');

          if (saveBtn) {
            saveBtn.textContent = originalText;
            saveBtn.disabled = false;
          }
        } else {
          showNotification('Auto-saved', 'info', 2000);
        }
      })
      .catch(error => {
        console.error('Error saving article:', error);

        // Fallback to simulation if API fails
        console.log('Falling back to simulation mode');
        simulateSave();
      });
  } else {
    // Fallback simulation
    simulateSave();
  }

  function simulateSave() {
    setTimeout(() => {
      console.log('Article data (simulated save):', articleData);

      if (!isAutoSave) {
        showNotification(`Article ${status === 'draft' ? 'saved as draft' : status === 'publish' ? 'published' : 'submitted for review'}! (Demo Mode)`, 'success');

        if (saveBtn) {
          saveBtn.textContent = originalText;
          saveBtn.disabled = false;
        }
      } else {
        showNotification('Auto-saved (Demo)', 'info', 2000);
      }
    }, 1000);
  }
}

function previewArticle() {
  const title = document.getElementById('article-title').value.trim();

  // Get content from Gutenberg editor if available, otherwise fallback to textarea
  let content = '';
  if (window.frontendGutenberg && typeof window.frontendGutenberg.getBlocksContent === 'function') {
    content = window.frontendGutenberg.getBlocksContent();
  } else {
    content = document.getElementById('content-editor')?.value || '';
  }

  if (!title && !content) {
    alert('Please add some content to preview.');
    return;
  }

  // Open preview in new window/tab
  const previewWindow = window.open('', '_blank');
  previewWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Preview: ${title || 'Untitled'}</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 40px 20px; line-height: 1.6; }
        h1 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .preview-notice { background: #f0f8ff; border: 1px solid #b3d9ff; border-radius: 6px; padding: 15px; margin-bottom: 30px; color: #0066cc; }
        /* Basic block styles */
        .wp-block-heading { margin: 1.5em 0 0.5em; }
        .wp-block-paragraph { margin: 1em 0; }
        .wp-block-quote { border-left: 4px solid #ccc; padding-left: 1em; margin: 1.5em 0; font-style: italic; }
        .wp-block-code { background: #f4f4f4; padding: 1em; border-radius: 4px; font-family: monospace; }
        .wp-block-list { margin: 1em 0; padding-left: 2em; }
      </style>
    </head>
    <body>
      <div class="preview-notice">
        <strong>📖 Article Preview</strong><br>
        This is how your article will appear when published.
      </div>
      <h1>${title || 'Untitled Article'}</h1>
      <div class="article-content">${content || content.replace(/\n/g, '<br>')}</div>
    </body>
    </html>
  `);
}

function showNotification(message, type = 'info', duration = 4000) {
  // Create notification element
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transition: all 0.3s ease;
    transform: translateX(100%);
  `;

  // Set background color based on type
  switch (type) {
    case 'success':
      notification.style.background = '#28a745';
      break;
    case 'error':
      notification.style.background = '#dc3545';
      break;
    case 'warning':
      notification.style.background = '#ffc107';
      notification.style.color = '#333';
      break;
    default:
      notification.style.background = '#17a2b8';
  }

  notification.textContent = message;
  document.body.appendChild(notification);

  // Animate in
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);

  // Animate out and remove
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, duration);
}

// Helper function to get selected values from multi-select dropdown
function getMultiSelectValues(dropdownId) {
  const dropdown = document.getElementById(dropdownId);
  if (!dropdown) return [];

  const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');
  return Array.from(checkboxes).map(cb => cb.value);
}

// Multi-select dropdown functionality
function initializeMultiSelectDropdowns() {
  const dropdowns = document.querySelectorAll('.multi-select-dropdown');

  dropdowns.forEach(dropdown => {
    const display = dropdown.querySelector('.multi-select-display');
    const options = dropdown.querySelector('.multi-select-options');
    const placeholder = dropdown.querySelector('.multi-select-placeholder');
    const searchInput = dropdown.querySelector('.multi-select-search-input');
    const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');

    // Toggle dropdown
    display.addEventListener('click', () => {
      const isVisible = options.style.display === 'block';

      // Close all other dropdowns
      document.querySelectorAll('.multi-select-options').forEach(opt => {
        opt.style.display = 'none';
      });

      options.style.display = isVisible ? 'none' : 'block';
      if (!isVisible && searchInput) {
        searchInput.focus();
      }
    });

    // Search functionality
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const optionLabels = dropdown.querySelectorAll('.multi-select-option');

        optionLabels.forEach(label => {
          const text = label.textContent.toLowerCase();
          label.style.display = text.includes(searchTerm) ? 'flex' : 'none';
        });
      });
    }

    // Handle checkbox changes
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        updateMultiSelectDisplay(dropdown);
      });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!dropdown.contains(e.target)) {
        options.style.display = 'none';
      }
    });
  });
}

// Update multi-select display with selected items
function updateMultiSelectDisplay(dropdown) {
  const display = dropdown.querySelector('.multi-select-display');
  const placeholder = dropdown.querySelector('.multi-select-placeholder');
  const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');

  // Remove existing tags
  const existingTags = display.querySelectorAll('.selected-tag');
  existingTags.forEach(tag => tag.remove());

  if (checkboxes.length === 0) {
    placeholder.style.display = 'block';
  } else {
    placeholder.style.display = 'none';

    checkboxes.forEach(checkbox => {
      const label = checkbox.closest('.multi-select-option').querySelector('span').textContent;
      const tag = document.createElement('span');
      tag.className = 'selected-tag';
      tag.style.cssText = `
        background: var(--color-secondary);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
      `;
      tag.innerHTML = `
        ${label}
        <i class="fas fa-times" style="cursor: pointer; font-size: 10px;" onclick="removeMultiSelectTag(this, '${checkbox.value}')"></i>
      `;
      display.insertBefore(tag, placeholder);
    });
  }
}

// Remove tag from multi-select
function removeMultiSelectTag(element, value) {
  const dropdown = element.closest('.multi-select-dropdown');
  const checkbox = dropdown.querySelector(`input[value="${value}"]`);
  if (checkbox) {
    checkbox.checked = false;
    updateMultiSelectDisplay(dropdown);
  }
}

// Featured image upload functionality
function initializeFeaturedImageUpload() {
  const container = document.getElementById('featured-image-container');
  const preview = document.getElementById('featured-image-preview');
  const input = document.getElementById('featured-image-input');
  const removeBtn = document.getElementById('remove-featured-image');

  if (!container || !preview || !input) return;

  // Click to upload
  preview.addEventListener('click', () => {
    input.click();
  });

  // Handle file selection
  input.addEventListener('change', (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        preview.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: var(--border-radius);">`;
        removeBtn.style.display = 'block';
      };
      reader.readAsDataURL(file);
    }
  });

  // Remove image
  if (removeBtn) {
    removeBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      input.value = '';
      preview.innerHTML = `
        <div style="text-align: center; color: #666;">
          <i class="fas fa-image" style="font-size: 20px; margin-bottom: 4px; display: block;"></i>
          <span style="font-size: 12px;">Click to upload</span>
        </div>
      `;
      removeBtn.style.display = 'none';
    });
  }

  // Drag and drop
  preview.addEventListener('dragover', (e) => {
    e.preventDefault();
    preview.style.borderColor = 'var(--color-secondary)';
    preview.style.background = 'rgba(var(--color-secondary-rgb), 0.1)';
  });

  preview.addEventListener('dragleave', (e) => {
    e.preventDefault();
    preview.style.borderColor = '#ddd';
    preview.style.background = '#f8f9fa';
  });

  preview.addEventListener('drop', (e) => {
    e.preventDefault();
    preview.style.borderColor = '#ddd';
    preview.style.background = '#f8f9fa';

    const files = e.dataTransfer.files;
    if (files.length > 0 && files[0].type.startsWith('image/')) {
      input.files = files;
      input.dispatchEvent(new Event('change'));
    }
  });
}

// Auto-growing textarea functionality
function initializeAutoGrowTextarea() {
  const textarea = document.getElementById('article-title');
  if (!textarea) return;

  function adjustHeight() {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 150) + 'px';
  }

  textarea.addEventListener('input', adjustHeight);
  textarea.addEventListener('paste', () => {
    setTimeout(adjustHeight, 0);
  });

  // Initial adjustment
  adjustHeight();
}

// Initialize all frontend editor functionality
document.addEventListener('DOMContentLoaded', function() {
  if (document.getElementById('frontend-editor-container')) {
    initializeMultiSelectDropdowns();
    initializeFeaturedImageUpload();
    initializeAutoGrowTextarea();
  }
});

// Organization Profile Modal functionality
jQuery(document).ready(function($) {
  // Open organization profile modal
  $('#open-org-profile-modal').on('click', function(e) {
    e.preventDefault();
    $('#org-profile-modal').show();
  });

  // Close organization profile modal
  $('#close-org-profile-modal').on('click', function(e) {
    e.preventDefault();
    $('#org-profile-modal').hide();
  });

  // Close modal when clicking on backdrop
  $('#org-profile-modal').on('click', function(e) {
    if (e.target === this) {
      $(this).hide();
    }
  });

  // Close organization creation modal (existing functionality)
  $('#close-org-modal').on('click', function(e) {
    e.preventDefault();
    $('#org-modal').hide();
  });

  // Open organization creation modal
  $('#open-org-modal').on('click', function(e) {
    e.preventDefault();
    $('#org-modal').show();
  });

  // Close org creation modal when clicking on backdrop
  $('#org-modal').on('click', function(e) {
    if (e.target === this) {
      $(this).hide();
    }
  });
});
</script>


<?php get_footer(); ?>
