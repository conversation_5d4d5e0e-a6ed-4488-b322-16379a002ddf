<?php
/**
 * BaumPress Skeleton Loader System
 *
 * Enhanced skeleton loading system for various UI components
 * including category headers, cards, and other content areas.
 *
 * @package BaumPress
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

/**
 * Enqueue skeleton loader styles
 */
function baum_skeleton_styles() {
  ?>
  <style>
    /* ==========================================================================
       BAUM SKELETON LOADER SYSTEM
       ========================================================================== */

    /* Base skeleton animation */
    @keyframes baum-skeleton-loading {
      0% {
        background-position: 200% 0;
      }
      100% {
        background-position: -200% 0;
      }
    }

    /* Base skeleton element */
    .baum-skeleton {
      background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
      background-size: 400% 100%;
      animation: baum-skeleton-loading 1.2s ease-in-out infinite;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
    }

    /* Dark mode skeleton */
    .dark-mode .baum-skeleton {
      background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
      background-size: 400% 100%;
    }

    /* ==========================================================================
       CATEGORY HEADER SKELETON
       ========================================================================== */

    .baum-skeleton-category-header {
      height: 50px;
      background: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
      position: relative;
      overflow: hidden;
    }

    .baum-skeleton-category-nav {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .baum-skeleton-category-items {
      display: flex;
      align-items: center;
      gap: 20px;
      flex: 1;
    }

    .baum-skeleton-category-item {
      height: 24px;
      border-radius: 12px;
      background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
      background-size: 400% 100%;
      animation: baum-skeleton-loading 1.2s ease-in-out infinite;
    }

    .baum-skeleton-category-item:nth-child(1) { width: 80px; }
    .baum-skeleton-category-item:nth-child(2) { width: 95px; }
    .baum-skeleton-category-item:nth-child(3) { width: 70px; }
    .baum-skeleton-category-item:nth-child(4) { width: 110px; }
    .baum-skeleton-category-item:nth-child(5) { width: 85px; }
    .baum-skeleton-category-item:nth-child(6) { width: 75px; }

    .baum-skeleton-more-btn {
      width: 60px;
      height: 24px;
      border-radius: 12px;
      background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
      background-size: 400% 100%;
      animation: baum-skeleton-loading 1.2s ease-in-out infinite;
    }

    .baum-skeleton-darkmode-toggle {
      width: 40px;
      height: 24px;
      border-radius: 12px;
      background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
      background-size: 400% 100%;
      animation: baum-skeleton-loading 1.2s ease-in-out infinite;
    }

    /* ==========================================================================
       CARD SKELETON
       ========================================================================== */

    .baum-skeleton-card {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
      padding: 1rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .baum-skeleton-card__avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
      background-size: 400% 100%;
      animation: baum-skeleton-loading 1.2s ease-in-out infinite;
      margin-right: 1rem;
      flex-shrink: 0;
    }

    .baum-skeleton-card__content {
      flex: 1;
    }

    .baum-skeleton-card__line {
      height: 12px;
      background: linear-gradient(90deg, #e0e0e0 25%, #f8f8f8 50%, #e0e0e0 75%);
      background-size: 400% 100%;
      animation: baum-skeleton-loading 1.2s ease-in-out infinite;
      margin-bottom: 8px;
      border-radius: 4px;
    }

    .baum-skeleton-card__line:nth-child(1) { width: 80%; }
    .baum-skeleton-card__line:nth-child(2) { width: 60%; }
    .baum-skeleton-card__line:nth-child(3) { width: 90%; }

    /* ==========================================================================
       TEXT SKELETON
       ========================================================================== */

    .baum-skeleton-text {
      height: 16px;
      margin-bottom: 8px;
      border-radius: 4px;
    }

    .baum-skeleton-text--title {
      height: 24px;
      width: 70%;
      margin-bottom: 12px;
    }

    .baum-skeleton-text--subtitle {
      height: 18px;
      width: 50%;
      margin-bottom: 10px;
    }

    .baum-skeleton-text--line {
      height: 14px;
      margin-bottom: 6px;
    }

    .baum-skeleton-text--line:nth-child(odd) { width: 85%; }
    .baum-skeleton-text--line:nth-child(even) { width: 75%; }

    /* ==========================================================================
       BUTTON SKELETON
       ========================================================================== */

    .baum-skeleton-button {
      height: 36px;
      width: 120px;
      border-radius: 6px;
      display: inline-block;
      margin-right: 10px;
    }

    .baum-skeleton-button--small {
      height: 28px;
      width: 80px;
    }

    .baum-skeleton-button--large {
      height: 44px;
      width: 160px;
    }

    /* ==========================================================================
       RESPONSIVE ADJUSTMENTS
       ========================================================================== */

    @media (max-width: 768px) {
      .baum-skeleton-category-items {
        gap: 15px;
      }

      .baum-skeleton-category-item:nth-child(n+4) {
        display: none;
      }

      .baum-skeleton-card {
        flex-direction: column;
        align-items: flex-start;
      }

      .baum-skeleton-card__avatar {
        margin-right: 0;
        margin-bottom: 1rem;
      }
    }

    /* ==========================================================================
       FADE IN ANIMATION
       ========================================================================== */

    .baum-skeleton-fade-in {
      animation: baum-skeleton-fade-in 0.3s ease-out;
    }

    @keyframes baum-skeleton-fade-in {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* ==========================================================================
       UTILITY CLASSES
       ========================================================================== */

    .baum-skeleton-hidden {
      display: none !important;
    }

    .baum-skeleton-visible {
      display: block !important;
    }

    .baum-skeleton-loading {
      pointer-events: none;
      user-select: none;
    }
  </style>
  <?php
}

// Hook skeleton styles to wp_head for frontend
add_action('wp_head', 'baum_skeleton_styles');

// Hook skeleton styles to admin_head for admin
add_action('admin_head', 'baum_skeleton_styles');

/**
 * Generate category header skeleton
 */
function baum_get_category_header_skeleton() {
  ob_start();
  ?>
  <div class="baum-skeleton-category-header" id="baum-category-skeleton">
    <div class="baum-skeleton-category-nav">
      <div class="baum-skeleton-category-items">
        <div class="baum-skeleton-category-item"></div>
        <div class="baum-skeleton-category-item"></div>
        <div class="baum-skeleton-category-item"></div>
        <div class="baum-skeleton-category-item"></div>
        <div class="baum-skeleton-category-item"></div>
        <div class="baum-skeleton-category-item"></div>
      </div>
      <div class="baum-skeleton-more-btn"></div>
      <div class="baum-skeleton-darkmode-toggle"></div>
    </div>
  </div>
  <?php
  return ob_get_clean();
}

/**
 * Generate card skeleton
 */
function baum_get_card_skeleton($count = 1) {
  ob_start();
  for ($i = 0; $i < $count; $i++) {
    ?>
    <div class="baum-skeleton-card">
      <div class="baum-skeleton-card__avatar"></div>
      <div class="baum-skeleton-card__content">
        <div class="baum-skeleton-card__line"></div>
        <div class="baum-skeleton-card__line"></div>
        <div class="baum-skeleton-card__line"></div>
      </div>
    </div>
    <?php
  }
  return ob_get_clean();
}

/**
 * Generate text skeleton
 */
function baum_get_text_skeleton($lines = 3, $type = 'line') {
  ob_start();
  for ($i = 0; $i < $lines; $i++) {
    ?>
    <div class="baum-skeleton baum-skeleton-text baum-skeleton-text--<?php echo esc_attr($type); ?>"></div>
    <?php
  }
  return ob_get_clean();
}

/**
 * Generate button skeleton
 */
function baum_get_button_skeleton($size = 'default', $count = 1) {
  ob_start();
  for ($i = 0; $i < $count; $i++) {
    $size_class = $size !== 'default' ? ' baum-skeleton-button--' . $size : '';
    ?>
    <div class="baum-skeleton baum-skeleton-button<?php echo esc_attr($size_class); ?>"></div>
    <?php
  }
  return ob_get_clean();
}

/**
 * Generate navigation skeleton
 */
function baum_get_nav_skeleton($items = 3) {
  ob_start();
  ?>
  <div class="baum-skeleton-nav">
    <?php for ($i = 0; $i < $items; $i++) { ?>
      <div class="baum-skeleton baum-skeleton-nav-item"></div>
    <?php } ?>
  </div>
  <?php
  return ob_get_clean();
}

/**
 * Generate form skeleton
 */
function baum_get_form_skeleton($fields = 3) {
  ob_start();
  ?>
  <div class="baum-skeleton-form">
    <?php for ($i = 0; $i < $fields; $i++) { ?>
      <div style="margin-bottom: 16px;">
        <div class="baum-skeleton baum-skeleton-text" style="width: 100px; height: 14px; margin-bottom: 6px;"></div>
        <div class="baum-skeleton" style="width: 100%; height: 40px; border-radius: 6px;"></div>
      </div>
    <?php } ?>
    <div class="baum-skeleton baum-skeleton-button" style="margin-top: 10px;"></div>
  </div>
  <?php
  return ob_get_clean();
}

/**
 * Generate table skeleton
 */
function baum_get_table_skeleton($rows = 5, $cols = 4) {
  ob_start();
  ?>
  <div class="baum-skeleton-table" style="width: 100%; border-collapse: collapse;">
    <!-- Header -->
    <div style="display: flex; margin-bottom: 10px; padding: 10px 0; border-bottom: 1px solid #e0e0e0;">
      <?php for ($c = 0; $c < $cols; $c++) { ?>
        <div class="baum-skeleton baum-skeleton-text" style="flex: 1; height: 16px; margin-right: 20px;"></div>
      <?php } ?>
    </div>
    <!-- Rows -->
    <?php for ($r = 0; $r < $rows; $r++) { ?>
      <div style="display: flex; margin-bottom: 8px; padding: 8px 0;">
        <?php for ($c = 0; $c < $cols; $c++) { ?>
          <div class="baum-skeleton baum-skeleton-text" style="flex: 1; height: 14px; margin-right: 20px;"></div>
        <?php } ?>
      </div>
    <?php } ?>
  </div>
  <?php
  return ob_get_clean();
}

/**
 * Generate image skeleton
 */
function baum_get_image_skeleton($width = '100%', $height = '200px', $rounded = false) {
  $border_radius = $rounded ? 'border-radius: 50%;' : 'border-radius: 8px;';
  ob_start();
  ?>
  <div class="baum-skeleton" style="width: <?php echo esc_attr($width); ?>; height: <?php echo esc_attr($height); ?>; <?php echo $border_radius; ?>"></div>
  <?php
  return ob_get_clean();
}

/**
 * Generate list skeleton
 */
function baum_get_list_skeleton($items = 5, $show_bullets = true) {
  ob_start();
  ?>
  <div class="baum-skeleton-list">
    <?php for ($i = 0; $i < $items; $i++) { ?>
      <div style="display: flex; align-items: center; margin-bottom: 12px;">
        <?php if ($show_bullets) { ?>
          <div class="baum-skeleton" style="width: 8px; height: 8px; border-radius: 50%; margin-right: 12px; flex-shrink: 0;"></div>
        <?php } ?>
        <div class="baum-skeleton baum-skeleton-text" style="flex: 1; height: 14px; width: <?php echo rand(60, 90); ?>%;"></div>
      </div>
    <?php } ?>
  </div>
  <?php
  return ob_get_clean();
}

/**
 * Generate complete page skeleton
 */
function baum_get_page_skeleton() {
  ob_start();
  ?>
  <div class="baum-skeleton-page" style="max-width: 800px; margin: 0 auto; padding: 20px;">
    <!-- Header -->
    <?php echo baum_get_text_skeleton(1, 'title'); ?>
    <?php echo baum_get_text_skeleton(1, 'subtitle'); ?>

    <!-- Image -->
    <div style="margin: 20px 0;">
      <?php echo baum_get_image_skeleton('100%', '300px'); ?>
    </div>

    <!-- Content -->
    <div style="margin: 20px 0;">
      <?php echo baum_get_text_skeleton(8, 'line'); ?>
    </div>

    <!-- Buttons -->
    <div style="margin: 20px 0;">
      <?php echo baum_get_button_skeleton('default', 2); ?>
    </div>
  </div>
  <?php
  return ob_get_clean();
}