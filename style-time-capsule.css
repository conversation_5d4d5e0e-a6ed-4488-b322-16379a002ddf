/**
 * BaumPress Time Capsule Component Styles
 *
 * This file contains all styles for the Time Capsule component, which displays
 * historical events and facts in an engaging card format with purple theming
 * and interactive elements.
 *
 * @package BaumPress
 * @since 1.0.0
 */

/* ==========================================================================
   TIME CAPSULE COMPONENT
   ========================================================================== */

.baum-time-capsule {
  position: relative;
  background: var(--color-white);
  border: 2px solid var(--color-purple);
  border-radius: var(--border-radius);
  padding: 24px;
  margin: 20px 0;
  max-width: 600px;
  box-shadow: 0 4px 12px rgba(148, 4, 156, 0.1);
  transition: all var(--transition-medium);
}

.baum-time-capsule:hover {
  box-shadow: 0 8px 24px rgba(148, 4, 156, 0.15);
  transform: translateY(-2px);
}

/* Time Capsule Header */
.baum-time-capsule-header {
  position: relative;
  margin-bottom: 20px;
}

/* Clock Icon in Top Right */
.baum-time-capsule-icon {
  position: absolute;
  top: -12px;
  right: -12px;
  width: 60px;
  height: 60px;
  background: var(--color-purple);
  border-radius: 0 var(--border-radius) 0 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: 24px;
  box-shadow: 0 2px 8px rgba(148, 4, 156, 0.3);
}

/* Main Title */
.baum-time-capsule-title {
  font-size: 32px;
  font-weight: 900;
  color: var(--color-body-text);
  margin: 0 0 8px 0;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* Subtitle */
.baum-time-capsule-subtitle {
  font-size: 16px;
  font-style: italic;
  color: var(--color-quinary);
  margin: 0 0 16px 0;
  font-weight: 400;
}

/* Divider Line */
.baum-time-capsule-divider {
  width: 100%;
  height: 2px;
  background: var(--color-purple);
  margin: 16px 0 20px 0;
  border: none;
}

/* Date Display */
.baum-time-capsule-date {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-body-text);
  margin: 0 0 20px 0;
  letter-spacing: -0.5px;
}

/* Content Area */
.baum-time-capsule-content {
  font-size: 18px;
  line-height: 1.6;
  color: var(--color-body-text);
  margin: 0 0 24px 0;
  padding: 0;
}

.baum-time-capsule-content p {
  margin: 0 0 16px 0;
}

.baum-time-capsule-content p:last-child {
  margin-bottom: 0;
}

/* Action Button */
.baum-time-capsule-button {
  background: var(--color-purple);
  color: var(--color-white);
  border: none;
  border-radius: var(--border-radius);
  padding: 16px 32px;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 100%;
  margin-top: 8px;
}

.baum-time-capsule-button:hover {
  background: var(--color-purple-bg);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(148, 4, 156, 0.3);
}

.baum-time-capsule-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(148, 4, 156, 0.3);
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

@media (max-width: 768px) {
  .baum-time-capsule {
    padding: 20px;
    margin: 16px 0;
  }

  .baum-time-capsule-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    top: -10px;
    right: -10px;
  }

  .baum-time-capsule-title {
    font-size: 24px;
    margin-right: 40px;
  }

  .baum-time-capsule-subtitle {
    font-size: 14px;
    margin-right: 40px;
  }

  .baum-time-capsule-date {
    font-size: 24px;
  }

  .baum-time-capsule-content {
    font-size: 16px;
  }

  .baum-time-capsule-button {
    padding: 14px 24px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .baum-time-capsule {
    padding: 16px;
    margin: 12px 0;
  }

  .baum-time-capsule-title {
    font-size: 20px;
  }

  .baum-time-capsule-date {
    font-size: 20px;
  }

  .baum-time-capsule-content {
    font-size: 14px;
  }
}

/* ==========================================================================
   DARK MODE SUPPORT
   ========================================================================== */

@media (prefers-color-scheme: dark) {
  .baum-time-capsule {
    background: var(--card-bg);
    border-color: var(--color-purple);
  }

  .baum-time-capsule-title,
  .baum-time-capsule-date,
  .baum-time-capsule-content {
    color: var(--color-body-text);
  }

  .baum-time-capsule-subtitle {
    color: var(--color-quinary);
  }
}

/* ==========================================================================
   ANIMATION VARIANTS
   ========================================================================== */

.baum-time-capsule.animate-in {
  animation: timeCapsuleSlideIn 0.6s ease-out;
}

@keyframes timeCapsuleSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.baum-time-capsule.loading .baum-time-capsule-content {
  opacity: 0.5;
  pointer-events: none;
}

.baum-time-capsule.loading .baum-time-capsule-button {
  opacity: 0.7;
  cursor: not-allowed;
}

/* ==========================================================================
   STYLE GUIDE SPECIFIC STYLES
   ========================================================================== */

.style-guide-section .baum-time-capsule {
  margin: 20px 0 40px 0;
}

.style-guide-section .baum-time-capsule-demo {
  display: grid;
  gap: 20px;
  margin: 20px 0;
}

@media (min-width: 768px) {
  .style-guide-section .baum-time-capsule-demo {
    grid-template-columns: 1fr 1fr;
  }
}
