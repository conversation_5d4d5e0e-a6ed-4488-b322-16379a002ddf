/**
 * <PERSON>um<PERSON>ress Header Categories Navigation JavaScript
 *
 * Handles responsive category navigation with overflow management.
 * Categories that don't fit are moved to a "More" dropdown menu.
 * Includes skeleton loading for smooth UX.
 *
 * @package BaumPress
 * @since 1.0.0
 */

document.addEventListener("DOMContentLoaded", function () {
  // Skeleton and navigation elements
  const skeleton = document.getElementById("baum-category-skeleton");
  const navContainer = document.getElementById("baum-category-nav");
  const nav = document.querySelector(".categories-nav");
  const categoryList = document.getElementById("category-list");
  const moreBtn = document.getElementById("more-btn");
  const moreDropdown = document.getElementById("more-dropdown");
  const moreMenu = document.querySelector(".more-menu");

  /**
   * Hide skeleton and show navigation with fade-in effect
   */
  function showNavigation() {
    if (skeleton) {
      skeleton.style.display = 'none';
    }
    if (navContainer) {
      navContainer.style.display = 'block';
      navContainer.classList.add('baum-skeleton-fade-in');
    }
  }

  /**
   * Initialize navigation after DOM is ready
   */
  function initializeNavigation() {
    // Early return if required elements don't exist
    if (!nav || !categoryList || !moreBtn || !moreDropdown || !moreMenu) {
      // If elements don't exist, just hide skeleton
      if (skeleton) {
        skeleton.style.display = 'none';
      }
      return;
    }

    // Show navigation after a brief delay to ensure smooth loading
    setTimeout(() => {
      showNavigation();
      adjustCategories();
    }, 150);
  }

  /**
   * Adjusts category display based on available space
   * Moves overflow categories to the "More" dropdown
   */
  function adjustCategories() {
    // Reset dropdown and hide more menu
    moreDropdown.innerHTML = "";
    moreMenu.style.display = "none";

    // Calculate available width (subtract space for more button and toggle)
    const navWidth = nav.clientWidth - 170;
    let totalWidth = 0;
    let moreNeeded = false;

    // Check each category item
    const categoryItems = document.querySelectorAll(".category-item");
    categoryItems.forEach(category => {
      // Show category initially
      category.style.display = "inline-block";
      totalWidth += category.offsetWidth;

      // If it exceeds available space, move to dropdown
      if (totalWidth > navWidth - 80) {
        const clone = category.cloneNode(true);
        moreDropdown.appendChild(clone);
        category.style.display = "none";
        moreNeeded = true;
      }
    });

    // Show more menu if needed
    if (moreNeeded) {
      moreMenu.style.display = "inline-block";
    }
  }

  /**
   * Handles more button click to toggle dropdown
   */
  function handleMoreButtonClick(event) {
    event.preventDefault();
    moreDropdown.classList.toggle("show");

    // Update ARIA attributes for accessibility
    const isExpanded = moreDropdown.classList.contains("show");
    moreBtn.setAttribute("aria-expanded", isExpanded);
  }

  /**
   * Closes dropdown when clicking outside
   */
  function handleDocumentClick(event) {
    if (!moreMenu.contains(event.target)) {
      moreDropdown.classList.remove("show");
      moreBtn.setAttribute("aria-expanded", "false");
    }
  }

  /**
   * Handles keyboard navigation for accessibility
   */
  function handleKeyDown(event) {
    if (event.key === "Escape") {
      moreDropdown.classList.remove("show");
      moreBtn.setAttribute("aria-expanded", "false");
      moreBtn.focus();
    }
  }

  // Event listeners (only add if elements exist)
  if (moreBtn && moreDropdown && moreMenu) {
    moreBtn.addEventListener("click", handleMoreButtonClick);
    document.addEventListener("click", handleDocumentClick);
    document.addEventListener("keydown", handleKeyDown);
    window.addEventListener("resize", adjustCategories);

    // Set initial ARIA attributes
    moreBtn.setAttribute("aria-expanded", "false");
    moreBtn.setAttribute("aria-haspopup", "true");
  }

  // Initialize navigation with skeleton handling
  initializeNavigation();
});
