/**
 * BaumPress Skeleton Loader Utilities
 * 
 * JavaScript utilities for managing skeleton loading states.
 * Provides easy-to-use functions for showing/hiding skeleton loaders.
 * 
 * @package BaumPress
 * @since 1.0.0
 */

(function() {
  'use strict';

  /**
   * BaumSkeleton utility class
   */
  window.BaumSkeleton = {

    /**
     * Show skeleton loader for an element
     * @param {string|Element} target - CSS selector or DOM element
     * @param {string} type - Type of skeleton (card, text, button, etc.)
     * @param {Object} options - Configuration options
     */
    show: function(target, type = 'text', options = {}) {
      const element = typeof target === 'string' ? document.querySelector(target) : target;
      if (!element) return;

      // Default options
      const defaults = {
        lines: 3,
        height: '16px',
        width: '100%',
        className: 'baum-skeleton-temp'
      };
      
      const config = Object.assign({}, defaults, options);
      
      // Store original content
      element.dataset.originalContent = element.innerHTML;
      element.dataset.skeletonActive = 'true';
      
      // Generate skeleton HTML based on type
      let skeletonHTML = '';
      
      switch(type) {
        case 'card':
          skeletonHTML = this.generateCardSkeleton(config);
          break;
        case 'text':
          skeletonHTML = this.generateTextSkeleton(config);
          break;
        case 'button':
          skeletonHTML = this.generateButtonSkeleton(config);
          break;
        case 'image':
          skeletonHTML = this.generateImageSkeleton(config);
          break;
        case 'list':
          skeletonHTML = this.generateListSkeleton(config);
          break;
        default:
          skeletonHTML = this.generateTextSkeleton(config);
      }
      
      element.innerHTML = skeletonHTML;
      element.classList.add('baum-skeleton-loading');
    },

    /**
     * Hide skeleton loader and restore original content
     * @param {string|Element} target - CSS selector or DOM element
     * @param {number} delay - Delay before hiding (for smooth transitions)
     */
    hide: function(target, delay = 0) {
      const element = typeof target === 'string' ? document.querySelector(target) : target;
      if (!element || element.dataset.skeletonActive !== 'true') return;

      setTimeout(() => {
        // Restore original content
        if (element.dataset.originalContent) {
          element.innerHTML = element.dataset.originalContent;
          delete element.dataset.originalContent;
        }
        
        delete element.dataset.skeletonActive;
        element.classList.remove('baum-skeleton-loading');
        element.classList.add('baum-skeleton-fade-in');
        
        // Remove fade-in class after animation
        setTimeout(() => {
          element.classList.remove('baum-skeleton-fade-in');
        }, 300);
      }, delay);
    },

    /**
     * Toggle skeleton state
     * @param {string|Element} target - CSS selector or DOM element
     * @param {string} type - Type of skeleton
     * @param {Object} options - Configuration options
     */
    toggle: function(target, type = 'text', options = {}) {
      const element = typeof target === 'string' ? document.querySelector(target) : target;
      if (!element) return;

      if (element.dataset.skeletonActive === 'true') {
        this.hide(target);
      } else {
        this.show(target, type, options);
      }
    },

    /**
     * Generate card skeleton HTML
     */
    generateCardSkeleton: function(config) {
      return `
        <div class="baum-skeleton-card ${config.className}">
          <div class="baum-skeleton-card__avatar"></div>
          <div class="baum-skeleton-card__content">
            <div class="baum-skeleton-card__line"></div>
            <div class="baum-skeleton-card__line"></div>
            <div class="baum-skeleton-card__line"></div>
          </div>
        </div>
      `;
    },

    /**
     * Generate text skeleton HTML
     */
    generateTextSkeleton: function(config) {
      let html = '';
      for (let i = 0; i < config.lines; i++) {
        const width = i === config.lines - 1 ? '60%' : '100%';
        html += `<div class="baum-skeleton baum-skeleton-text ${config.className}" style="height: ${config.height}; width: ${width}; margin-bottom: 8px;"></div>`;
      }
      return html;
    },

    /**
     * Generate button skeleton HTML
     */
    generateButtonSkeleton: function(config) {
      return `<div class="baum-skeleton baum-skeleton-button ${config.className}" style="width: ${config.width}; height: ${config.height};"></div>`;
    },

    /**
     * Generate image skeleton HTML
     */
    generateImageSkeleton: function(config) {
      const borderRadius = config.rounded ? '50%' : '8px';
      return `<div class="baum-skeleton ${config.className}" style="width: ${config.width}; height: ${config.height}; border-radius: ${borderRadius};"></div>`;
    },

    /**
     * Generate list skeleton HTML
     */
    generateListSkeleton: function(config) {
      let html = '<div class="baum-skeleton-list">';
      for (let i = 0; i < config.lines; i++) {
        html += `
          <div style="display: flex; align-items: center; margin-bottom: 12px;">
            ${config.bullets ? '<div class="baum-skeleton" style="width: 8px; height: 8px; border-radius: 50%; margin-right: 12px; flex-shrink: 0;"></div>' : ''}
            <div class="baum-skeleton baum-skeleton-text ${config.className}" style="flex: 1; height: 14px; width: ${Math.random() * 30 + 60}%;"></div>
          </div>
        `;
      }
      html += '</div>';
      return html;
    },

    /**
     * Show skeleton for multiple elements
     * @param {Array} targets - Array of target configurations
     */
    showMultiple: function(targets) {
      targets.forEach(target => {
        this.show(target.element, target.type, target.options);
      });
    },

    /**
     * Hide skeleton for multiple elements
     * @param {Array} targets - Array of target selectors or elements
     * @param {number} staggerDelay - Delay between each hide (for staggered effect)
     */
    hideMultiple: function(targets, staggerDelay = 50) {
      targets.forEach((target, index) => {
        this.hide(target, index * staggerDelay);
      });
    },

    /**
     * Auto-hide skeleton after a timeout
     * @param {string|Element} target - CSS selector or DOM element
     * @param {number} timeout - Timeout in milliseconds
     */
    autoHide: function(target, timeout = 2000) {
      setTimeout(() => {
        this.hide(target);
      }, timeout);
    },

    /**
     * Show skeleton while promise is pending
     * @param {string|Element} target - CSS selector or DOM element
     * @param {Promise} promise - Promise to wait for
     * @param {string} type - Type of skeleton
     * @param {Object} options - Configuration options
     */
    showWhilePending: function(target, promise, type = 'text', options = {}) {
      this.show(target, type, options);
      
      promise.finally(() => {
        this.hide(target, 150);
      });
      
      return promise;
    },

    /**
     * Initialize skeleton loaders for elements with data attributes
     */
    initDataAttributes: function() {
      document.querySelectorAll('[data-skeleton]').forEach(element => {
        const type = element.dataset.skeleton || 'text';
        const lines = parseInt(element.dataset.skeletonLines) || 3;
        const height = element.dataset.skeletonHeight || '16px';
        const width = element.dataset.skeletonWidth || '100%';
        
        this.show(element, type, { lines, height, width });
      });
    }
  };

  /**
   * jQuery plugin for skeleton loaders (if jQuery is available)
   */
  if (typeof jQuery !== 'undefined') {
    jQuery.fn.skeleton = function(action = 'show', type = 'text', options = {}) {
      return this.each(function() {
        if (action === 'show') {
          BaumSkeleton.show(this, type, options);
        } else if (action === 'hide') {
          BaumSkeleton.hide(this);
        } else if (action === 'toggle') {
          BaumSkeleton.toggle(this, type, options);
        }
      });
    };
  }

  /**
   * Auto-initialize on DOM ready
   */
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize skeleton loaders for elements with data attributes
    BaumSkeleton.initDataAttributes();
    
    // Auto-hide skeletons after 3 seconds (fallback)
    setTimeout(() => {
      document.querySelectorAll('[data-skeleton-auto-hide]').forEach(element => {
        BaumSkeleton.hide(element);
      });
    }, 3000);
  });

})();
