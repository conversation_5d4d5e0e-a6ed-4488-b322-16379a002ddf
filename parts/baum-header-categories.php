<?php
/**
 * BaumPress Header Categories Navigation Template Part
 *
 * This template part displays a responsive category navigation bar with overflow
 * handling. Categories that don't fit in the available space are moved to a
 * "More" dropdown menu. Includes dark mode toggle integration and skeleton loading.
 *
 * Features:
 * - Responsive category navigation with overflow handling
 * - Dynamic "More" dropdown for categories that don't fit
 * - Active category highlighting
 * - Dark mode toggle integration
 * - Fixed positioning for persistent navigation
 * - JavaScript-powered responsive behavior
 * - Apple-style menu design
 * - Skeleton loading for smooth UX
 *
 * @package BaumPress
 * @since 1.0.0
 */

// Security check
if (!defined('ABSPATH')) {
  exit;
}

// Get all categories ordered by name
$all_categories = get_categories([
  'orderby' => 'name',
  'order' => 'ASC',
  'hide_empty' => true
]);

// Get current categories for active state highlighting
$current_categories = function_exists('get_current_categories') ? get_current_categories() : [];
$current_category_ids = wp_list_pluck($current_categories, 'term_id');
?>

<!-- Category Header Skeleton Loader -->
<?php echo baum_get_category_header_skeleton(); ?>

<div class="small-navbar" id="baum-category-nav" style="display: none;">
  <nav class="container categories-nav">

    <ul id="category-list">
      <?php foreach ($all_categories as $category) : ?>
        <?php
        $is_active = in_array($category->term_id, $current_category_ids);
        $category_link = get_category_link($category->term_id);
        ?>
        <li class="category-item<?php echo $is_active ? ' active' : ''; ?>">
          <a href="<?php echo esc_url($category_link); ?>">
            <?php echo esc_html($category->name); ?>
          </a>
        </li>
      <?php endforeach; ?>
    </ul>

    <div class="more-menu">
      <ul class="baum-apple-menu">
        <li class="menu-item">
          <a id="more-btn" href="#" aria-label="<?php esc_attr_e('More categories', 'baumpress'); ?>">
            <?php esc_html_e('More', 'baumpress'); ?>
            <i class="fa-fw fa-solid fa-caret-down more-caret"></i>
          </a>
          <ul id="more-dropdown" class="sub-menu"></ul>
        </li>
      </ul>
    </div>

    <div class="darkmode-toggle-container">
      <?php echo do_shortcode('[baum_darkmode_toggle]'); ?>
    </div>

  </nav>
</div>
