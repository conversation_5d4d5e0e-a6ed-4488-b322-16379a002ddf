<?php

//
// Set path for yt-dlp
//
putenv('PATH=/Users/<USER>/Library/Python/3.9/bin:' . getenv('PATH'));

// Define Constants
define('BAUM_FEATURED_IMG_MIN_WIDTH', 1000);

// $logo_url = get_theme_mod('baum_logo');
// $greeting = 'Shalom, %s';
// require_once(get_template_directory() . '/create-user.php');

//
// Notify the editor of admin color scheme
//
// $current_user_id = get_current_user_id();
// if ($current_user_id) {
//   $admin_color_scheme = get_user_option('admin_color', $current_user_id);
//   baum_notify_admin('Current Admin Color Scheme:', $admin_color_scheme);
// }

/**
 * Updates all attachment posts to allow comments
 *
 * This function sets the comment_status to 'open' for all attachment post types
 * in the WordPress database, enabling comments on all media files.
 *
 * @global wpdb $wpdb WordPress database abstraction object
 * @return void
 *
 * @since 1.0.0
 */
function allow_comments_for_all_attachments() {
  global $wpdb;
  $wpdb->query("UPDATE {$wpdb->posts} SET comment_status = 'open' WHERE post_type = 'attachment'");
}
// allow_comments_for_all_attachments();

/**
 * Transfers all featured images to a custom meta field
 *
 * This function gets all posts and stories, retrieves their featured image IDs,
 * adds them to the 'multiple_featured_images' custom meta field, and then
 * removes the original featured image. This allows for multiple featured images
 * to be associated with a post.
 *
 * @return void
 *
 * @since 1.0.0
 */
function transfer_featured_images_to_meta_field() {
  $posts = get_posts(['post_type' => ['post', 'story'], 'numberposts' => -1]);
  foreach ($posts as $post) {
    $featured_image_id = get_post_thumbnail_id($post->ID);
    if ($featured_image_id) {
      $existing_images = get_post_meta($post->ID, 'multiple_featured_images', true) ?: [];
      if (!in_array($featured_image_id, $existing_images)) {
        $existing_images[] = $featured_image_id;
        update_post_meta($post->ID, 'multiple_featured_images', $existing_images);
      }
      delete_post_thumbnail($post->ID);
    }
  }
}
// add_action('init', 'transfer_featured_images_to_meta_field');

/**
 * Removes Iframely auto-embed functionality
 *
 * This function removes the Iframely filter from the oembed_result hook,
 * disabling automatic embedding of Iframely content in the Gutenberg editor.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_deregister_iframely_autoembed () {
  remove_filter('oembed_result', [ 'Iframely', 'process' ], 10);
}
add_action('init', 'baum_deregister_iframely_autoembed');

/**
 * Adds a meta box for multiple featured images
 *
 * This function registers a meta box in the WordPress admin that allows
 * users to add and manage multiple featured images for posts and stories.
 * The meta box appears in the sidebar of the post editor.
 *
 * @return void
 *
 * @since 1.0.0
 */
function add_multiple_featured_images_meta_box () {
  add_meta_box(
    'multiple_featured_images',
    __('Baum\'s Featured Images'),
    'multiple_featured_images_callback',
    [ 'story', 'post' ], // Adjust post types as needed
    'side'
  );
}
add_action('add_meta_boxes', 'add_multiple_featured_images_meta_box');

/**
 * Callback function for the multiple featured images meta box
 *
 * This function renders the content of the multiple featured images meta box.
 * It displays the currently selected images, provides UI for adding/removing images,
 * and includes JavaScript for handling the media library integration.
 *
 * @param WP_Post $post The post object being edited
 * @return void
 *
 * @since 1.0.0
 */
function multiple_featured_images_callback ($post) {
  $image_ids = get_post_meta($post->ID, 'multiple_featured_images', true) ?: [];
  print_r($image_ids);
  $max_featured_images = get_theme_mod('max_featured_images', 5);
  wp_enqueue_media();
  ?>
  <div id="multiple-featured-images">
    <button id="add-featured-images" class="button" <?php echo count($image_ids) >= $max_featured_images ? 'disabled' : ''; ?>>Add Images</button>
    <ul id="featured-image-list">
      <?php foreach ($image_ids as $image_id): ?>
        <li>
          <a href="#" class="open-media" data-id="<?php echo esc_attr($image_id); ?>"><?php echo wp_get_attachment_image($image_id, 'thumbnail', false, ['style' => 'border-radius: 8px;']); ?></a>
          <p>
            Caption: <?php echo esc_html(wp_get_attachment_caption($image_id)); ?>
          </p>
          <input type="hidden" name="multiple_featured_images[]" value="<?php echo esc_attr($image_id); ?>">
          <button class="remove-image button-link-delete">Remove</button>
        </li>
      <?php endforeach; ?>
    </ul>
  </div>
  <script>
    /**
     * Multiple Featured Images functionality
     *
     * This script handles the multiple featured images feature in the WordPress admin.
     * It allows users to add, remove, and edit multiple featured images for posts.
     *
     * @since 1.0.0
     */
    jQuery(document).ready(function($) {
      /**
       * Maximum number of featured images allowed
       * @type {number}
       */
      const maxImages = <?php echo esc_js($max_featured_images); ?>;

      /**
       * Handles the "Add Images" button click
       * Opens the WordPress media library for image selection
       *
       * @param {Event} e - Click event object
       * @returns {void}
       */
      $('#add-featured-images').on('click', function(e) {
        e.preventDefault();

        /**
         * WordPress media frame for image selection
         * @type {object}
         */
        const mediaFrame = wp.media({
          title: 'Select Images',
          button: { text: 'Use Images' },
          multiple: true
        });

        /**
         * Handles image selection from the media library
         * Adds selected images to the featured images list
         *
         * @returns {void}
         */
        mediaFrame.on('select', function() {
          const selection = mediaFrame.state().get('selection');
          const currentCount = $('#featured-image-list li').length;

          /**
           * Process each selected attachment
           *
           * @param {object} attachment - WordPress attachment object
           * @returns {boolean|void} - Returns false if max images limit is reached
           */
          selection.each(function(attachment) {
            if (currentCount >= maxImages) {
              return false; // Limit reached
            }
            const imgID = attachment.id;
            const imgURL = attachment.attributes.url;
            $('#featured-image-list').append(`
              <li>
                <a href="#" class="open-media" data-id="${imgID}">
                  <img src="${imgURL}" style="width: 100px; border-radius: 8px;">
                </a>
                <p>
                  Caption: ${attachment.attributes.caption || 'No caption available'}
                </p>
                <input type="hidden" name="multiple_featured_images[]" value="${imgID}">
                <button class="remove-image button-link-delete">Remove</button>
              </li>
            `);
          });

          // Disable the "Add Images" button if max limit is reached
          if ($('#featured-image-list li').length >= maxImages) {
            $('#add-featured-images').prop('disabled', true);
          }
        });

        mediaFrame.open();
      });

      /**
       * Handles removing an image from the featured images list
       *
       * @returns {void}
       */
      $(document).on('click', '.remove-image', function() {
        $(this).parent().remove();
        // Enable the "Add Images" button if below the limit
        if ($('#featured-image-list li').length < 4) {
          $('#add-featured-images').prop('disabled', false);
        }
      });

      /**
       * Handles opening the media library to edit an existing image
       *
       * @param {Event} e - Click event object
       * @returns {void}
       */
      $(document).on('click', '.open-media', function(e) {
        e.preventDefault();
        const imgID = $(this).data('id');
        const mediaFrame = wp.media({
          title: 'Edit Image',
          library: { type: 'image', id: imgID },
          button: { text: 'Close' },
          multiple: false
        });
        mediaFrame.open();
      });
    });
  </script>
  <?php
}

/**
 * Saves the multiple featured images data
 *
 * This function is called when a post is saved. It processes the submitted
 * featured images data, limits the number of images based on theme settings,
 * and updates the post meta with the selected image IDs.
 *
 * @param int $post_id The ID of the post being saved
 * @return void
 *
 * @since 1.0.0
 */
function save_multiple_featured_images ($post_id) {
  // Get the max number of featured images from the Customizer setting
  $max_images = get_theme_mod('max_featured_images', 5);

  if (isset($_POST['multiple_featured_images'])) {
    $images = array_slice($_POST['multiple_featured_images'], 0, $max_images);
    update_post_meta($post_id, 'multiple_featured_images', $images);
  } else {
    delete_post_meta($post_id, 'multiple_featured_images');
  }
}

add_action('save_post', 'save_multiple_featured_images');

// function baum_custom_iframely_embed () {
//   wp_enqueue_script(
//       'baum-embed-handler',
//       get_template_directory_uri() . '/admin/baum-embed-handler.js',
//       ['wp-blocks', 'wp-element', 'wp-editor', 'wp-i18n']
//   );
//   // echo 'embed';
// }
//
// add_action('enqueue_block_editor_assets', 'baum_custom_iframely_embed');

// function baum_register_inline_embed_block() {
//   // Register the block editor script
//   wp_register_script(
//       'baum-inline-embed-block',
//       get_template_directory_uri() . '/admin/baum-inline-embed-block.js',
//       [ 'wp-blocks', 'wp-element', 'wp-editor' ]
//   );
//   // Register the block
//   register_block_type('baum/inline-embed', array(
//       'editor_script' => 'baum-inline-embed-block',
//       'render_callback' => 'baum_render_inline_embed_block',
//       'attributes' => array(
//           'url' => array(
//               'type' => 'string',
//               'default' => ''
//           ),
//           'title' => array(
//               'type' => 'string',
//               'default' => ''
//           ),
//       ),
//       'supports' => [
//           'inline' => true,
//       ],
//   ));
// }
//
// add_action('init', 'baum_register_inline_embed_block');

// //
// // Allow the block renderer to respond to REST requests for preview
// //
// function baum_render_inline_embed_block ($attributes) {
//   $url = isset($attributes['url']) ? esc_url($attributes['url']) : '';
//   $title = isset($attributes['title']) ? sanitize_text_field($attributes['title']) : '';
//   if (empty($url)) {
//       return ''; // Don't render anything if no URL is provided
//   }
//   // Execute the shortcode and return its output
//   return do_shortcode('[baum_embed url="' . $url . '" title="' . $title . '"]');
// }

/////////////////////////////////////
// Enqueque Custom Admin Area CSS
/////////////////////////////////////

// function load_custom_wp_admin_style () {
//   wp_register_style(
//     'custom_wp_admin_css',
//     get_bloginfo('stylesheet_directory') . '/css/spectre.css',
//     false,
//     '1.0.0'
//   );
//   wp_enqueue_style( 'custom_wp_admin_css' );
// }
// add_action('admin_enqueue_scripts', 'load_custom_wp_admin_style');

/////////////////////////////////////
// Customizer Additions
/////////////////////////////////////

// require get_template_directory() . '/inc/customizer.php';

/**
 *
 * Use a custom wp_die() handler.
 *
 */

// function baum_wp_die_handler ($message, $title = '', $args = []) {
// 	$defaults = ['response' => 500];
// 	$r = wp_parse_args($args, $defaults);
// 	if (function_exists('is_wp_error') && is_wp_error($message)) {
// 		$errors = $message->get_error_messages();
// 		switch (count($errors)) {
// 			case 0 :
// 				$message = '';
// 				break;
// 			case 1 :
// 				$message = $errors[0];
// 				break;
// 			default :
// 				$message = "<ul>\n\t\t<li>"
//           . join("</li>\n\t\t<li>", $errors)
//           . "</li>\n\t</ul>";
// 				break;
// 		}
// 	} else {
// 		$message = strip_tags($message);
// 	}
// 	require_once get_stylesheet_directory() . '/wp-die.php';
// 	die();
// }

// add_filter('wp_robots', function ($ret) {
//   global $pagenow;
//   if ('wp-comments-post.php' !== $pagenow) return $ret;
//   $url = get_template_directory_uri() . '/style.css';
//   echo '<link rel="stylesheet" href="' . $url . '">';
//   return $ret;
// });

/**
 * Enqueues scripts and styles for the WordPress admin area
 *
 * This function loads various CSS and JavaScript files needed for the admin
 * interface, including Font Awesome icons, syntax highlighting, tooltips,
 * and custom theme styles. It also sets up the editor styles.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enqueue_admin_scripts () {
  $version = wp_get_theme()->get('Version');

  //
  // Font Awesome 6
  //

  wp_enqueue_style(
    'font-awesome-6',
    get_template_directory_uri() . '/css/font-awesome-6.css',
    [],
    '6.0.0',
    'all',
  );

  // TODO: Save the font files locally and link to them locally
  //
  // wp_enqueue_style(
  //   'font-awesome-6',
  //   'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css',
  //   [],
  //   '6.0.0',
  //   'all',
  // );

  //
  // Style Variables CSS
  //

  wp_enqueue_style(
    'style-vars-css',
    get_template_directory_uri() . '/style-baum-vars.css'
  );

  // //
  // // Styles
  // //
  // wp_enqueue_style(
  //   'style',
  //   get_template_directory_uri() . '/style.css'
  // );

  //
  // Baum Styles
  //

  wp_enqueue_style(
    'baum-style',
    get_template_directory_uri() . '/style-baum.css'
  );

  //
  // Baum Colors Styles
  //

  wp_enqueue_style(
    'baum-colors',
    get_template_directory_uri() . '/style-baum-colors.css'
  );

  //
  // Baum Cards Styles
  //

  wp_enqueue_style(
    'baum-cards',
    get_template_directory_uri() . '/style-baum-cards.css'
  );

  //
  //
  //

  wp_enqueue_style(
    'baum-third-party',
    get_template_directory_uri() . '/style-third-party.css'
  );


  //
  // Dark Mode CSS
  //

  wp_enqueue_style(
    'admin-dark-mode-css',
    get_template_directory_uri() . '/admin/dark-mode.css'
  );

  //
  // Dark Mode JS
  //

  wp_enqueue_script(
    'admin-dark-mode-js',
    get_template_directory_uri() . '/admin/dark-mode.js',
    ['jquery'],
    null,
    true
  );

  //
  // Baum Font Styles
  //

  wp_enqueue_style(
    'baum-font',
    get_template_directory_uri() . '/style-font.css'
  );

  //
  // editor-style.css
  //

  add_editor_style(['editor-style.css']);

  //
  // Syntax Highlighting - Atom One (dark)
  //
  wp_enqueue_style(
    'baum-atom-one-dark',
    get_template_directory_uri() . '/css/atom-one-dark.css',
    [],
    $version,
    'all'
  );

  // //
  // // Syntax Highlighting - Atom One (light)
  // //
  // wp_enqueue_style(
  //   'baum-atom-one-light',
  //   get_template_directory_uri() . '/css/atom-one-light.css',
  //   [],
  //   $version,
  //   'all'
  // );

  //
  // Highlight.js
  //

  wp_enqueue_script(
    'baum-highlight-js',
    get_template_directory_uri() . '/js/highlight.js',
    ['jquery'],
    '1.0',
    true
  );

  //
  // Syntax Highlighting - select desired programming languages
  //

  $comp_lang = [
    'apache',
    'bash',
    'css',
    'dockerfile',
    'graphql',
    'http',
    'javascript',
    'json',
    'makefile',
    'markdown',
    'nginx',
    'php',
    'plaintext',
    'python',
    'shell',
    'sql',
    'typescript',
    'yaml'
  ];

  //
  // Syntax Highlighting - enqueque each language in the array
  //

  foreach ($comp_lang as $lang) {
    wp_enqueue_script(
      'baum-highlight-language-' . $lang,
      get_template_directory_uri() . '/js/languages/' . $lang . '.js',
      ['baum-highlight-js'],
      '1.0',
      true
    );
  }

  //
  // Tooltipster JS
  //

  wp_enqueue_script(
    'baum-tooltips-js',
    get_template_directory_uri() . '/js/tooltipster.bundle.min.js',
    ['jquery'],
    '1.0',
    true
  );

  //
  // Tooltipster CSS
  //

  wp_enqueue_style(
    'baum-tooltips-css',
    get_template_directory_uri() . '/css/tooltipster.bundle.min.css',
    [],
    $version,
    'all'
  );

  //
  // Tooltipster CSS - Borderless Theme
  //

  wp_enqueue_style(
    'baum-tooltips-theme',
    get_template_directory_uri() . '/css/tooltipster-borderless.min.css',
    ['baum-tooltips-css'],
    $version,
    'all'
  );

  //
  // Main Index JS
  //

  wp_register_script(
    'baum-index',
    get_template_directory_uri() . '/admin/index.js',
    ['baum-tooltips-js'],
    '1.0',
    true
  );

  //
  // Send data to the frontend javascript file
  //

  wp_localize_script(
    'baum-index',
    'wp_object',
    []
  );

  wp_enqueue_script('baum-index');
}

add_action('admin_enqueue_scripts', 'baum_enqueue_admin_scripts');

/**
 * Sets the default order of posts in the admin list view
 *
 * This function modifies the WordPress query to order posts by date in
 * descending order (newest first) when viewing the posts list in the admin,
 * unless a specific ordering has been requested.
 *
 * @param WP_Query $wp_query The WordPress query object
 * @return void
 *
 * @since 1.0.0
 */
function set_post_order_in_admin ($wp_query) {
  global $pagenow;
  if (is_admin() && 'edit.php' == $pagenow && !isset($_GET['orderby'])) {
    $wp_query->set('orderby', 'date');
    $wp_query->set('order', 'DESC');
  }
}

add_filter('pre_get_posts', 'set_post_order_in_admin');

/**
 * Adds the current date and time to the WordPress admin bar
 *
 * This function adds a menu item to the admin bar that displays the current
 * local date and time. Clicking on it navigates to the general settings page.
 *
 * @param WP_Admin_Bar $wp_admin_bar The WordPress admin bar object
 * @return void
 *
 * @since 1.0.0
 */
function baum_datetime_adminbar (WP_Admin_Bar $wp_admin_bar) {
  // $screen = get_current_screen();
  $parent_slug = 'adminbar-date-time';
  $local_time  = date('F j, Y g:i a', current_time('timestamp', 0));
  $wp_admin_bar->add_menu([
    'id'     => $parent_slug,
    'parent' => 'top-secondary',
    'group'  => null,
    'title'  => $local_time,
    'href'   => admin_url('/options-general.php')
  ]);
}

add_action('admin_bar_menu', 'baum_datetime_adminbar');

/////////////////////////////////////
// Use Rich Text Editor for Media Descriptions
/////////////////////////////////////

// apply_filters('activate_tinymce_for_media_description', true);



/////////////////////////////////////
// Enqueue Javascript / CSS
/////////////////////////////////////

// function baum_enqueue_scripts () {
//   global $wp_styles;
//   $version = wp_get_theme()->get('Version');

//   //
//   // Font Awesome 6
//   //
//   // TODO: Save the font files locally and link to them locally

//   wp_enqueue_style(
//     'font-awesome-6',
//     'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css',
//     [],
//     '6.0.0',
//     'all',
//   );

//   //
//   // Font Awesome 5
//   //

//   wp_enqueue_style(
//     'font-awesome-5',
//     'https://use.fontawesome.com/releases/v5.12.1/css/all.css',
//     [],
//     '5.12.1',
//     'all'
//   );

//   //
//   // Tooltipster JS
//   //

//   wp_enqueue_script(
//     'baum-tooltips-js',
//     get_template_directory_uri() . '/js/tooltipster.bundle.min.js',
//     ['jquery'],
//     '1.0',
//     true
//   );

//   //
//   // Tooltipster CSS
//   //

//   wp_enqueue_style(
//     'baum-tooltips-css',
//     get_template_directory_uri() . '/css/tooltipster.bundle.min.css',
//     [],
//     $version,
//     'all'
//   );

//   //
//   // Tooltipster CSS - Borderless Theme
//   //

//   wp_enqueue_style(
//     'baum-tooltips-theme',
//     get_template_directory_uri() . '/css/tooltipster-borderless.min.css',
//     ['baum-tooltips-css'],
//     $version,
//     'all'
//   );

//   //
//   // Syntax Highlighting - Atom One (dark)
//   //

//   wp_enqueue_style(
//     'baum-atom-one-dark',
//     get_template_directory_uri() . '/css/atom-one-dark.css',
//     [],
//     $version,
//     'all'
//   );

//   //
//   // Syntax Highlighting - Atom One (light)
//   //

//   wp_enqueue_style(
//     'baum-atom-one-light',
//     get_template_directory_uri() . '/css/atom-one-light.css',
//     [],
//     $version,
//     'all'
//   );

  // //
  // // Highlight.js
  // //

  // wp_enqueue_script(
  //   'baum-highlight-js',
  //   get_template_directory_uri() . '/js/highlight.js',
  //   ['jquery'],
  //   '1.0',
  //   true
  // );

  // //
  // // Syntax Highlighting - select desired programming languages
  // //

  // $comp_lang = [
  //   'apache',
  //   'bash',
  //   'css',
  //   'dockerfile',
  //   'graphql',
  //   'http',
  //   'javascript',
  //   'json',
  //   'makefile',
  //   'markdown',
  //   'nginx',
  //   'php',
  //   'plaintext',
  //   'python',
  //   'shell',
  //   'sql',
  //   'typescript',
  //   'yaml'
  // ];

  // //
  // // Syntax Highlighting - enqueque each language in the array
  // //

  // foreach ($comp_lang as $lang) {
  //   wp_enqueue_script(
  //     'baum-highlight-language-' . $lang,
  //     get_template_directory_uri() . '/js/languages/' . $lang . '.js',
  //     ['baum-highlight-js'],
  //     '1.0',
  //     true
  //   );
  // }

//   //
//   // Step 1 - register the script
//   //
//   wp_register_script(
//     'baum-admin-index',
//     get_template_directory_uri() . '/admin/index.js',
//     ['baum-tooltips'],
//     '1.0',
//     true
//   );

//   if (is_single()) {
//     $cat_ids = [];
//     $cats = get_the_category();
//     foreach ($cats as $cat) {
//       $cat_ids[] = get_cat_ID($cat->cat_name);
//     }

//     //
//     // Step 2 - pass some data to the frontend javascript file
//     //
//     wp_localize_script(
//       'baum-admin-index',
//       'wp_object',
//       [
//         'cat_ids' => $cat_ids,
//         'categories' => $cats,
//       ]
//     );
//   }

//   //
//   // Step 3 - enqueque the script
//   //
//   wp_enqueue_script('baum-admin-index');


//   /////////////////////////////////////
//   // Sign Out Redirect
//   /////////////////////////////////////

//   function auto_redirect_after_logout () {
//     wp_safe_redirect(home_url());
//     exit;
//   }

//   add_action('wp_logout', 'auto_redirect_after_logout');

//   /////////////////////////////////////
//   // CSS Color Variables
//   /////////////////////////////////////

//   $color_gray = '#616161';
//   $color_black = '#2b2b2b';
//   $color_pink = '#e70d5a';
//   $color_red = '#9c0404';
//   $color_purple = '#94049c';
//   $color_blue = '#195bc4';
//   $color_green = '#13a471';
//   $color_teal = '#048f9c';
//   $color_yellow = '#f1d72d';
//   $color_orange = '#d77f26';
//   $color_primary = '#bc0c0c';
//   $color_secondary = '#000000';
//   $color_tertiary = '#191919';
//   $color_quaternary = '#2d2d2d';
//   $color_quinary = '#737373';
//   $color_senary = '#a3a3a3';
//   $color_septenary = '#d6d6d6';
//   $color_octonary = '#e2e2e2';
//   $color_nonary = '#ebebeb';
//   $color_denary = '#f2f2f2';
//   $color_primary_dark = '#bc0c0c';
//   $color_secondary_dark = '#000000';
//   $color_tertiary_dark = '#191919';
//   $color_quaternary_dark = '#2d2d2d';
//   $color_quinary_dark = '#5f5f5f';
//   $color_senary_dark = '#5a5a5a';
//   $color_septenary_dark = '#5d5d5d';
//   $color_octonary_dark = '#9d9d9d';
//   $color_nonary_dark = '#c4c4c4';
//   $color_denary_dark = '#bababa';

// }

// add_action('wp_enqueue_scripts', 'baum_enqueue_scripts');



// //
// // Does an Author have the same slug as an Influential Person
// //
// function check_slug_conflict ($author_name, $single_person_slug) {
//   $user = get_user_by('slug', $author_name);
//   $single_person = get_page_by_path(
//     $single_person_slug,
//     OBJECT,
//     'single-person'
//   );
//   if ($user && $single_person) return true;
//   return false;
// }

// //
// // Display warning in admin if conflicts are found
// //

// function baum_display_slug_conflict_warning () {
//   $authors = get_users([
//     'fields' => [ 'user_nicename' ]
//   ]);

//   $single_persons = get_posts([
//     'post_type' => 'single-person',
//     'posts_per_page' => -1,
//   ]);

//   foreach ($authors as $author) {
//     foreach ($single_persons as $single_person) {
//       if (check_slug_conflict($author->user_nicename, $single_person->post_name)) {
//         baum_notify_admin(
//           'Conflict detected:',
//           'The slug "' . $author->user_nicename . '" is used by both an author and a single person post.'
//         );
//       }
//     }
//   }
// }
// add_action('admin_notices', 'baum_display_slug_conflict_warning');

//
// Fix for Yoast SEO
//
// add_filter( 'wpseo_primary_term_taxonomies', '__return_empty_array' );

/**
 * Sets a YouTube video thumbnail as the featured image
 *
 * This function checks if a post has a YouTube video ID in an ACF field,
 * fetches the thumbnail image from YouTube, and sets it as the post's
 * featured image if one doesn't already exist.
 *
 * @param int $post_id The ID of the post to set the featured image for
 * @return void
 *
 * @since 1.0.0
 */
function baum_get_youtube_thumbnail ($post_id) {

  // Check if post has featured image
  if (has_post_thumbnail($post_id)) {
    error_log('Post ID {$post_id}: Already has a featured image.');
    return;
  }

  // Check if a featured image is set manually
  if (get_post_meta($post_id, '_thumbnail_id', true)) return;

  // Check if this is an autosave or a revision
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
  if (wp_is_post_revision($post_id)) return;

  // Check if the YouTube Video ID ACF field is set
  $youtube_id = get_field('youtube_id', $post_id);
  if (!$youtube_id) return;

  // Add Featured Image to Post
  $image_url = 'https://img.youtube.com/vi/' . $youtube_id . '/maxresdefault.jpg';
  $image_name = $youtube_id . '.jpg';
  $upload_dir = wp_upload_dir(); // Set upload folder
  $image_data = file_get_contents($image_url); // Get image data
  $unique_file_name = wp_unique_filename($upload_dir['path'], $image_name);
  // Generate unique name
  $filename = basename( $unique_file_name ); // Create image file name

  // Check folder permission and define file location
  if (wp_mkdir_p($upload_dir['path'])) {
    $file = $upload_dir['path'] . '/' . $filename;
  } else {
    $file = $upload_dir['basedir'] . '/' . $filename;
  }

  // Create the image  file on the server
  file_put_contents($file, $image_data);

  // Check image file type
  $wp_filetype = wp_check_filetype($filename, null);

  // Set attachment data
  $attachment = [
    'post_mime_type' => $wp_filetype['type'],
    'post_title' => sanitize_file_name($filename),
    'post_content' => '',
    'post_status' => 'inherit'
  ];

  // Create the attachment
  $attach_id = wp_insert_attachment($attachment, $file, $post_id);

  // Include image.php
  require_once(ABSPATH . 'wp-admin/includes/image.php');

  // Define attachment metadata
  $attach_data = wp_generate_attachment_metadata($attach_id, $file);

  // Assign metadata to attachment
  wp_update_attachment_metadata($attach_id, $attach_data);

  // And finally assign featured image to post
  set_post_thumbnail($post_id, $attach_id);
}

// add_filter('save_post', 'baum_get_youtube_thumbnail', 20);

// function customize_video_shortcode_defaults($output, $atts, $video, $post_id) {
//     // Temporarily remove the filter to avoid recursion
//     remove_filter('wp_video_shortcode_override', 'customize_video_shortcode_defaults', 10);

//     // Override width and height defaults
//     $atts['width'] = '100%'; // Set to 100% for responsive layout
//     $atts['height'] = ''; // Height will adjust automatically based on width

//     // Generate the video shortcode output
//     $output = wp_video_shortcode($atts, $video, $post_id);

//     // Re-add the filter after generating the output
//     add_filter('wp_video_shortcode_override', 'customize_video_shortcode_defaults', 10, 4);

//     return $output;
// }
// add_filter('wp_video_shortcode_override', 'customize_video_shortcode_defaults', 10, 4);















/**
 * Enqueues the Gutenberg refresh script
 *
 * This function loads a JavaScript file that refreshes the Gutenberg editor
 * when certain conditions are met. It only loads on post edit screens.
 *
 * @param {string} $hook - The current admin page hook
 * @return {void}
 */
function enqueue_gutenberg_refresh_script($hook) {
  // Only load for the Gutenberg editor
  if ($hook !== 'post.php' && $hook !== 'post-new.php') return;

  wp_enqueue_script(
      'gutenberg-refresh-script',
      get_template_directory_uri() . '/js/gutenberg-refresh.js', // Adjust path as needed
      ['wp-data', 'wp-edit-post'],
      '1.0',
      true
  );
}

add_action('admin_enqueue_scripts', 'enqueue_gutenberg_refresh_script');

/**
 * Generates a thumbnail from a video file
 *
 * This function uses FFmpeg to extract a frame from a video file
 * and create a thumbnail image. It's triggered by an ACF field
 * and processes videos attached to posts.
 *
 * @param int $post_id The ID of the post with the video attachment
 * @return void
 *
 * @since 1.0.0
 */
function baum_generate_video_thumbnail ($post_id) {
  error_log('baum_generate_video_thumbnail');
  // Ensure this is not an autosave or a revision
  if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
  if (wp_is_post_revision($post_id)) return;

  $trigger_thumbnail_generation = get_field('trigger_thumbnail_generation', $post_id);


  $ffmpeg_path = locate_command('ffmpeg');

  error_log('ffmpeg_path: ' . $ffmpeg_path);


  if (!$trigger_thumbnail_generation) return;

  error_log('$trigger_thumbnail_generation');

  // Check if the ACF field 'video_file' contains a video attachment
  $video_file_id = get_field('video_file', $post_id);
  if (!$video_file_id) return;

  error_log('$video_file_id: ' . $video_file_id);

  // Get the video file path
  $video_file_path = get_attached_file($video_file_id);
  if (!file_exists($video_file_path)) {
      error_log("Video file (".$video_file_id.") not found: $video_file_path");
      return;
  }

  // Define the output thumbnail path
  $upload_dir = wp_upload_dir();
  $thumbnail_filename = basename($video_file_path, '.' . pathinfo($video_file_path, PATHINFO_EXTENSION)) . '-thumbnail.jpg';
  $thumbnail_path = $upload_dir['path'] . '/' . $thumbnail_filename;



  // Use FFmpeg to extract a thumbnail
  $ffmpeg_command = $ffmpeg_path . " -i " . escapeshellarg($video_file_path) . " -ss 00:00:14.000 -vframes 1 " . escapeshellarg($thumbnail_path);
  exec($ffmpeg_command, $output, $return_var);

  if ($return_var !== 0 || !file_exists($thumbnail_path)) {
      error_log("Failed to generate thumbnail for video: $video_file_path");
      return;
  }

  // Check the file type of the generated thumbnail
  $wp_filetype = wp_check_filetype($thumbnail_filename, null);

  // Prepare attachment data
  $attachment = [
      'post_mime_type' => $wp_filetype['type'],
      'post_title'     => sanitize_file_name($thumbnail_filename),
      'post_content'   => '',
      'post_status'    => 'inherit',
  ];

  // Insert the thumbnail into the media library
  $attachment_id = wp_insert_attachment($attachment, $thumbnail_path, $post_id);

  // Include necessary file for generating attachment metadata
  require_once(ABSPATH . 'wp-admin/includes/image.php');

  // Generate attachment metadata
  $attach_data = wp_generate_attachment_metadata($attachment_id, $thumbnail_path);
  wp_update_attachment_metadata($attachment_id, $attach_data);

  // Update the multiple_featured_images field
  $multiple_featured_images = get_post_meta($post_id, 'multiple_featured_images', true) ?: [];
  array_unshift($multiple_featured_images, $attachment_id); // Add the thumbnail as the first item
  update_post_meta($post_id, 'multiple_featured_images', $multiple_featured_images);

  update_field('trigger_thumbnail_generation', false, $post_id); // Reset the field

  error_log("Thumbnail generated and set for post ID: $post_id");
}

add_action('save_post', 'baum_generate_video_thumbnail', 20);






//
// PHP code to locate yt-dlp in $PATH
//

/**
 * Locates the full path to a command executable
 *
 * This function searches for a command in common system paths and returns
 * the full path to the executable if found. It supports both Unix/Linux/macOS
 * and Windows operating systems.
 *
 * @param string $cmd The name of the command to locate
 * @return string|null The full path to the command if found, null otherwise
 *
 * @since 1.0.0
 */
function locate_command($cmd) {
  $paths_to_check = [
    '/usr/local/bin/' . $cmd,
    '/usr/bin/' . $cmd,
    getenv('HOME') . '/.local/bin/' . $cmd,
  ];

  // Add Windows paths
  if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    $paths_to_check = array_merge($paths_to_check, [
      getenv('ProgramFiles') . '\\'.$cmd.'\\'.$cmd.'.exe',
      getenv('ProgramFiles(x86)') . '\\'.$cmd.'\\'.$cmd.'.exe',
      getenv('LOCALAPPDATA') . '\\Programs\\Python\\PythonXX\\Scripts\\'.$cmd.'.exe',
      getenv('APPDATA') . '\\Python\\PythonXX\\Scripts\\'.$cmd.'.exe',
    ]);
  }

  // Check $PATH
  $which_command = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN' ? 'where' : 'which';
  $command_output = [];
  exec($which_command . ' ' . $cmd, $command_output);
  if (!empty($command_output)) {
    $paths_to_check[] = trim($command_output[0]);
  }

  // Validate each path
  foreach ($paths_to_check as $path) {
    if (file_exists($path) && is_executable($path)) {
      return $path;
    }
  }

  return null; // Return null if not found
}

// // Usage
// $yt_dlp_path = locate_yt_dlp();

// if ($yt_dlp_path) {
//   error_log("yt-dlp found at: $yt_dlp_path");
// } else {
//   error_log("yt-dlp not found. Please install it.");
// }

// exec('echo $PATH', $output);
// error_log('PHP PATH: ' . implode(':', $output));





//
//
//

/**
 * Transfers a video from a URL to the WordPress media library
 *
 * This function is triggered when a post is saved with the 'trigger_video_transfer'
 * ACF field set to true. It downloads a video from the URL specified in the
 * 'video_url' field, adds it to the media library, and updates the post's metadata.
 *
 * @param int $post_id The ID of the post being saved
 * @param WP_Post $post The post object being saved
 * @return void
 *
 * @since 1.0.0
 */
function transfer_video_to_wp($post_id, $post) {
  $supported_sites = [
    '9gag', 'abcotvs', 'adultswim', 'aenetworks', 'americastestkitchen',
    'bloomberg', 'buzzfeed', 'cspan', 'cbsnews', 'cnn', 'comedycentral',
    'condenast', 'dailywire', 'ebaumsworld', 'espn', 'fivethirtyeight',
    'foxnews', 'hbo', 'iheartradio', 'msnbc', 'nbcnews', 'npr',
    'pbs', 'soundcloud', 'spotify', 'thenewyorker', 'theweatherchannel',
    'tmz', 'twitch', 'twitter', 'usatoday', 'vice', 'vimeo', 'worldstarhiphop',
    'wsj', 'youtube'
  ];

  if (get_field('trigger_video_transfer', $post_id)) {
      $video_url = get_field('video_url', $post_id);
      $video_type = get_field('video_type', $post_id);

      if ($video_type !== 'download') {
          error_log('Video type is not set to "download" for post ID: ' . $post_id);
          return;
      }

      if (!$video_url || !filter_var($video_url, FILTER_VALIDATE_URL)) {
          error_log('Invalid or missing video URL for post ID: ' . $post_id);
          return;
      }

      // Determine the video origin
      $video_origin = null;
      foreach ($supported_sites as $site) {
          if (strpos($video_url, $site) !== false) {
              $video_origin = $site;
              break;
          }
      }

      if (!$video_origin) {
          $video_origin = 'generic';
          error_log('Unsupported site for video transfer: ' . $video_url);
      }

      // Adjust the path to yt-dlp on your server
      // $yt_dlp_path = '/usr/local/bin/yt-dlp';
      // $yt_dlp_path = '/Users/<USER>/Library/Python/3.9/bin/yt-dlp'; // Adjust path as needed
      $yt_dlp_path = locate_command('yt-dlp');

      // Fetch video metadata using yt-dlp to get the title and other details
      $metadata_command = "$yt_dlp_path -f 'bv*+ba/best' --merge-output-format mp4 --dump-json " . escapeshellarg($video_url);
      exec($metadata_command, $metadata_output, $metadata_return_var);

      if ($metadata_return_var !== 0) {
          error_log('Failed to fetch metadata for video: ' . implode("\n", $metadata_output));
          return;
      }

      // Parse the metadata
      $metadata = json_decode(implode("\n", $metadata_output), true);
      if (!$metadata) {
          error_log('Failed to parse metadata for video.');
          return;
      }

      // Extract and sanitize the title
      $video_title = isset($metadata['title']) ? sanitize_file_name($metadata['title']) : 'untitled';

      // Define the output path
      $upload_dir = wp_get_upload_dir();
      $output_filename = $video_title . '.mp4';
      $output_path = $upload_dir['path'] . '/' . $output_filename;

      // Download the video
      // $download_command = "$yt_dlp_path -S ext:mp4:m4a -f mp4 -o " . escapeshellarg($output_path) . " " . escapeshellarg($video_url);

      $download_command = "$yt_dlp_path -f 'bv*+ba/best' --merge-output-format mp4 -o " . escapeshellarg($output_path) . " " . escapeshellarg($video_url);

      error_log('Executing command: ' . $download_command);
      exec($download_command, $download_output, $download_return_var);

      if ($download_return_var !== 0) {
          error_log('Failed to download video: ' . implode("\n", $download_output));
          return;
      }

      // Insert the downloaded video into the media library
      $attachment_id = insert_video_into_media_library($output_path, $post_id);
      if (!$attachment_id) {
          error_log('Failed to insert video into media library.');
          return;
      }

      // Store metadata in the 'video_metadata' ACF field
      update_field('video_metadata', $metadata, $post_id);

      // Update other ACF fields
      update_field('video_file', $attachment_id, $post_id);
      update_field('video_origin', $video_origin, $post_id);
      update_field('trigger_video_transfer', false, $post_id); // Reset the field
  }
}

add_action('save_post', 'transfer_video_to_wp', 10, 2);

//
//
//

/**
 * Inserts a video file into the WordPress media library
 *
 * This function creates an attachment post for a video file and
 * associates it with a specific post. It generates attachment metadata
 * and returns the new attachment ID.
 *
 * @param string $file_path The full path to the video file
 * @param int $post_id The ID of the post to associate the video with
 * @return int The attachment ID
 *
 * @since 1.0.0
 */
function insert_video_into_media_library($file_path, $post_id) {
  $filetype = wp_check_filetype(basename($file_path), null);
  $upload_dir = wp_get_upload_dir();

  $attachment = array(
    'guid'           => $upload_dir['url'] . '/' . basename($file_path),
    'post_mime_type' => $filetype['type'],
    'post_title'     => preg_replace('/\.[^.]+$/', '', basename($file_path)),
    'post_content'   => '',
    'post_status'    => 'inherit',
  );

  // Insert attachment into the media library
  $attachment_id = wp_insert_attachment($attachment, $file_path, $post_id);

  // Generate attachment metadata
  require_once(ABSPATH . 'wp-admin/includes/image.php');
  $attach_data = wp_generate_attachment_metadata($attachment_id, $file_path);
  wp_update_attachment_metadata($attachment_id, $attach_data);

  return $attachment_id;
}

//
//
//

/**
 * Displays an admin notice when an image already exists in the media library
 *
 * This function checks for a transient containing an image URL and displays
 * a warning notice with a link to the image if the transient exists.
 * The notice is dismissible and the transient is deleted after display.
 *
 * @return void
 *
 * @since 1.0.0
 */
function image_already_exists_admin_notice() {
  if ($image_url = get_transient('image_already_exists_notice')) {
    echo '<div class="notice notice-warning is-dismissible">
            <p>Image already exists in the media library: <a href="' . esc_url($image_url) . '" target="_blank">' . esc_html($image_url) . '</a></p>
          </div>';
    delete_transient('image_already_exists_notice');
    // Remove notice after displaying
  }
}

add_action('admin_notices', 'image_already_exists_admin_notice');

//
//
//

/**
 * Checks if a file already exists in the media library
 *
 * This function extracts the filename from a URL and checks if a file
 * with that name already exists in the WordPress media library.
 * It uses a database query to search for matching filenames.
 *
 * @param string $image_url The URL of the image to check
 * @return int|false The attachment ID if found, false otherwise
 *
 * @since 1.0.0
 */
function check_existing_media($image_url) {
  error_log('check_existing_media');
  global $wpdb;

  // Extract filename from the image URL
  $filename = basename(parse_url($image_url, PHP_URL_PATH));

  if (!$filename) {
    return false;
  }

  // Query the media library for an existing attachment with this filename
  $attachment_id = $wpdb->get_var(
    $wpdb->prepare(
      "SELECT post_id FROM $wpdb->postmeta WHERE meta_key = '_wp_attached_file' AND meta_value LIKE %s LIMIT 1",
      '%' . $wpdb->esc_like($filename)
    )
  );

  return $attachment_id ? intval($attachment_id) : false;
}

//
// Download URL to the Media Library and attach the saved file to to post_id
// - Provide `url` and `post_id`
//

/**
 * Downloads an image from a URL and attaches it to a post
 *
 * This function downloads an image from a URL, checks if it already exists
 * in the media library, and if not, adds it and associates it with a post.
 * If the image already exists, it sets a transient to display a notice.
 *
 * @param string $image_url The URL of the image to download
 * @param int $post_id The ID of the post to attach the image to
 * @return int|false The attachment ID if successful, false otherwise
 *
 * @since 1.0.0
 */
function download_and_attach_image($image_url, $post_id) {
  if (!$image_url) {
    return false;
  }

  // Check if the image already exists
  $existing_attachment_id = check_existing_media($image_url);
  if ($existing_attachment_id) {
    error_log('Set admin notice for existing image');
    set_transient('image_already_exists_notice', esc_url($image_url), 10);
    return $existing_attachment_id;
  }

  // Download image to temp file
  $image_data = wp_remote_get($image_url);
  if (is_wp_error($image_data)) {
    return false;
  }

  $image_content = wp_remote_retrieve_body($image_data);
  $file_name = basename(parse_url($image_url, PHP_URL_PATH));
  $upload_dir = wp_upload_dir();
  $file_path = $upload_dir['path'] . '/' . $file_name;

  file_put_contents($file_path, $image_content);

  // Insert into WordPress media library
  $file_type = wp_check_filetype($file_path, null);
  $attachment = array(
    'post_mime_type' => $file_type['type'],
    'post_title' => sanitize_file_name($file_name),
    'post_content' => '',
    'post_status' => 'inherit'
  );

  $attachment_id = wp_insert_attachment($attachment, $file_path, $post_id);
  require_once ABSPATH . 'wp-admin/includes/image.php';
  $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
  wp_update_attachment_metadata($attachment_id, $attachment_data);

  return $attachment_id;
}






















//
//
//

/**
 * Adds a "Transfer from URL" button to the WordPress media modal
 *
 * This function adds a button to the WordPress media modal that allows users
 * to import media files from external URLs directly into the media library.
 * It includes JavaScript for handling the button click and AJAX requests.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_add_transfer_button() {
  ?>
  <script type="text/javascript">
      /**
       * Media Transfer Button Integration
       *
       * This script adds a "Transfer from URL" button to the WordPress media modal.
       * It allows users to import media files from external URLs directly into the media library.
       *
       * @since 1.0.0
       */
      jQuery(document).ready(function($) {
          /**
           * Adds the "Transfer from URL" button when the media modal opens
           * Uses a timeout to ensure the modal is fully loaded
           */
          $(document).on('click', '.insert-media', function() {
              // Delay to ensure the media modal DOM is fully loaded
              setTimeout(function() {
                  // Check if the button already exists
                  if ($('#baum-transfer-url-btn').length === 0) {
                      // Add the "Transfer from URL" button
                      $('.media-frame-router').append('<a id="baum-transfer-url-btn" class="media-menu-item" href="#">Transfer from URL</a>');
                  }
              }, 500);
          });

          /**
           * Handles the "Transfer from URL" button click
           * Creates and displays the URL input form
           *
           * @param {Event} event - Click event object
           * @returns {void}
           */
          $(document).on('click', '#baum-transfer-url-btn', function(event) {
              event.preventDefault();

              // Create the "Transfer from URL" form
              var content = `
                  <div class="baum-transfer-container">
                      <h3><?php _e('Transfer Media from URL', 'baum-transfer'); ?></h3>
                      <input type="text" id="baum_transfer_url" placeholder="Enter URL..." style="width:100%; margin-bottom:10px;">
                      <button id="baum_transfer_button" class="button button-primary"><?php _e('Transfer', 'baum-transfer'); ?></button>
                      <p id="baum_transfer_status" style="margin-top:10px;"></p>
                  </div>
              `;

              // Replace media modal content with the transfer form
              $('.media-frame-content').html(content);
          });

          /**
           * Handles the file transfer via AJAX
           * Validates the URL and sends the transfer request
           *
           * @returns {void}
           */
          $(document).on('click', '#baum_transfer_button', function() {
              var url = $('#baum_transfer_url').val();
              if (!url) {
                  $('#baum_transfer_status').text('Please enter a valid URL.');
                  return;
              }

              $('#baum_transfer_status').text('Processing...');

              /**
               * Send AJAX request to transfer the file
               */
              $.ajax({
                  url: ajaxurl,
                  type: 'POST',
                  data: {
                      action: 'baum_transfer_url',
                      url: url,
                      _ajax_nonce: '<?php echo wp_create_nonce("baum_transfer_nonce"); ?>'
                  },
                  /**
                   * Handle the AJAX response
                   *
                   * @param {Object} response - AJAX response object
                   * @returns {void}
                   */
                  success: function(response) {
                      if (response.success) {
                          $('#baum_transfer_status').html('<span style="color: green;">' + response.data.message + '</span>');
                          wp.media.frame.setState('library'); // Return to media library
                      } else {
                          $('#baum_transfer_status').html('<span style="color: red;">' + response.data.message + '</span>');
                      }
                  }
              });
          });
      });
  </script>
  <?php
}

// add_action('admin_footer', 'baum_add_transfer_button');

























//
//
//

/**
 * Adds a "Transfer from URL" tab to the media uploader tabs
 *
 * This function adds a custom tab to the WordPress media uploader
 * that allows users to transfer media files from external URLs.
 *
 * @param array $tabs The existing media uploader tabs
 * @return array The modified tabs array with the new tab added
 *
 * @since 1.0.0
 */
function baum_add_transfer_tab($tabs) {
  $tabs['baum_transfer'] = __('Transfer from URL', 'baum-transfer');
  return $tabs;
}

// add_filter('media_upload_tabs', 'baum_add_transfer_tab');


/**
 * Renders the "Transfer from URL" interface in the media uploader
 *
 * This function outputs the HTML and JavaScript for the URL transfer
 * interface in the WordPress media uploader. It includes a form for
 * entering a URL and handling the AJAX request to transfer the file.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_transfer_from_url() {
  ?>
  <div class="baum-transfer-container">
    <h3><?php _e('Transfer Media from URL', 'baum-transfer'); ?></h3>
    <input type="text" id="baum_transfer_url" placeholder="Enter URL..." style="width:100%; margin-bottom:10px;">
    <button id="baum_transfer_button" class="button button-primary"><?php _e('Transfer', 'baum-transfer'); ?></button>
    <p id="baum_transfer_status" style="margin-top:10px;"></p>
  </div>

  <script type="text/javascript">
      jQuery(document).ready(function($) {
          $('#baum_transfer_button').on('click', function() {
              var url = $('#baum_transfer_url').val();
              if (!url) {
                  $('#baum_transfer_status').text('Please enter a valid URL.');
                  return;
              }

              $('#baum_transfer_status').text('Processing...');

              $.ajax({
                  url: ajaxurl,
                  type: 'POST',
                  data: {
                      action: 'baum_transfer_url',
                      url: url,
                      _ajax_nonce: '<?php echo wp_create_nonce("baum_transfer_nonce"); ?>'
                  },
                  success: function(response) {
                      if (response.success) {
                          $('#baum_transfer_status').html('<span style="color: green;">' + response.data.message + '</span>');
                          wp.media.frame.setState('library'); // Return to media library
                      } else {
                          $('#baum_transfer_status').html('<span style="color: red;">' + response.data.message + '</span>');
                      }
                  }
              });
          });
      });
  </script>
  <?php
}

// function baum_media_upload_transfer() {
//   wp_iframe('baum_transfer_from_url');
// }

// add_action('media_upload_baum_transfer', 'baum_media_upload_transfer');

















//
//
//


/**
 * AJAX handler for transferring files from URLs to the media library
 *
 * This function processes AJAX requests to transfer files from external URLs
 * to the WordPress media library. It validates the URL, determines if it's a
 * video or other file type, and calls the appropriate transfer function.
 *
 * @return void Sends JSON response and exits
 *
 * @since 1.0.0
 */
function baum_transfer_url_handler() {
  check_ajax_referer('baum_transfer_nonce', '_ajax_nonce');

  if (empty($_POST['url']) || !filter_var($_POST['url'], FILTER_VALIDATE_URL)) {
    error_log('❌ Invalid or missing URL: ' . ($_POST['url'] ?? 'No URL provided'));
    wp_send_json_error(['message' => 'Invalid URL.']);
  }

  $url = esc_url_raw($_POST['url']);
  error_log("🔄 Attempting file transfer from: " . $url);

  // List of known video platforms
  $video_sites = [
    '9gag', 'abcotvs', 'adultswim', 'aenetworks', 'americastestkitchen',
    'bloomberg', 'buzzfeed', 'cspan', 'cbsnews', 'cnn', 'comedycentral',
    'condenast', 'dailywire', 'ebaumsworld', 'espn', 'fivethirtyeight',
    'foxnews', 'hbo', 'iheartradio', 'msnbc', 'nbcnews', 'npr',
    'pbs', 'soundcloud', 'spotify', 'thenewyorker', 'theweatherchannel',
    'tmz', 'twitch', 'twitter', 'usatoday', 'vice', 'vimeo', 'worldstarhiphop',
    'wsj', 'youtube'
  ];

  $is_video = false;
  foreach ($video_sites as $site) {
      if (strpos($url, $site) !== false) {
          $is_video = true;
          break;
      }
  }

  // Handle video downloads using yt-dlp
  if ($is_video) {
    error_log("✅ Transferring Video.");
    $attachment_id = baum_transfer_video($url);
  } else {
    error_log("✅ Transferring Generic File.");
      // Handle all other files
    $attachment_id = baum_transfer_generic_file($url);
  }

  if ($attachment_id) {
      error_log("✅ File transferred successfully! Attachment ID: " . $attachment_id);
      wp_send_json_success(['message' => 'File transferred successfully!']);
  } else {
      error_log("❌ File transfer failed. No attachment ID returned.");
      wp_send_json_error(['message' => 'File transfer failed.']);
  }
}

add_action('wp_ajax_baum_transfer_url', 'baum_transfer_url_handler');







//
//
//

/**
 * Downloads a video from a URL and adds it to the media library
 *
 * This function uses yt-dlp to download videos from various platforms
 * like YouTube, Vimeo, etc. It extracts metadata, downloads the video,
 * and adds it to the WordPress media library.
 *
 * @param string $video_url The URL of the video to download
 * @return int|false The attachment ID if successful, false otherwise
 *
 * @since 1.0.0
 */
function baum_transfer_video($video_url) {
  // $yt_dlp_path = '/usr/local/bin/yt-dlp'; // Adjust path if necessary

  // $upload_dir = wp_get_upload_dir();
  // $output_filename = uniqid('video_') . '.mp4';
  // $output_path = $upload_dir['path'] . '/' . $output_filename;

  // $download_command = escapeshellcmd("$yt_dlp_path -S ext:mp4:m4a -f mp4 -o " . escapeshellarg($output_path) . " " . escapeshellarg($video_url));
  // exec($download_command, $output, $return_var);

  // if ($return_var !== 0) {
  //     error_log("❌ Error : " . print_r($output, true));
  //     return false;
  // }

    $supported_sites = [
      '9gag', 'abcotvs', 'adultswim', 'aenetworks', 'americastestkitchen',
      'bloomberg', 'buzzfeed', 'cspan', 'cbsnews', 'cnn', 'comedycentral',
      'condenast', 'dailywire', 'ebaumsworld', 'espn', 'fivethirtyeight',
      'foxnews', 'hbo', 'iheartradio', 'msnbc', 'nbcnews', 'npr',
      'pbs', 'soundcloud', 'spotify', 'thenewyorker', 'theweatherchannel',
      'tmz', 'twitch', 'twitter', 'usatoday', 'vice', 'vimeo', 'worldstarhiphop',
      'wsj', 'youtube'
    ];

    if (!$video_url || !filter_var($video_url, FILTER_VALIDATE_URL)) {
        error_log('Invalid or missing video URL for post ID: ' . $post_id);
        return;
    }

    // Determine the video origin
    $video_origin = null;
    foreach ($supported_sites as $site) {
        if (strpos($video_url, $site) !== false) {
            $video_origin = $site;
            break;
        }
    }

    if (!$video_origin) {
        $video_origin = 'generic';
        error_log('Unsupported site for video transfer: ' . $video_url);
    }

    // Adjust the path to yt-dlp on your server
    // $yt_dlp_path = '/usr/local/bin/yt-dlp';
    // $yt_dlp_path = '/Users/<USER>/Library/Python/3.9/bin/yt-dlp'; // Adjust path as needed
    $yt_dlp_path = locate_command('yt-dlp');

    // Fetch video metadata using yt-dlp to get the title and other details
    $metadata_command = "$yt_dlp_path --dump-json " . escapeshellarg($video_url);
    exec($metadata_command, $metadata_output, $metadata_return_var);

    if ($metadata_return_var !== 0) {
        error_log('Failed to fetch metadata for video: ' . implode("\n", $metadata_output));
        return;
    }

    // Parse the metadata
    $metadata = json_decode(implode("\n", $metadata_output), true);
    if (!$metadata) {
        error_log('Failed to parse metadata for video.');
        return;
    }

    // Extract and sanitize the title
    $video_title = isset($metadata['title']) ? sanitize_file_name($metadata['title']) : 'untitled';

    // Define the output path
    $upload_dir = wp_get_upload_dir();
    $output_filename = $video_title . '.mp4';
    $output_path = $upload_dir['path'] . '/' . $output_filename;

    // Download the video
    $download_command = "$yt_dlp_path -S ext:mp4:m4a -f mp4 -o " . escapeshellarg($output_path) . " " . escapeshellarg($video_url);
    error_log('Executing command: ' . $download_command);
    exec($download_command, $download_output, $download_return_var);

    if ($download_return_var !== 0) {
        error_log('Failed to download video: ' . implode("\n", $download_output));
        return;
    }

    return baum_insert_into_media_library($output_path);
}

/**
 * Transfers a file from a URL to the media library
 *
 * This function downloads a file from a URL and adds it to the WordPress
 * media library. It's used for importing external images and files.
 *
 * @param string $file_url The URL of the file to transfer
 * @return int|false The attachment ID if successful, false otherwise
 *
 * @since 1.0.0
 */
function baum_transfer_image_or_file($file_url) {
  $file_name = basename(parse_url($file_url, PHP_URL_PATH));
  $upload_dir = wp_upload_dir();
  $file_path = $upload_dir['path'] . '/' . $file_name;

  $response = wp_remote_get($file_url);
  if (is_wp_error($response)) {
      return false;
  }

  file_put_contents($file_path, wp_remote_retrieve_body($response));

  return baum_insert_into_media_library($file_path);
}

/**
 * Transfers any file type from a URL to the media library
 *
 * This function is more robust than baum_transfer_image_or_file as it
 * performs additional checks on the file type and handles errors better.
 * It downloads a file from a URL and adds it to the WordPress media library.
 *
 * @param string $file_url The URL of the file to transfer
 * @return int|false The attachment ID if successful, false otherwise
 *
 * @since 1.0.0
 */
function baum_transfer_generic_file($file_url) {
  $file_name = basename(parse_url($file_url, PHP_URL_PATH));
  $upload_dir = wp_upload_dir();
  $file_path = $upload_dir['path'] . '/' . $file_name;

  $response = wp_remote_get($file_url);
  if (is_wp_error($response)) {
      error_log("❌ Error : " . print_r($response, true));
      return false;
  }

  $file_body = wp_remote_retrieve_body($response);
  if (!$file_body) {
      return false;
  }

  // Save the file locally
  file_put_contents($file_path, $file_body);

  // Check file type
  $file_info = wp_check_filetype($file_path);
  $mime_type = $file_info['type'];

  // Handle unsupported file types
  if (!$mime_type) {
      unlink($file_path); // Delete the file if it's not valid
      return false;
  }

  return baum_insert_into_media_library($file_path);
}

/**
 * Inserts a file into the WordPress media library
 *
 * This function creates an attachment post for a file that has already
 * been saved to the uploads directory. It generates attachment metadata
 * and returns the new attachment ID.
 *
 * @param string $file_path The full path to the file in the uploads directory
 * @return int The attachment ID
 *
 * @since 1.0.0
 */
function baum_insert_into_media_library ($file_path) {
  error_log("✅ Insert into Media Library.");


  $filetype = wp_check_filetype($file_path);

  $attachment = [
      'guid'           => wp_upload_dir()['url'] . '/' . basename($file_path),
      'post_mime_type' => $filetype['type'],
      'post_title'     => sanitize_file_name(basename($file_path)),
      'post_content'   => '',
      'post_status'    => 'inherit'
  ];

  $attachment_id = wp_insert_attachment($attachment, $file_path);
  require_once ABSPATH . 'wp-admin/includes/image.php';
  $attach_data = wp_generate_attachment_metadata($attachment_id, $file_path);
  wp_update_attachment_metadata($attachment_id, $attach_data);

  return $attachment_id;
}

// //
// //
// //

// function baum_insert_into_media_library ($file_path) {
//   $filetype = wp_check_filetype($file_path);
//   $attachment = [
//       'guid' => wp_upload_dir()['url'] . '/' . basename($file_path),
//       'post_mime_type' => $filetype['type'],
//       'post_title' => sanitize_file_name(basename($file_path)),
//       'post_content' => '',
//       'post_status' => 'inherit'
//   ];

//   $attachment_id = wp_insert_attachment($attachment, $file_path);
//   require_once ABSPATH . 'wp-admin/includes/image.php';
//   $attach_data = wp_generate_attachment_metadata($attachment_id, $file_path);
//   wp_update_attachment_metadata($attachment_id, $attach_data);

//   return $attachment_id;
// }






/**
 * Adds a URL transfer interface to the media upload page
 *
 * This function adds a UI element to the media upload page that allows users
 * to transfer media files from external URLs directly into the WordPress media library.
 * It includes CSS styling and JavaScript for handling the AJAX requests.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_add_transfer_ui_to_media_page() {
  $screen = get_current_screen();
  if ($screen && $screen->id !== 'media') {
    return; // Only add UI on the "Add New Media" page
  }

  ?>
  <style>
    .baum-media-transfer {
      background: #fff;
      border: 1px solid #ddd;
      padding: 15px;
      margin: 20px 0;
      max-width: 600px;
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
    }
    .baum-media-transfer h3 {
      margin-top: 0;
    }
    #baum_media_url {
      width: 100%;
      padding: 8px;
      font-size: 14px;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    #baum_media_transfer_button {
      margin-top: 10px;
      display: block;
    }
  </style>

  <script type="text/javascript">
    /**
     * Media Transfer UI for Upload Page
     *
     * This script adds a URL transfer interface to the WordPress media upload page.
     * It allows users to import media files from external URLs directly into the media library.
     *
     * @since 1.0.0
     */
    jQuery(document).ready(function($) {
      /**
       * HTML template for the transfer UI
       * @type {string}
       */
      let transferUI = `
        <div class="baum-media-transfer">
          <h3><?php _e('Transfer Media from URL', 'baum-transfer'); ?></h3>
          <input type="text" id="baum_media_url" placeholder="Enter URL...">
          <button id="baum_media_transfer_button" class="button button-primary"><?php _e('Transfer', 'baum-transfer'); ?></button>
          <p id="baum_media_transfer_status"></p>
        </div>
      `;

      // Insert UI below the "Drop files here" section
      $('#drag-drop-area').after(transferUI);

      /**
       * Handles the transfer button click
       * Validates the URL and sends the transfer request
       *
       * @returns {void}
       */
      $('#baum_media_transfer_button').on('click', function() {
        var url = $('#baum_media_url').val();
        if (!url) {
          $('#baum_media_transfer_status').text('Please enter a valid URL.');
          return;
        }

        $('#baum_media_transfer_status').text('Processing...');

        /**
         * Send AJAX request to transfer the file
         */
        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'baum_transfer_url',
            url: url,
            _ajax_nonce: '<?php echo wp_create_nonce("baum_transfer_nonce"); ?>'
          },
          /**
           * Handle the AJAX response
           *
           * @param {Object} response - AJAX response object
           * @returns {void}
           */
          success: function(response) {
            if (response.success) {
              $('#baum_media_transfer_status').html('<span style="color: green;">' + response.data.message + '</span>');
              // Reload page after a short delay to show the new media
              setTimeout(function() {
                location.reload();
              }, 2000);
            } else {
              $('#baum_media_transfer_status').html('<span style="color: red;">' + response.data.message + '</span>');
            }
          }
        });
      });
    });
  </script>
  <?php
}

add_action('load-media-new.php', function() {
    add_action('admin_footer', 'baum_add_transfer_ui_to_media_page');
});

//
//
//

/**
 * Enables the custom transfer tab in the media uploader
 *
 * This function adds JavaScript that enables the "Transfer from URL" tab
 * in the WordPress media uploader. It handles tab switching when the user
 * clicks on the custom tab.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_enable_transfer_tab_in_media_uploader() {
  ?>
  <script type="text/javascript">
    /**
     * Media Transfer Tab Enabler
     *
     * This script enables the custom "Transfer from URL" tab in the WordPress media uploader.
     * It handles the tab switching when the user clicks on the tab.
     *
     * @since 1.0.0
     */
    jQuery(document).ready(function($) {
      /**
       * Handle clicks on media menu items
       * Switch to the transfer state when the transfer tab is clicked
       *
       * @returns {void}
       */
      $(document).on('click', '.media-menu-item', function() {
        if ($(this).attr('href') === '#baum_transfer') {
          wp.media.frame.setState('baum_transfer');
        }
      });
    });
  </script>
  <?php
}
// add_action('admin_footer', 'baum_enable_transfer_tab_in_media_uploader');











/**
 * Adds a custom "Transfer from URL" tab to the media uploader
 *
 * This function extends the WordPress media uploader by adding a custom tab
 * that allows users to transfer media files from external URLs. It overrides
 * the default MediaFrame.Select to include the new tab and its functionality.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_add_transfer_tab_script() {
  ?>
  <script type="text/javascript">
    /**
     * Media Transfer Tab Integration
     *
     * This script extends the WordPress media uploader to add a custom "Transfer from URL" tab.
     * It overrides the default MediaFrame.Select to include the new tab and its functionality.
     *
     * @since 1.0.0
     */
    jQuery(document).ready(function($) {
      // Exit early if WordPress media is not available
      if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
        return;
      }

      /**
       * Store reference to the original MediaFrame.Select
       * @type {Function}
       */
      var _originalFrame = wp.media.view.MediaFrame.Select;

      wp.media.view.MediaFrame.Select = _originalFrame.extend({
        initialize: function() {
          _originalFrame.prototype.initialize.apply(this, arguments);
          /**
           * Store reference to the frame for use in event handlers
           * @type {Object}
           */
          var frame = this;

          /**
           * Wait for the media modal to be fully initialized
           * Adds the custom tab button after a short delay
           */
          setTimeout(function () {
            if ($('.media-frame-router').length && !$('.media-menu-item[data-tab="transfer_from_url"]').length) {
              $('.media-frame-router').append(
                '<button type="button" class="media-menu-item" data-tab="transfer_from_url">Transfer from URL</button>'
              );
              console.log("Transfer from URL tab successfully added!");
            } else {
              console.log("Media modal not ready or tab already exists.");
            }
          }, 500);

          /**
           * Handle tab click to switch content properly
           * Creates the transfer form when the tab is clicked
           */
          $(document).off('click', '.media-menu-item[data-tab="transfer_from_url"]').on('click', '.media-menu-item[data-tab="transfer_from_url"]', function(event) {
            event.preventDefault();
            $('.media-menu-item').removeClass('active');
            $(this).addClass('active');

            /**
             * Set tab content inside .media-frame-content
             * Creates the HTML form for URL input
             */
            $('.media-frame-content').html(`
              <div class="baum-transfer-container">
                <h3>Transfer Media from URL</h3>
                <input type="text" id="baum_transfer_url" placeholder="Enter URL..." style="width:100%; margin-bottom:10px;">
                <button id="baum_transfer_button" class="button button-primary">Transfer</button>
                <p id="baum_transfer_status" style="margin-top:10px;"></p>
              </div>
            `);

            console.log("Switched to 'Transfer from URL' tab and loaded content.");
          });

          /**
           * Handle the "Transfer" button click
           * Validates the URL and sends the transfer request
           *
           * @returns {void}
           */
          $(document).off('click', '#baum_transfer_button').on('click', '#baum_transfer_button', function() {
            var url = $('#baum_transfer_url').val();
            if (!url) {
              $('#baum_transfer_status').text('Please enter a valid URL.');
              return;
            }
            $('#baum_transfer_status').text('Processing...');

            /**
             * Send AJAX request to transfer the file
             */
            $.ajax({
              url: ajaxurl,
              type: 'POST',
              data: {
                action: 'baum_transfer_url',
                url: url,
                _ajax_nonce: '<?php echo wp_create_nonce("baum_transfer_nonce"); ?>'
              },
              /**
               * Handle the AJAX response
               *
               * @param {Object} response - AJAX response object
               * @returns {void}
               */
              success: function(response) {
                if (response.success) {
                  $('#baum_transfer_status').html('<span style="color: green;">' + response.data.message + '</span>');
                  frame.setState('library'); // Return to media library
                } else {
                  $('#baum_transfer_status').html('<span style="color: red;">' + response.data.message + '</span>');
                }
              }
            });
          });

          console.log("Transfer from URL script loaded.");
        }
      });
    });
  </script>
  <?php
}
add_action('admin_footer', 'baum_add_transfer_tab_script');







/**
 * Adds CSS styles for the custom transfer tab in the media uploader
 *
 * This function adds inline CSS to style the "Transfer from URL" tab
 * in the WordPress media uploader. It ensures the tab is properly displayed
 * and matches the WordPress admin UI style.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_fix_transfer_tab_css() {
  ?>
  <style>
    .media-menu-item[data-tab="transfer_from_url"] {
      cursor: pointer !important;
      opacity: 1 !important;
      display: inline-block !important;
      pointer-events: auto !important;
      background: none;
      border: none;
      color: #555;
      font-size: 13px;
      padding: 10px 12px;
      margin: 0;
      font-weight: 600;
      text-transform: uppercase;
    }
    .media-menu-item.active {
      color: #007cba !important; /* WordPress blue */
      border-bottom: 2px solid #007cba;
    }
    .baum-transfer-container {
      display: block !important;
      padding: 15px;
    }
  </style>
  <?php
}
add_action('admin_head', 'baum_fix_transfer_tab_css');

























// function transfer_youtube_video_to_wp ($post_id, $post) {

//     // $command = '/usr/local/bin/ffmpeg -version';
//     // $output = [];
//     // $return_var = 0;
//     // exec($command, $output, $return_var);
//     // // error_log("FFMPEG Output: " . implode("\n", $output) . "\n");
//     // // error_log("FFMPEG Return Code: " . $return_var . "\n");
//     // if ($return_var !== 0) return;

//     if (get_field('trigger_video_transfer', $post_id)) {
//         $youtube_id = get_field('youtube_id', $post_id);

//         if (!$youtube_id) {
//             error_log('No YouTube ID found for post ID: ' . $post_id);
//             return;
//         }

//         // echo shell_exec('echo $PATH');

//         // Build the YouTube URL
//         $youtube_url = "https://www.youtube.com/watch?v=" . $youtube_id;

//         // Sanitize the URL without over-escaping
//         $youtube_url = sanitize_text_field($youtube_url);

//         // Validate the URL
//         if (!filter_var($youtube_url, FILTER_VALIDATE_URL)) {
//             error_log('Invalid YouTube URL: ' . $youtube_url);
//             return;
//         }

//         // Define the output path
//         $upload_dir = wp_get_upload_dir();
//         $output_path = $upload_dir['path'] . '/' . $youtube_id . '.mp4';

//         // Use youtube-dl command without escapeshellcmd
//         // $command = "youtube-dl -o " . escapeshellarg($output_path) . " " . escapeshellarg($youtube_url);

//         $yt_dlp_path = '/Users/<USER>/Library/Python/3.9/bin/yt-dlp';
//         $command = "$yt_dlp_path -S ext:mp4:m4a -f mp4 -o " . escapeshellarg($output_path) . " " . escapeshellarg($youtube_url);

//         error_log('Executing command: ' . $command);
//         exec($command, $output, $return_var);
//         error_log('Command output: ' . implode("\n", $output));
//         error_log('Return code: ' . $return_var);

//         // $command = "yt-dlp --no-check-certificate --verbose -o " . escapeshellarg($output_path) . " " . escapeshellarg($youtube_url);
//         // exec($command, $output, $return_var);

//         if ($return_var !== 0) {
//             error_log('Failed to download video: ' . implode("\n", $output));
//             return;
//         }

//         // Insert the downloaded video into the media library
//         $attachment_id = insert_video_into_media_library($output_path, $post_id);
//         if (!$attachment_id) {
//             error_log('Failed to insert video into media library.');
//             return;
//         }

//         // Update ACF fields
//         // update_field('video_type', 'upload', $post_id);
//         update_field('video_file', $attachment_id, $post_id);
//         update_field('trigger_video_transfer', false, $post_id); // Reset the field
//     }
// }

// add_action('save_post', 'transfer_youtube_video_to_wp', 10, 2);

// function insert_video_into_media_library ($file_path, $post_id) {
//     $filetype = wp_check_filetype(basename($file_path), null);
//     $upload_dir = wp_get_upload_dir();

//     $attachment = array(
//         'guid'           => $upload_dir['url'] . '/' . basename($file_path),
//         'post_mime_type' => $filetype['type'],
//         'post_title'     => preg_replace('/\.[^.]+$/', '', basename($file_path)),
//         'post_content'   => '',
//         'post_status'    => 'inherit',
//     );

//     // Insert attachment into the media library
//     $attachment_id = wp_insert_attachment($attachment, $file_path, $post_id);

//     // Generate attachment metadata
//     require_once(ABSPATH . 'wp-admin/includes/image.php');
//     $attach_data = wp_generate_attachment_metadata($attachment_id, $file_path);
//     wp_update_attachment_metadata($attachment_id, $attach_data);

//     return $attachment_id;
// }


/**
 * Fixes empty widget titles
 *
 * This function ensures that widgets with empty titles display properly.
 * It handles special cases for calendar, categories, and post views counter widgets,
 * returning appropriate title values based on the widget type.
 *
 * @param string $title    The widget title
 * @param array  $instance The widget instance settings
 * @param string $base     The widget ID base
 * @return string The modified widget title
 *
 * @since 1.0.0
 */
function my_repair_widgets_empty_title ($title, $instance = [], $base = 'unknown') {
  if ($base == 'calendar') {
    return '';
  }
  if ($base == 'categories') {
    if (!isset($instance['title'])) {
      return '';
    }
    if (trim($instance['title']) == '') {
      return '';
    } else {
      return trim($instance['title']);
    }
  }
  if ($base == 'post_views_counter_list_widget') {
    if (trim($instance['title']) == 'Most Viewed Posts') {
      return '';
    } else {
      return trim($instance['title']);
    }
  }
  return $title;
}

add_filter('widget_title', 'my_repair_widgets_empty_title', 10, 3);

/**
 * Schedules a cron job to expire breaking news tags
 *
 * This function schedules a single event to run the tag expiration function
 * after a specified time interval. The interval is configurable through
 * the theme customizer.
 *
 * @return void
 *
 * @since 1.0.0
 */
function schedule_tag_cron () {
  $baum_breaking_news_cron = get_theme_mod('baum_breaking_news_cron', 180);
  // wp_schedule_event(time(), $baum_breaking_news_cron, 'tag_hook');
  $time = time() + ($baum_breaking_news_cron * 60);
  wp_schedule_single_event($time, 'tag_hook');
}

if (!wp_next_scheduled('tag_hook')) {
  add_action('init', 'schedule_tag_cron');
}

/**
 * Removes the 'breaking' tag from posts
 *
 * This function is triggered by the scheduled cron job. It finds all posts
 * with the 'breaking' tag and removes that tag, effectively expiring the
 * breaking news status after a set time period.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_expire_breaking_tag () {
  $the_query = new WP_Query([ 'tag' => 'breaking' ]);
  while ($the_query->have_posts()) {
    if ($the_query->have_posts()) {
      $the_query->the_post();
      $post_id = get_the_ID();
      $post_tags = wp_get_post_terms($post_id, 'post_tag', [
        'fields' => 'slugs'
      ]);
      $pos = array_search('breaking', $post_tags);
      if (false !== $pos) {
        unset($post_tags[$pos]);
        wp_set_post_terms($post_id, $post_tags, 'post_tag');
      }
    }
  }
}

add_action('tag_hook', 'baum_expire_breaking_tag');



//
// Here is a WordPress admin script that ensures
// the featured image meets the minimum width
// requirement of 1000px. If the image is too
// small, it will prevent saving the post and
// display a dismissible admin notice.
//

/**
 * Adds JavaScript to validate featured image size in the admin
 *
 * This function adds a script to the admin footer that checks if the featured
 * image meets the minimum width requirement. It displays an error message if
 * the image is too small, helping to maintain image quality standards.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_reject_small_featured_images () {
  ?>
  <script>
  /**
   * Featured Image Size Validation
   *
   * This script validates that featured images meet the minimum width requirement.
   * It displays an error notice if the image is too small.
   *
   * @since 1.0.0
   */
  jQuery(document).ready(function ($) {
      /**
       * Checks if the featured image meets the minimum width requirement
       * Makes an AJAX request to get the image dimensions and displays an error if needed
       *
       * @returns {void}
       */
      function checkFeaturedImageSize() {
          let featuredImageId = $('#postimagediv .inside img').attr('src');
          if (!featuredImageId) return;

          let ajaxurl = "<?php echo admin_url('admin-ajax.php'); ?>";

          /**
           * AJAX request to check the featured image size
           */
          $.ajax({
              url: ajaxurl,
              type: 'POST',
              dataType: 'json',
              data: {
                  action: 'check_featured_image_size',
                  post_id: $('#post_ID').val()
              },
              /**
               * Handles the AJAX response
               * Displays an error notice if the image is too small
               *
               * @param {Object} response - AJAX response object
               * @param {boolean} response.error - Whether there's an error
               * @param {number} response.width - Current image width
               * @returns {void}
               */
              success: function (response) {
                  $('.baum-featured-image-error').remove();
                  if (response.error) {
                      let errorHtml = '<div class="notice notice-error baum-featured-image-error is-dismissible">';
                      errorHtml += '<p><strong>Featured Image Too Small:</strong> The image width must be at least ' + <?php echo BAUM_FEATURED_IMG_MIN_WIDTH; ?> + 'px. Current width: ' + response.width + 'px.</p>';
                      errorHtml += '</div>';
                      $('#poststuff').prepend(errorHtml);
                  }
              }
          });
      }

      /**
       * Run check when featured image is updated
       * Uses setTimeout to ensure the image has been fully updated in the DOM
       */
      $(document).on('click', '#set-post-thumbnail', function () {
          setTimeout(checkFeaturedImageSize, 1000);
      });

      // Run check on page load
      checkFeaturedImageSize();
  });
  </script>
  <?php
}

add_action('admin_footer', 'baum_reject_small_featured_images');

//
// Success / Error if featured image is less than BAUM_FEATURED_IMG_MIN_WIDTH
//
// TODO: Check multiplle featured images too
//

/**
 * AJAX handler for checking featured image size
 *
 * This function processes AJAX requests to check if a featured image meets
 * the minimum width requirement. It returns a JSON response with the image
 * dimensions and an error flag if the image is too small.
 *
 * @return void Sends JSON response and exits
 *
 * @since 1.0.0
 */
function baum_ajax_check_featured_image_size () {
  if (!isset($_POST['post_id'])) {
      wp_send_json_error(['error' => 'Missing post ID']);
  }

  $post_id = intval($_POST['post_id']);
  $thumbnail_id = get_post_thumbnail_id($post_id);
  if (!$thumbnail_id) {
      wp_send_json_success(['error' => false]);
  }

  $image = wp_get_attachment_metadata($thumbnail_id);
  if (!$image || !isset($image['width'])) {
      wp_send_json_success(['error' => false]);
  }

  if ($image['width'] < BAUM_FEATURED_IMG_MIN_WIDTH) {
      wp_send_json_success([
          'error' => true,
          'width' => $image['width']
      ]);
  } else {
      wp_send_json_success(['error' => false]);
  }
}

add_action('wp_ajax_check_featured_image_size', 'baum_ajax_check_featured_image_size');

/**
 * Adds titles to ACF field groups
 *
 * This function adds H2 titles to Advanced Custom Fields field groups
 * on user profile pages. It improves the organization and readability
 * of ACF fields in the WordPress admin.
 *
 * @return void
 *
 * @since 1.0.0
 */
function my_acf_add_field_group_titles() {
  global $pagenow;
  // Only run on the user profile pages.
  if ( ! in_array( $pagenow, array( 'profile.php', 'user-edit.php' ) ) ) {
      return;
  }
  ?>
  <script type="text/javascript">
  /**
   * ACF Field Group Title Normalizer
   *
   * This script adds consistent H2 titles to ACF field groups on user profile pages.
   * It ensures that all field groups have proper headings for better organization.
   *
   * @since 1.0.0
   */
  (function($) {
      $(document).ready(function(){
          /**
           * Process each ACF field group to add missing titles
           */
          $('.acf-field-group').each(function(){
              // Check if an H2 title already exists.
              if ( $(this).find('h2.acf-group-title').length === 0 ) {
                  /**
                   * Try to retrieve the group title from an existing container.
                   * Depending on your ACF version and markup, you may need to adjust this selector.
                   * @type {string}
                   */
                  var groupTitle = $(this).find('.acf-field-group-title').text();

                  // Fallback: if no text is found, try using a data attribute.
                  if (!groupTitle) {
                      groupTitle = $(this).data('name');
                  }

                  // Add the title if we found one
                  if (groupTitle) {
                      $(this).prepend('<h2 class="acf-group-title">' + groupTitle + '</h2>');
                  }
              }
          });
      });
  })(jQuery);
  </script>
  <?php
}
add_action('admin_footer', 'my_acf_add_field_group_titles');

/**
 * Organizes the WordPress admin menu
 *
 * This function sorts the admin menu items into logical groups and
 * alphabetizes them within each group. It also adds section headers
 * to improve navigation and organization of the admin interface.
 *
 * @return void
 *
 * @since 1.0.0
 */
function sort_admin_menu_by_type_and_alphabet_with_headers() {
  global $menu;

  $dashboard_group = array();
  $first_list  = array();
  $other_menu  = array();

  // Split menu items into groups.
  foreach ($menu as $menu_item) {
      if ( isset($menu_item[2]) ) {
          if ( $menu_item[2] === 'index.php' || (strpos($menu_item[2], 'baum-') !== false)) {
              // Dashboard.
              $dashboard_group[] = $menu_item;
          } elseif (
              $menu_item[2] === 'edit.php' ||               // Default Posts.
              $menu_item[2] === 'upload.php' ||               // Media.
              $menu_item[2] === 'users.php' ||                // Users.
              $menu_item[2] === 'comments.php' ||             // Comments.
              $menu_item[2] === 'edit-comments.php' ||             // Comments.
              (strpos($menu_item[2], 'workmail-inbox') !== false) ||
              (strpos($menu_item[2], 'newsletter_main_index') !== false) ||
              // (strpos($menu_item[2], 'baum-cc-syndication') !== false) ||
              strpos( $menu_item[2], 'edit.php?post_type=' ) !== false  // All Custom Post Types.
          ) {
              $first_list[] = $menu_item;
          } else {
              $other_menu[] = $menu_item;
          }
      } else {
          $other_menu[] = $menu_item;
      }
  }

  // Sort the first_list alphabetically by menu title.
  usort($first_list, function($a, $b) {
      return strcmp( strtolower($a[0]), strtolower($b[0]) );
  });
  // Sort the other_menu alphabetically by menu title.
  usort($other_menu, function($a, $b) {
      return strcmp( strtolower($a[0]), strtolower($b[0]) );
  });

  // Create header items for each group with a custom CSS class.
  $header_dashboard = array(
      0 => 'Dashboard',
      1 => 'read',
      2 => 'custom-header-dashboard',
      3 => '',
      4 => 'custom-header',
  );
  $header_first = array(
      0 => 'Posts & Users',
      1 => 'read',
      2 => 'custom-header-first',
      3 => '',
      4 => 'custom-header',
  );
  $header_other = array(
      0 => 'Other Items',
      1 => 'read',
      2 => 'custom-header-other',
      3 => '',
      4 => 'custom-header',
  );

  // Add header items at the beginning of each group if the group isn't empty.
  if (!empty($dashboard_group)) {
      array_unshift($dashboard_group, $header_dashboard);
  }
  if (!empty($first_list)) {
      array_unshift($first_list, $header_first);
  }
  if (!empty($other_menu)) {
      array_unshift($other_menu, $header_other);
  }

  // Merge the groups: Dashboard first, then default items & CPTs, then everything else.
  $menu = array_merge($dashboard_group, $first_list, $other_menu);
}

add_action('admin_menu', 'sort_admin_menu_by_type_and_alphabet_with_headers', 999);

/**
 * Adds custom CSS for admin menu headers
 *
 * This function outputs inline CSS to style the custom header items
 * in the admin menu. It makes the headers stand out visually and
 * disables pointer events to prevent clicking on them.
 *
 * @return void
 *
 * @since 1.0.0
 */
function custom_admin_menu_header_css() {
echo '<style></style>';
}

add_action('admin_head', 'custom_admin_menu_header_css');

/**
 * Replaces the newsletter menu icon with a Dashicons icon
 *
 * This function adds custom CSS to replace the default newsletter plugin
 * menu icon with a Dashicons email icon. It hides the original image and
 * inserts a pseudo-element with the appropriate icon.
 *
 * @return void
 *
 * @since 1.0.0
 */
function replace_newsletter_menu_icon() {
  ?>
  <style>
    /* Hide the original image */
    #toplevel_page_newsletter_main_index .wp-menu-image img {
      display: none;
    }
    /* Insert an email icon using Dashicons via a pseudo-element */
    #toplevel_page_newsletter_main_index .wp-menu-image:before {
      content: "\f461"; /* Dashicons email-alt icon */
      content: "\f465";
      font-family: dashicons;
      speak: none;
      font-size: 20px;
      line-height: 1;
      vertical-align: middle;
    }
  </style>
  <?php
}

add_action('admin_head', 'replace_newsletter_menu_icon');

























/**
 * Adds a "New File" submenu to the Media menu
 *
 * This function registers a custom admin page under the Media menu
 * that allows users to create various types of files, including
 * JSON data, AI-generated images, and spreadsheets.
 *
 * @return void
 *
 * @since 1.0.0
 */
add_action('admin_menu', function () {
  add_media_page(
    'New File',             // Page title
    'New File',             // Menu title
    'upload_files',         // Capability
    'baum-new-file',        // Menu slug
    'baum_render_new_file_page' // Callback function
  );
});

// Callback to render the page
/**
 * Renders the "Add New File" admin page
 *
 * This function outputs the HTML for a custom admin page that allows users
 * to create various types of files, including JSON data from external sources,
 * AI-generated images, and spreadsheets.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_render_new_file_page() {
  ?>
  <div class="wrap">
    <h1>Add New File</h1>
    <div class="baum-grid">

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-film"></i>
        <!-- <i class="fa-solid fa-file-import"></i> -->
        <i class="fa-solid fa-code"></i>
          <p>IMDB (JSON)</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-film"></i>
        <!-- <i class="fa-solid fa-file-import"></i> -->
        <i class="fa-solid fa-code"></i>
          <p>TMDB (JSON)</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-barcode"></i>
        <!-- <i class="fa-solid fa-code"></i> -->
        <!-- <i class="fa-solid fa-bookmark"></i> -->
        <i class="fa-solid fa-file-import"></i>
          <p>Book ISBN (JSON)</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <!-- <i class="fa-solid fa-robot"></i> -->
          <i class="fa-solid fa-book-atlas"></i>
          <i class="fa-solid fa-file-import"></i>
          <p>Generate AI Image</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-plus"></i>
        <i class="fa-solid fa-table-list"></i>
          <p>New Spreadsheet</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-plus"></i>
        <i class="fa-solid fa-cube"></i>
          <p>New Airtable</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-link"></i>
        <i class="fa-solid fa-cube"></i>
          <p>Link Airtable</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-plus"></i>
        <i class="fa-solid fa-file-csv"></i>
          <p>New Google Sheet</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-link"></i>
        <i class="fa-solid fa-file-csv"></i>
          <p>Link Google Sheet</p>
        </div>
      </div>


      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-plus"></i>
        <i class="fa-solid fa-file-csv"></i>
          <p>Paste .CSV</p>
        </div>
      </div>


      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-plus"></i>
        <i class="fa-solid fa-image"></i>
          <p>Generate an Image</p>

        </div>
      </div>



      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-file-csv"></i>
        <i class="fa-solid fa-file-import"></i>
          <p>Transfer .CSV</p>
        </div>
      </div>

      <!-- <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-cloud-arrow-up"></i>
        <i class="fa-solid fa-file-csv"></i>
          <p>Upload .CSV</p>
        </div>
      </div> -->

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-plus"></i>
        <i class="fa-solid fa-file-code"></i>
          <p>Paste .JSON</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-file-code"></i>
        <i class="fa-solid fa-file-import"></i>
          <p>Transfer .JSON</p>
        </div>
      </div>

      <!-- <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-cloud-arrow-up"></i>
        <i class="fa-solid fa-file-code"></i>
          <p>Upload .JSON</p>
        </div>
      </div> -->

      <!-- <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-cloud-arrow-up"></i>
        <i class="fa-solid fa-microphone-lines"></i>
          <p>Upload Audio</p>
        </div>
      </div> -->

      <div class="baum-card-container">
        <div class="baum-card">

        <i class="fa-solid fa-microphone-lines"></i>
        <i class="fa-solid fa-file-import"></i>
        <!-- <i class="fa-solid fa-microphone-lines"></i> -->
        <!-- <i class="fa-solid fa-file-audio"></i> -->
          <p>Transfer Audio</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-film"></i>
        <i class="fa-solid fa-file-import"></i>
          <p>Transfer Video</p>
        </div>
      </div>

      <!-- <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-cloud-arrow-up"></i>
        <i class="fa-solid fa-film"></i>
          <p>Upload Video</p>
        </div>
      </div> -->

      <!-- <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-plus"></i>
        <i class="fa-solid fa-film"></i>
          <p>Record New Video</p>
        </div>
      </div> -->

      <div class="baum-card-container">
        <div class="baum-card">
        <i class="fa-solid fa-plus"></i>
        <i class="fa-solid fa-microphone-lines"></i>
        <!-- <i class="fa-solid fa-file-audio"></i> -->
          <p>Record New Audio</p>
        </div>
      </div>

    </div>

    <p> <br> </p>

    <h1><i class="fa-solid fa-arrow-left"></i> Internet Movie Database API transfer to JSON</h1>

    <div class='baum-grid baum-grid-1-1'>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>
            Search for Film title:
              <input type="search" placeholder="Battlestar Galactica...">
          </p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p></p>
        </div>
      </div>

    </div>

    <p> <br> </p>

    <h1>Generate Meme, Image or Infographic</h1>

    <div class='baum-grid'>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Quote Meme</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Simple Text Meme</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Viral Social Meme</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>News Headline Graphic</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Image Vs. Image</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Sports Highlight Meme</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Stock Symbol Chart</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Weather Alert Meme</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Election Infographic</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
          <p>Countdown Meme</p>
        </div>
      </div>

      <div class="baum-card-container">
        <div class="baum-card">
        <!-- <i class="fa-solid fa-cloud-arrow-up"></i> -->
        <!-- <i class="fa-solid fa-image"></i> -->
          <p>Watermarked Graphic</p>
        </div>
      </div>

    </div>

  </div>
  <style>
    .wrap h1 i { font-size: 21px; }
    .wrap .baum-grid i { font-size: 35px; }
    .baum-grid p {
      font-size: 14px;
      font-weight: 1000;
      text-transform: uppercase;
    }
    .baum-grid {
      display: grid;
      /* grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); */
      grid-template-columns: 1fr 1fr 1fr 1fr;
      /* gap: 16px; */
      margin-top: 20px;
    }
    .baum-card {
      background: white;
      border: var(--card-outline);
      padding: 20px;
      text-align: center;
      border-radius: 8px;
      box-shadow: none;
    }
  </style>
  <?php
}


// generate_quote_image	$post_id, $quote, $author, $image_url
// generate_text_image	$post_id, $text, $alignment (random left/center)
// generate_news_headline_image	$post_id, $headline, $source, $image_url
// generate_sports_highlight_image	$post_id, $player_name, $team_name, $highlight_text
// generate_stock_market_chart	$post_id, $stock_ticker, $price_change, $chart_url
// generate_weather_alert_image	$post_id, $city, $temperature, $condition
// generate_meme_image	$post_id, $meme_text, $meme_image
// generate_election_graphic	$post_id, $poll_data, $election_date
// generate_resume_summary	$post_id, $user_id, $user_name, $user_job, $skills, $summary, $photo
// generate_countdown_image	$post_id, $event_name, $days_left, $end_date




// generate_quote_image	$post_id, $quote, $author, $image_url
// generate_text_image	$post_id, $text, $alignment (random left/center)
// generate_news_headline_image	$post_id, $headline, $source, $image_url
// generate_sports_highlight_image	$post_id, $player_name, $team_name, $highlight_text
// generate_stock_market_chart	$post_id, $stock_ticker, $price_change, $chart_url
// generate_weather_alert_image	$post_id, $city, $temperature, $condition
// generate_meme_image	$post_id, $meme_text, $meme_image
// generate_election_graphic	$post_id, $poll_data, $election_date
// generate_resume_summary	$post_id, $user_id, $user_name, $user_job, $skills, $summary, $photo
// generate_countdown_image	$post_id, $event_name, $days_left, $end_date




/**
 * Clears all WordPress cache and transients
 *
 * This function performs a complete cache flush by removing all transients
 * from the database and clearing the object cache if available. It's useful
 * for troubleshooting or after major site changes.
 *
 * @global wpdb $wpdb WordPress database abstraction object
 * @return void
 *
 * @since 1.0.0
 */
function baum_clear_all_cache_and_transients() {
  global $wpdb;

  // Delete all transients
  $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%'");

  // Delete all site transients (for multisite setups)
  $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_site_transient_%'");

  // Optionally delete object cache keys (if you're using an external object cache like Redis or Memcached)
  if (function_exists('wp_cache_flush')) {
    wp_cache_flush(); // Clear object cache
  }

    // Delete any caching plugin leftovers
    delete_option( 'wp_cache' ); // WP Super Cache
    delete_option( 'w3tc_config' ); // W3 Total Cache
    delete_option( 'advanced_cache' );
    delete_option( 'autoptimize_css' );
    delete_option( 'autoptimize_js' );
    delete_option( 'autoptimize_html' );

    echo "All transients, caches, and related options have been flushed!";
}

// Run it once
// baum_clear_all_cache_and_transients();






$logo_light = get_theme_mod( 'masthead_logo_light' );
$logo_dark = get_theme_mod( 'masthead_logo_dark' );
$logo_width = get_theme_mod( 'masthead_logo_width', 200 ); // Default to 200px if unset

































// == Spreadsheet Viewer/Editor for Media Library ==
/**
 * Enqueues spreadsheet editor scripts and styles for CSV files
 *
 * This function loads the Handsontable library for spreadsheet editing
 * when viewing a spreadsheet file in the media library. It also adds
 * the necessary JavaScript to initialize the editor and handle saving.
 *
 * @param string $hook The current admin page hook
 * @return void
 *
 * @since 1.0.0
 */
add_action('admin_enqueue_scripts', function($hook) {
  if ($hook === 'post.php' && isset($_GET['post'])) {
    $post_id = absint($_GET['post']);
    $mime = get_post_mime_type($post_id);
    if (preg_match('/(csv|spreadsheetml|excel|opendocument)/', $mime)) {
      wp_enqueue_script('handsontable', 'https://cdn.jsdelivr.net/npm/handsontable@13.0.0/dist/handsontable.min.js', [], null);
      wp_enqueue_style('handsontable', 'https://cdn.jsdelivr.net/npm/handsontable@13.0.0/dist/handsontable.min.css', [], null);

          /**
           * Adds the spreadsheet editor JavaScript to the admin footer
           *
           * This function outputs the JavaScript needed to initialize the
           * Handsontable spreadsheet editor and handle saving the data via AJAX.
           *
           * @return void
           *
           * @since 1.0.0
           */
          add_action('admin_footer', function() use ($post_id) {
            $file_url = wp_get_attachment_url($post_id);
            $nonce = wp_create_nonce('baum_save_spreadsheet');
            $ajax_url = admin_url('admin-ajax.php');
              ?>
              <script>
              /**
               * Spreadsheet Editor Integration
               *
               * This script provides a spreadsheet editor for CSV files in the WordPress admin.
               * It uses Handsontable to render and edit CSV data, with AJAX saving functionality.
               *
               * @since 1.0.0
               */
              jQuery(document).ready(function($) {
                  /**
                   * Fetch the CSV file content and initialize the spreadsheet editor
                   */
                  fetch('<?php echo esc_js($file_url); ?>')
                    .then(response => response.text())
                    .then(csv => {
                        /**
                         * Container element for the spreadsheet
                         * @type {HTMLElement}
                         */
                        const container = document.getElementById('baum-spreadsheet-container');

                        /**
                         * Parse CSV data into rows and columns
                         * @type {Array<Array<string>>}
                         */
                        const rows = csv.split('\n').map(line => line.split(','));

                        /**
                         * Handsontable instance for spreadsheet editing
                         * @type {Handsontable}
                         */
                        const hot = new Handsontable(container, {
                            data: rows,
                            rowHeaders: true,
                            colHeaders: true,
                            licenseKey: 'non-commercial-and-evaluation',
                            stretchH: 'all',
                        });

                        /**
                         * Handle saving the spreadsheet data via AJAX
                         *
                         * @returns {void}
                         */
                        $('#baum-save-sheet').on('click', function () {
                            const data = hot.getData();

                            /**
                             * Send AJAX request to save the spreadsheet data
                             */
                            $.post('<?php echo esc_url($ajax_url); ?>', {
                                action: 'baum_save_spreadsheet',
                                nonce: '<?php echo esc_js($nonce); ?>',
                                postId: '<?php echo esc_js($post_id); ?>',
                                data: JSON.stringify(data)
                            },
                            /**
                             * Handle the AJAX response
                             *
                             * @param {Object} resp - AJAX response object
                             * @param {boolean} resp.success - Whether the save was successful
                             * @param {string} resp.data - Error message if save failed
                             * @returns {void}
                             */
                            function (resp) {
                                if (resp.success) {
                                    alert('Spreadsheet saved!');
                                } else {
                                    alert('Failed to save: ' . resp.data);
                                }
                            });
                        });
                    });
              });
              </script>
              <?php
          });
      }
  }
});

// /**
//  * Adds a spreadsheet editor container after the title on spreadsheet attachments
//  *
//  * This function checks if the current post is a spreadsheet type attachment
//  * and adds the necessary HTML containers for the Handsontable spreadsheet editor.
//  *
//  * @param WP_Post $post The current post object
//  * @return void
//  *
//  * @since 1.0.0
//  */
// add_action('edit_form_after_title', function($post) {
//   $mime = get_post_mime_type($post);
//   if (preg_match('/(csv|spreadsheetml|excel|opendocument)/', $mime)) {
//     echo '<div id="baum-spreadsheet-container" style="margin:20px 0;"></div>';
//     echo '<button id="baum-save-sheet" class="button button-primary">Save Spreadsheet</button>';
//   }
// });

/**
 * AJAX handler for saving spreadsheet data
 *
 * This function processes AJAX requests to save edited spreadsheet data.
 * It verifies the nonce, checks user permissions, and writes the updated
 * data back to the original CSV file.
 *
 * @return void Sends JSON response and exits
 *
 * @since 1.0.0
 */
add_action('wp_ajax_baum_save_spreadsheet', function() {
  check_ajax_referer('baum_save_spreadsheet', 'nonce');

  $post_id = absint($_POST['postId']);
  $data = json_decode(stripslashes($_POST['data']), true);

  if (!current_user_can('edit_post', $post_id) || !is_array($data)) {
    wp_send_json_error('Unauthorized or invalid data');
  }

  $file = get_attached_file($post_id);
  $handle = fopen($file, 'w');

  foreach ($data as $row) {
    fputcsv($handle, $row);
  }

  fclose($handle);
  wp_send_json_success('Saved');
});


/**
 * Adds CSS styles for the spreadsheet editor
 *
 * This function outputs inline CSS to style the Handsontable spreadsheet
 * editor container and related elements. It ensures proper display and
 * usability of the spreadsheet interface.
 *
 * @return void
 *
 * @since 1.0.0
 */
add_action('admin_head', function() {
  echo '<style>
    #baum-spreadsheet-container {
      width: 100%;
      height: 500px;
      max-width: 100%;
      overflow: auto;
      border: 1px solid #ddd;
      margin-top: 1em;
    }

    .handsontable .ht_master {
      overflow: visible !important;
    }

    .wp-core-ui #baum-save-sheet {
      margin-top: 1em;
    }
  </style>';
});








/**
 * Adds a spreadsheet editor container after the title on spreadsheet attachments
 *
 * This function checks if the current post is a spreadsheet type attachment
 * and adds the necessary HTML containers for the Handsontable spreadsheet editor.
 *
 * @param WP_Post $post The current post object
 * @return void
 *
 * @since 1.0.0
 */
add_action('edit_form_after_title', function($post) {
  if ($post->post_type !== 'attachment') return;
  
  $mime = get_post_mime_type($post->ID);
  if (preg_match('/(csv|spreadsheetml|excel|opendocument)/', $mime)) {
    echo '<div class="postbox">';
    echo '<h2 class="hndle">Spreadsheet Editor</h2>';
    echo '<div class="inside">';
    echo '<div id="baum-spreadsheet-container" style="margin:20px 0;"></div>';
    echo '<button id="baum-save-sheet" class="button button-primary">Save Spreadsheet</button>';
    echo '</div></div>';
  }
});



/**
 * Adds a file upload button to replace media files
 *
 * This function adds an "Upload & Replace" button to the media edit screen
 * that allows users to replace an existing media file with a new one while
 * preserving the attachment ID and metadata.
 *
 * @return void
 *
 * @since 1.0.0
 */
function baum_add_replace_upload_button() {
  global $post;
  
  // Only run on attachment edit screens
  if (!$post || $post->post_type !== 'attachment') return;
  
  // Add to attachment details metabox
  add_meta_box(
    'baum-replace-file-box',
    'Replace File',
    function($post) {
      ?>
      <div class="misc-pub-section" id="baum-replace-file">
        <p>Upload a new file to replace the current one. The new file must be of the same type category.</p>
        <div id="baum-file-upload-container">
          <input type="file" id="baum_replace_file" name="baum_replace_file" accept="*/*" />
          <button type="button" id="baum_replace_button" class="button button-primary">Replace File</button>
          <span id="baum-upload-status"></span>
        </div>
      </div>
      
      <script>
      jQuery(document).ready(function($) {
        $('#baum_replace_button').on('click', function() {
          var fileInput = document.getElementById('baum_replace_file');
          if (!fileInput.files.length) {
            alert('Please select a file first.');
            return;
          }
          
          var file = fileInput.files[0];
          var formData = new FormData();
          formData.append('action', 'baum_replace_file');
          formData.append('post_id', '<?php echo esc_js($post->ID); ?>');
          formData.append('baum_replace_file', file);
          formData.append('baum_replace_file_nonce', '<?php echo esc_js(wp_create_nonce('baum_replace_file_action')); ?>');
          
          $('#baum-upload-status').text('Uploading...');
          
          $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
              if (response.success) {
                $('#baum-upload-status').html('<span style="color:green">File replaced successfully! Refreshing...</span>');
                setTimeout(function() {
                  window.location.reload();
                }, 1500);
              } else {
                $('#baum-upload-status').html('<span style="color:red">Error: ' + response.data + '</span>');
              }
            },
            error: function() {
              $('#baum-upload-status').html('<span style="color:red">Upload failed. Please try again.</span>');
            }
          });
        });
      });
      </script>
      <?php
    },
    'attachment',
    'side',
    'high'
  );
}
add_action('add_meta_boxes', 'baum_add_replace_upload_button');

/**
 * AJAX handler for file replacement
 *
 * This function processes AJAX requests to replace a file.
 * It verifies the nonce, checks user permissions, and replaces the file
 * while preserving the attachment ID and metadata.
 *
 * @return void Sends JSON response and exits
 *
 * @since 1.0.0
 */
function baum_ajax_replace_file() {
  // Verify nonce
  check_ajax_referer('baum_replace_file_action', 'baum_replace_file_nonce');
  
  // Get post ID
  $post_id = isset($_POST['post_id']) ? absint($_POST['post_id']) : 0;
  if (!$post_id) {
    wp_send_json_error('Invalid post ID');
    return;
  }
  
  // Check permissions
  if (!current_user_can('edit_post', $post_id)) {
    wp_send_json_error('You do not have permission to edit this file');
    return;
  }
  
  // Check if file was uploaded
  if (!isset($_FILES['baum_replace_file']) || $_FILES['baum_replace_file']['error'] !== UPLOAD_ERR_OK) {
    wp_send_json_error('File upload failed with error: ' . ($_FILES['baum_replace_file']['error'] ?? 'Unknown error'));
    return;
  }
  
  // Get file information
  $old_file = get_attached_file($post_id);
  $old_mime = get_post_mime_type($post_id);
  
  // Check new file type
  $new_file = $_FILES['baum_replace_file'];
  $new_type = wp_check_filetype(basename($new_file['name']));
  
  // Ensure file types match (at least the general category)
  $old_category = explode('/', $old_mime)[0]; // e.g., 'image' from 'image/jpeg'
  $new_category = explode('/', $new_type['type'])[0];
  
  if ($old_category !== $new_category) {
    wp_send_json_error("File type mismatch. Original is {$old_category}, but uploaded file is {$new_category}.");
    return;
  }
  
  // Replace the file
  $upload_dir = wp_upload_dir();
  $new_file_path = $upload_dir['path'] . '/' . basename($old_file);
  
  // Move the uploaded file to the correct location
  if (move_uploaded_file($new_file['tmp_name'], $new_file_path)) {
    // Update attachment metadata
    $attachment_data = wp_generate_attachment_metadata($post_id, $new_file_path);
    wp_update_attachment_metadata($post_id, $attachment_data);
    
    // Clear any caches
    clean_attachment_cache($post_id);
    
    wp_send_json_success('File replaced successfully');
  } else {
    wp_send_json_error('Failed to move uploaded file');
  }
}
add_action('wp_ajax_baum_replace_file', 'baum_ajax_replace_file');





// /**
//  * Adds a file upload button to replace media files
//  *
//  * This function adds an "Upload & Replace" button to the media edit screen
//  * that allows users to replace an existing media file with a new one while
//  * preserving the attachment ID and metadata.
//  *
//  * @return void
//  *
//  * @since 1.0.0
//  */
// function baum_add_replace_upload_button() {
//   global $post;
  
//   // Only run on attachment edit screens
//   if (!$post || $post->post_type !== 'attachment') return;
  
//   // Add to attachment details metabox
//   add_meta_box(
//     'baum-replace-file-box',
//     'Replace File',
//     function($post) {
//       wp_nonce_field('baum_replace_file_action', 'baum_replace_file_nonce');
//       echo '<div class="misc-pub-section" id="baum-replace-file">';
//       echo '<p>Upload a new file to replace the current one. The new file must be of the same type category.</p>';
//       echo '<input type="file" id="baum_replace_file" name="baum_replace_file" accept="*/*" />';
//       echo '</div>';
//     },
//     'attachment',
//     'side',
//     'high'
//   );
// }
// add_action('add_meta_boxes', 'baum_add_replace_upload_button');

// //
// // Add "Upload & Replace" file input on Edit Media screen
// //

// /**
//  * Adds a file upload button to replace media files
//  *
//  * This function adds an "Upload & Replace" button to the media edit screen
//  * that allows users to replace an existing media file with a new one while
//  * preserving the attachment ID and metadata.
//  *
//  * @global WP_Post $post The current post object
//  * @return void
//  *
//  * @since 1.0.0
//  */
// function baum_add_replace_upload_button() {
//   global $post;
//   if ($post->post_type !== 'attachment') return;

//   echo '<div class="misc-pub-section" id="baum-replace-file">';
//   echo '<label for="baum_replace_file">Upload & Replace</label><br>';
//   echo '<input type="file" id="baum_replace_file" name="baum_replace_file" accept="*/*" />';
//   echo '</div>';

//   wp_nonce_field('baum_replace_file_action', 'baum_replace_file_nonce');
// }

// add_action('post_submitbox_misc_actions', 'baum_add_replace_upload_button');


/**
 * Adds debug information to help troubleshoot media editing features
 *
 * This function outputs information about the current post and MIME type
 * to help identify why certain features might not be appearing.
 *
 * @return void
 *
 * @since 1.0.0
 */
add_action('admin_notices', function() {
  global $post;
  $screen = get_current_screen();
  
  // Only show on attachment edit screens
  if (!$post || $post->post_type !== 'attachment' || $screen->base !== 'post') return;
  
  $mime = get_post_mime_type($post->ID);
  $file_path = get_attached_file($post->ID);
  $file_ext = pathinfo($file_path, PATHINFO_EXTENSION);
  
  echo '<div class="notice notice-info is-dismissible">';
  echo '<p><strong>Debug Info:</strong></p>';
  echo '<ul>';
  echo '<li>Post ID: ' . esc_html($post->ID) . '</li>';
  echo '<li>MIME Type: ' . esc_html($mime) . '</li>';
  echo '<li>File Extension: ' . esc_html($file_ext) . '</li>';
  echo '<li>File Path: ' . esc_html($file_path) . '</li>';
  echo '</ul>';
  echo '</div>';
});

//
// Handle the uploaded replacement file
//

// /**
//  * Handles the replacement of a media file with a new upload
//  *
//  * This function processes the uploaded replacement file, verifies it's the same
//  * type category as the original, and replaces the file while preserving the
//  * attachment ID and updating metadata appropriately.
//  *
//  * @param int $post_id The ID of the attachment being edited
//  * @return void
//  *
//  * @since 1.0.0
//  */
// function baum_handle_replace_upload($post_id) {
//   if (!isset($_FILES['baum_replace_file']) || empty($_FILES['baum_replace_file']['tmp_name'])) return;
//   if (!isset($_POST['baum_replace_file_nonce']) || !wp_verify_nonce($_POST['baum_replace_file_nonce'], 'baum_replace_file_action')) return;

//   $old_file_path = get_attached_file($post_id);
//   $old_ext = strtolower(pathinfo($old_file_path, PATHINFO_EXTENSION));
//   $old_mime = get_post_mime_type($post_id);
//   $old_type_group = baum_mime_type_group($old_mime);

//     $uploaded_file = $_FILES['baum_replace_file'];
//     $new_ext = strtolower(pathinfo($uploaded_file['name'], PATHINFO_EXTENSION));

//     require_once ABSPATH . 'wp-admin/includes/file.php';
//     $check_info = wp_check_filetype_and_ext($uploaded_file['tmp_name'], $uploaded_file['name']);
//     $new_mime = $check_info['type'];
//     $new_type_group = baum_mime_type_group($new_mime);

//     // Require same MIME group (image → image, etc.)
//     if ($old_type_group !== $new_type_group) {
//         $args = [
//             'baum_replace_error' => 'category_mismatch',
//             'expected_type' => $old_type_group,
//             'received_type' => $new_type_group,
//             'expected_mime' => urlencode($old_mime),
//             'received_mime' => urlencode($new_mime)
//         ];
//         add_filter('redirect_post_location', function($location) use ($args) {
//             return add_query_arg($args, $location);
//         });
//         return;
//     }

//     // Proceed with replacement
//     $overrides = ['test_form' => false];
//     $file_info = wp_handle_upload($uploaded_file, $overrides);

//     if (isset($file_info['error'])) {
//         error_log('File replace error: ' . $file_info['error']);
//         return;
//     }

//     if (file_exists($old_file_path)) {
//         unlink($old_file_path);
//     }

//     update_attached_file($post_id, $file_info['file']);
//     wp_update_attachment_metadata($post_id, wp_generate_attachment_metadata($post_id, $file_info['file']));
//     wp_update_post([
//         'ID' => $post_id,
//         'post_mime_type' => $new_mime,
//     ]);
// }

// add_action('edit_attachment', 'baum_handle_replace_upload');






//
// Determine the general MIME type category
//

/**
 * Determines the general category of a MIME type
 *
 * This function categorizes MIME types into general groups like 'image',
 * 'audio', 'video', etc. It's used to ensure that replacement files
 * are of the same general type as the original.
 *
 * @param string $mime The MIME type to categorize
 * @return string The general category of the MIME type
 *
 * @since 1.0.0
 */
function baum_mime_type_group($mime) {
  if (strpos($mime, 'image/') === 0) return 'image';
  if (strpos($mime, 'audio/') === 0) return 'audio';
  if (strpos($mime, 'video/') === 0) return 'video';
  if (strpos($mime, 'text/') === 0) return 'text';
  if (in_array($mime, ['application/pdf'])) return 'pdf';
  if (in_array($mime, ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])) return 'document';
  if (in_array($mime, ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'])) return 'spreadsheet';
  return 'other';
}

//
// Show admin notice if mismatch occurs
//

/**
 * Displays an admin notice when there's a file type mismatch during replacement
 *
 * This function checks for the 'baum_replace_error' query parameter and displays
 * a detailed error message when a user attempts to replace a file with one of
 * a different type category.
 *
 * @return void
 *
 * @since 1.0.0
 */
add_action('admin_notices', function() {
  if (isset($_GET['baum_replace_error']) && $_GET['baum_replace_error'] === 'category_mismatch') {
    $expected = sanitize_text_field($_GET['expected_type'] ?? '');
    $received = sanitize_text_field($_GET['received_type'] ?? '');
    $expected_mime = sanitize_text_field(urldecode($_GET['expected_mime'] ?? ''));
    $received_mime = sanitize_text_field(urldecode($_GET['received_mime'] ?? ''));

    echo '<div class="notice notice-error is-dismissible">';
    echo '<p><strong>Upload & Replace Failed:</strong> You must upload the same category of file.</p>';
    echo '<ul>';
    echo "<li>Expected Category: <code>$expected</code> ($expected_mime)</li>";
    echo "<li>Received Category: <code>$received</code> ($received_mime)</li>";
    echo '</ul>';
    echo '</div>';
  }
});
























// // Replace user display name with avatar in admin tables
// add_filter('manage_users_columns', function ($columns) {
//   $columns['avatar'] = __('Avatar');
//   return $columns;
// }, 10, 1);

// add_filter('manage_users_custom_column', function ($value, $column_name, $user_id) {
//   if ($column_name === 'avatar') {
//       return get_avatar($user_id, 32);
//   }
//   return $value;
// }, 10, 3);

// // Optional: remove the Display Name column if desired
// add_filter('manage_users_columns', function ($columns) {
//   unset($columns['name']); // 'name' is the display name column
//   return $columns;
// });




// // Change the author column heading (optional)
// add_filter('manage_posts_columns', function ($columns) {
//   if (isset($columns['author'])) {
//       $columns['author'] = __('Author Avatar');
//   }
//   return $columns;
// });

// // Replace the author column content with the avatar
// add_action('manage_post_posts_custom_column', function ($column_name, $post_id) {
//   error_log('$column_name, $post_id ' . $column_name, $post_id); 
//   if ($column_name === 'author') {
//       $author_id = get_post_field('post_author', $post_id);
//       echo get_avatar($author_id, 32);
//   }
// }, 10, 2);





// // Repeat for custom post types as needed
// add_filter('manage_page_posts_columns', function ($columns) {
//   if (isset($columns['author'])) {
//       $columns['author'] = __('Author Avatar');
//   }
//   return $columns;
// });

// add_action('manage_page_posts_custom_column', function ($column_name, $post_id) {
//   if ($column_name === 'author') {
//       $author_id = get_post_field('post_author', $post_id);
//       echo get_avatar($author_id, 32);
//   }
// }, 10, 2);




// // Optional: apply to all public post types
// add_action('current_screen', function () {
//   $screen = get_current_screen();
//   if (strpos($screen->id, 'edit-') === 0) {
//       $post_type = str_replace('edit-', '', $screen->id);

//       add_filter("manage_{$post_type}_posts_columns", function ($columns) {
//           if (isset($columns['author'])) {
//               $columns['author'] = __('Author Avatar');
//           }
//           return $columns;
//       });

//       add_action("manage_{$post_type}_posts_custom_column", function ($column_name, $post_id) {
//           if ($column_name === 'author') {
//               $author_id = get_post_field('post_author', $post_id);
//               echo get_avatar($author_id, 32);
//           }
//       }, 10, 2);
//   }
// });


// // Replace default Author column with a custom Avatar column
// add_filter('manage_posts_columns', function ($columns) {
//   // Remove the original author column
//   if (isset($columns['author'])) {
//       unset($columns['author']);
//   }

//   // Add a new avatar-based author column
//   $columns['baum_author'] = __('Author Avatar');
//   return $columns;
// }, 10, 1);

// add_action('manage_posts_custom_column', function ($column_name, $post_id) {
//   if ($column_name === 'baum_author') {
//       $author_id = get_post_field('post_author', $post_id);
//       echo do_shortcode("[baum_user_tooltip id={$author_id} size=32]");
//   }
// }, 10, 2);



add_action('current_screen', function () {
  $screen = get_current_screen();
  if (strpos($screen->id, 'edit-') !== 0) return;

  $post_type = str_replace('edit-', '', $screen->id);

  add_filter("manage_{$post_type}_posts_columns", function ($columns) {
      unset($columns['author']);
      $columns['baum_author'] = __('Author(s)');
      return $columns;
  });

  add_action("manage_{$post_type}_posts_custom_column", function ($column_name, $post_id) {
      if ($column_name === 'baum_author') {
          $author_id = get_post_field('post_author', $post_id);
          echo do_shortcode("[baum_user_tooltip id={$author_id} size=32]");
      }
  }, 10, 2);
});

// echo get_avatar($author_id, 32);

add_action('admin_enqueue_scripts', 'baum_enqueue_tooltipster');
add_action('wp_enqueue_scripts', 'baum_enqueue_tooltipster');

function baum_enqueue_tooltipster() {
    wp_enqueue_style('tooltipster-css', 'https://cdn.jsdelivr.net/npm/tooltipster@4.2.8/dist/css/tooltipster.bundle.min.css');
    wp_enqueue_script('tooltipster-js', 'https://cdn.jsdelivr.net/npm/tooltipster@4.2.8/dist/js/tooltipster.bundle.min.js', ['jquery'], null, true);

    wp_add_inline_script('tooltipster-js', <<<JS
    jQuery(function($) {
        $('.baum-user-tooltip-trigger').tooltipster({
            contentCloning: true,
            theme: 'tooltipster-borderless',
            interactive: true,
            delay: 100,
            side: [ 'right', 'left' ]
        });
    });
    JS);
}

