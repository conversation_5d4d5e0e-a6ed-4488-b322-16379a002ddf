<?php 

function baum_author_tooltip_shortcode($atts) {
  $atts = shortcode_atts(
      array('id' => 0), // Default to no author ID
      $atts,
      'baum_author_tooltip'
  );

  $author_id = intval($atts['id']);
  if (!$author_id) return 'No author information available.';

  $author = get_userdata($author_id);
  if (!$author) return 'Author not found.';

  // Format author information
  $html = '<div class="author-tooltip">';
  $html .= '<h3>' . esc_html($author->display_name) . '</h3>';
  $html .= '<p>' . esc_html(get_the_author_meta('description', $author_id)) . '</p>';
  $html .= '<p><strong>Email:</strong> ' . esc_html($author->user_email) . '</p>';
  $html .= '</div>';

  return $html;
}

add_shortcode('baum_author_tooltip', 'baum_author_tooltip_shortcode');

/**
 * [baum_story_collection 
 *   query="specific_term" 
 *   slug="republican-101" 
 *   quantity="7" 
 *   size="text" 
 *   width="100%" 
 *   float="right" 
 *   columns="2" 
 *   title="Suggested Story Collection"]
 * 
 * Shortcode to display story collections
 *
 * @param array $atts Attributes passed to the shortcode.
 * 
 * @return string HTML output for the story collection.
 */

function baum_story_collection_shortcode ($atts) {
  global $post;
  global $do_not_duplicate;

  // Parse shortcode attributes
  $atts = shortcode_atts([
      'title' => '',         // Title of the widget/shortcode output
      'query' => 'current_post', // Query type: 'current_post', 'specific_term', or 'list_terms'
      'slug' => '',          // Term slug for 'specific_term'
      'quantity' => 9,      // Maximum number of terms or posts to display
      'size' => 'mini',      // Display size: 'mini' (default) or other sizes
      'float' => '',         // Align to left, center, right
      'width' => '100%',     // Accepts 370px, 50%, 10vw, etc.
      'columns' => 3,        // Number of columns: 1, 2, or 3
  ], $atts, 'baum_story_collection');

  $output = '';

  // Create wrapper style for float and width
  $style = '';
  if (!empty($atts['float'])) {
      $style .= 'float: ' . esc_attr($atts['float']) . '; ';
  }

  if ($atts['float'] == 'left') {
      $style .= 'margin-right: 10px; ';
  } else if ($atts['float'] == 'right') {
      $style .= 'margin-left: 10px; ';
  }

  if (!empty($atts['width'])) {
      $style .= 'width: ' . esc_attr($atts['width']) . '; ';
  }

  // Ensure columns is within valid range (1, 2, or 3)
  $columns = max(1, min(3, intval($atts['columns'])));

  // Render based on query type
  if ($atts['query'] === 'current_post' && is_single()) {
      $terms = get_the_terms($post, 'story-collection');
      if ($terms) {
          // Wrapper div to apply the styles
          $output .= '<div class="baum-story-collection-wrapper" style="' . esc_attr($style) . '">';

          // Add the title if provided
          if (!empty($atts['title'])) {
            $output .= '<div class="baum-title-width baum-text-standard">';
            $output .= '<div class="baum-bg baum-width-100 secondary">';
            $output .= '<h5 class="baum-heading baum-collection-title">';
            $output .= esc_html($atts['title']); 
            $output .= '</h5>';
            $output .= '</div>';
            $output .= '</div>';
          }

          foreach ($terms as $term) {
              $do_not_duplicate[] = $term->term_id;

              $output .= "<div class='baum-story-collection' style='display:grid;grid-template-columns:repeat($columns,1fr);gap:10px;'>";
              ob_start();
              get_template_part('parts/baum', 'collections', [
                  'terms' => [$term],
                  'size' => $atts['size']
              ]);
              $output .= ob_get_clean();

              ob_start();
              get_template_part('parts/baum', 'collection', [
                  'term' => $term,
                  'size' => $atts['size'],
                  'posts_per_page' => $atts['quantity']
              ]);
              $output .= ob_get_clean();

              $output .= "</div>";
          }
          // Close the wrapper div 
          $output .= '</div>'; 
      }
  } elseif ($atts['query'] === 'specific_term' && $atts['slug']) {
      $term = get_term_by('slug', $atts['slug'], 'story-collection');
      if ($term) {

          // Wrapper div to apply the styles
          $output .= '<div class="baum-story-collection-wrapper" style="' . esc_attr($style) . '">';

          // Add the title if provided
          if (!empty($atts['title'])) {
            $output .= '<div class="baum-title-width baum-text-standard">';
            $output .= '<div class="baum-bg baum-width-100 secondary">';
            $output .= '<h5 class="baum-heading baum-collection-title">';
            $output .= esc_html($atts['title']); 
            $output .= '</h5>';
            $output .= '</div>';
            $output .= '</div>';
          }

          $do_not_duplicate[] = $term->term_id;

          $output .= "<div class='baum-story-collection' style='display: grid; grid-template-columns: repeat($columns, 1fr);'>";
          ob_start();
          get_template_part('parts/baum', 'collections', [
              'terms' => [$term],
              'size' => $atts['size']
          ]);
          $output .= ob_get_clean();

          ob_start();
          get_template_part('parts/baum', 'collection', [
              'term' => $term,
              'size' => $atts['size'],
              'posts_per_page' => $atts['quantity']
          ]);
          $output .= ob_get_clean();

          $output .= "</div>";

          // Close the wrapper div 
          $output .= '</div>'; 
      }
  } elseif ($atts['query'] === 'list_terms') {
      $terms = get_terms([
          'taxonomy' => 'story-collection',
          'posts_per_page' => $atts['quantity'], 
          'exclude' => $do_not_duplicate, 
          'hide_empty' => true, // Ensures terms without posts are excluded
          'number' => $atts['quantity'],
          'meta_query' => [
              [
                  'key' => 'featured_image',
                  'compare' => 'EXISTS', // Ensures terms have a 'featured_image'
              ],
          ],
      ]);

      if ($terms && !is_wp_error($terms)) {

          // Wrapper div to apply the styles
          $output .= '<div class="baum-story-collection-wrapper" style="' . esc_attr($style) . '">';

          // Add the title if provided
          if (!empty($atts['title'])) {
            $output .= '<div class="baum-title-width baum-text-standard">';
            $output .= '<div class="baum-bg baum-width-100 secondary">';
            $output .= '<h5 class="baum-heading baum-collection-title">';
            $output .= esc_html($atts['title']); 
            $output .= '</h5>';
            $output .= '</div>';
            $output .= '</div>';
          }

          $output .= "<div class='baum-story-collection' style='display: grid; grid-template-columns: repeat($columns, 1fr);'>";
          ob_start();
          get_template_part('parts/baum', 'collections', [
              'terms' => $terms,
              'size' => $atts['size']
          ]);
          $output .= ob_get_clean();
          $output .= '</div>';

          // Close the wrapper div 
          $output .= '</div>'; 
      }
  } else {
      $output .= '<!-- ';
      $output .= esc_html__('No valid option selected.', 'baum');
      $output .= ' -->';
  }

  return $output;
}

add_shortcode('baum_story_collection', 'baum_story_collection_shortcode');

/**
 * Shortcode to display the site logo using a template part.
 * Example: [baum_logo]
 *
 * @param array $atts Shortcode attributes:
 *   - 'date' (bool): Whether to pass the date to the template part.
 *   - 'theme' (string): 'light', 'dark', or 'auto'. Defaults to 'auto'.
 * @return string HTML output from the template part.
 */

function baum_logo_shortcode($atts) {
  // Set default attributes.
  $atts = shortcode_atts([
    'date' => false,
    'theme' => 'auto', // light, dark, or auto
  ], $atts);

  // Load the template part and pass the date and theme attributes.
  ob_start();
  get_template_part('parts/baum-logo', null, array(
    'date' => filter_var($atts['date'], FILTER_VALIDATE_BOOLEAN),
    'theme' => $atts['theme']
  ));
  return ob_get_clean();
}

add_shortcode('baum_logo', 'baum_logo_shortcode');

/**
 * Shortcode to display the custom app logo.
 * Example: [baum_app_logo]
 * 
 * @param array $atts Shortcode attributes:
 *   - 'width' (int): Width of the logo in pixels (optional).
 *   - 'height' (int): Height of the logo in pixels (optional).
 *   - 'theme' (string): 'light', 'dark', or 'auto'. Defaults to 'auto'.
 * @return string HTML <img> tag for the app logo.
 */

function baum_app_logo_shortcode($atts) {
  // Set default attributes.
  $atts = shortcode_atts([
    'width' => '',
    'height' => '',
    'theme' => 'auto',
  ], $atts);

  // Retrieve the appropriate app logo based on the theme option.
  return baum_get_logo_or_icon('baum_app_logo', 'baum_app_logo_dark', $atts);
}

add_shortcode('baum_app_logo', 'baum_app_logo_shortcode');

/**
 * Shortcode to display the custom site icon.
 * Example: [baum_icon]
 *
 * @param array $atts Shortcode attributes:
 *   - 'width' (int): Width of the icon in pixels (optional).
 *   - 'height' (int): Height of the icon in pixels (optional).
 *   - 'theme' (string): 'light', 'dark', or 'auto'. Defaults to 'auto'.
 * @return string HTML <img> tag for the site icon.
 */

function baum_icon_shortcode($atts) {
  // Set default attributes.
  $atts = shortcode_atts([
    'width' => '',
    'height' => '',
    'theme' => 'auto',
  ], $atts);

  // Retrieve the appropriate site icon based on the theme option.
  return baum_get_logo_or_icon('baum_icon', 'baum_icon_dark', $atts);
}

add_shortcode('baum_icon', 'baum_icon_shortcode');

/**
 * Utility function to retrieve the appropriate logo or icon based on the theme option.
 *
 * @param string $setting_light The Customizer setting ID for the light version.
 * @param string $setting_dark The Customizer setting ID for the dark version.
 * @param array  $atts Shortcode attributes for theme and dimensions (width/height).
 * @return string HTML img tag for the logo or icon.
 */
function baum_get_logo_or_icon ($setting_light, $setting_dark, $atts) {
  // Determine the theme option (light, dark, or auto).
  $theme = isset($atts['theme']) ? $atts['theme'] : 'auto';
  $url = '';

  // Select the appropriate logo or icon based on the theme.
  if ($theme === 'light') {
    $url = get_theme_mod($setting_light);
  } elseif ($theme === 'dark') {
    $url = get_theme_mod($setting_dark);
  } elseif ($theme === 'auto') {
    // Auto detection logic (default to light for now; can be expanded for user/system preferences).
    $url = get_theme_mod($setting_light);
  }

  // If no URL is set in the Customizer, return an empty string.
  if (!$url) return '';

  // Generate inline styles based on width and height attributes.
  $style = '';
  if (!empty($atts['width']) && empty($atts['height'])) {
    $style = "style='width: {$atts['width']}px;height:auto;background:transparent;width:64px;height:auto;background:var(--color-secondary);border-radius:16px;margin:0px;padding:7.5px;'";
  } elseif (!empty($atts['height']) && empty($atts['width'])) {
    $style = "style='height: {$atts['height']}px;width:auto;background:transparent;width:64px;height:auto;background:var(--color-secondary);border-radius:16px;margin:0px;padding:7.5px;'";
  } elseif (!empty($atts['width']) && !empty($atts['height'])) {
    $style = "style='max-width: {$atts['width']}px; max-height: {$atts['height']}px;background:transparent;width:64px;height:auto;background:var(--color-secondary);border-radius:16px;margin:0px;padding:7.5px;'";
  }

  // Return the HTML <img> tag for the logo or icon.
  return "<img src='{$url}' alt='' {$style} />";
}

/** 
 * [site_icon] — Site Icon shortcode 
 * 
 */ 

 add_shortcode('site_icon', function () { 

  // Get the site icon URL
  $site_icon_url = get_site_icon_url();

  // Check if the site icon exists and display it
  if ($site_icon_url) {
    $output = '<img src="' . esc_url($site_icon_url) . '" alt="' . get_bloginfo() . '" title="' . get_bloginfo() . '" style="background:none;display:inline-block;height:20px;width:auto;vertical-align:text-bottom;margin-left:2.5px;margin-right:2.5px;" />';
  } else {
    $output = '';
  }
  return $output; 
}); 

/**
* Shortcode for displaying the site logo with customizable size and dark mode.
*
* @param array $atts - Optional. An array of shortcode attributes.
* @type string $size - The size. Accepts 'small' or 'full'. Default 'full'.
* @type string $darkmode - Accepts 'false', 'true', or 'auto'. Default 'false'.
* 
* @return string HTML output of the site logo, optionally with dark mode CSS.
*/

function baum_site_logo_shortcode ($atts) {
  $atts = shortcode_atts([ 
    'size' => 'full', 
    'darkmode' => 'false', 
  ], $atts, 'baum_site_logo'); 

  $size = esc_attr($atts['size']); 
  $darkmode = esc_attr($atts['darkmode']); 
  $logo_id = get_theme_mod('custom_logo'); 
  $logo = wp_get_attachment_image_src($logo_id, $size); 
  if (!$logo) return ''; 
  $logo_url = $logo[0]; 
  $darkmode_css = ''; 

  if ($darkmode === 'true' || $darkmode === 'auto') {
    $dark_logo_id = get_theme_mod('custom_logo_dark');
    $dark_logo = wp_get_attachment_image_src($dark_logo_id, $size);
    $dark_logo_url = $dark_logo ? $dark_logo[0] : $logo_url;
    
    if ($darkmode === 'true') {
      $logo_url = $dark_logo_url;
    } elseif ($darkmode === 'auto') {
      $darkmode_css = "
        <style>
          @media (prefers-color-scheme: dark) {
            .baum-site-logo {
              content: url('{$dark_logo_url}');
            }
          }
        </style>
      ";
    }
  }
  return "{$darkmode_css}<img src='{$logo_url}' class='baum-site-logo' />";
}

add_shortcode('baum_site_logo', 'baum_site_logo_shortcode');


function baum_featured_images_shortcode ($atts) {
  $atts = shortcode_atts([
    'column' => '1',         // Column: 1 (367px), 2 (733px), or 3(1100px)
    'thumb' => '1-1',        // Thumbnail aspect ratio
    'thumb_size' => 'baum-1-1-thumb', // Thumbnail aspect ratio
    'format' => '1',         // Layout format: 1, 2, or 3
    'post_id' => get_the_ID() // Optional: Specific post ID
  ], $atts, 'baum_featured_images');

  $atts['thumb'] = ($atts['thumb'] == 'none') ? '1-1' : $atts['thumb']; 
  // Get thumbnail size from aspect ratio
  $thumb_size = 'baum-' . $atts['thumb'] . '-thumb';

  // echo $thumb_size; 

  $aspect_ratio_class = 'aspect-ratio-' . $atts['thumb']; 

  // Retrieve featured images
  $image_ids = get_post_meta($atts['post_id'], 'multiple_featured_images', true) ?: []; 

  // if (has_post_thumbnail() && !empty($image_ids)) { 
  //   array_unshift($image_ids, get_post_thumbnail_id());
  // } else if (has_post_thumbnail()) {
  //   $image_ids[] = get_post_thumbnail_id(); 
  // }

  if (has_post_thumbnail() && empty($image_ids)) { 
    // array_unshift($image_ids, get_post_thumbnail_id());
  // } else if (has_post_thumbnail()) {
    $image_ids[] = get_post_thumbnail_id(); 
  }

  // print_r($atts); 
  // print_r($image_ids); 

  if (empty($image_ids)) {
    return; // '<p>No featured images found.</p>';
  }



  // foreach ($image_ids as $image_id) { 
  //   $parent_id = wp_get_post_parent_id($image_id);
  //   if ($parent_id) {
  //     $parent = get_post($parent_id);
  //     $mime = get_post_mime_type($parent);
  //     if (in_array($mime, ['text/csv', 'application/vnd.ms-excel'])) {
  //       echo do_shortcode('[baum_chart_sheet id="' . esc_attr($parent_id) . '"]');
  //     }
  //   }
  // }



  // Start output buffer
  ob_start();

  // Layout Formats
  switch ($atts['format']) {
    case '1': // Format 1: Large image with thumbnails
    case '2': // Format 2: Similar structure, shared logic
      ?>
      <div class="baum-featured-images format-<?php echo esc_attr($atts['format']); ?> <?php echo esc_attr($aspect_ratio_class); ?> columns-<?php echo esc_attr($atts['column']); ?>">
        <!-- Main Featured Image -->
        <div class="baum-featured baum-feat-main-image" style='position:relative;'>
          <?php if (isset($image_ids[0])) { 
            $thumb_id = $image_ids[0]; 

            // $caption = wp_get_attachment_caption($thumb_id); ?>
            <?php // if ($caption) { ?>
                <!-- <figcaption class="baum-img-caption wp-element-caption"> -->
                    <?php // echo $caption; ?>
                <!-- </figcaption> -->
            <?php // } ?> 

            <?php 
              $caption = wp_get_attachment_caption($thumb_id); 
              // if ($caption) { 
              echo "<figcaption class='baum-modal-caption' data-title-max-lines='1' style='width:100%;bottom:0;padding:5px 10px;'>"; 
              echo $caption; 
              // echo ' - '; 
              // echo $thumb_size; 
              echo "</figcaption>"; 
              // } 
            ?> 
            <?php echo wp_get_attachment_link($thumb_id, $thumb_size, false, false, false, [
              'class' => $thumb_size, // 'baum-feat-large-image',
              'data-full' => wp_get_attachment_image_url($thumb_id, 'full'),
              'data-attachment-url' => esc_url(get_attachment_link($thumb_id)),
            ]); ?>
          <?php } ?>
        </div>
  
        <!-- Thumbnails -->
        <div class="baum-feat-thumbnails baum-featured-images-gallery">
          <?php 
          // $image_ids = get_post_meta(get_the_ID(), 'multiple_featured_images', true);
          // if (!empty($image_ids)) { 
            foreach ($image_ids as $image_id) { 
              $caption = wp_get_attachment_caption($image_id); 
              $img_url = wp_get_attachment_image_url($image_id, $thumb_size); 
              $full_url = wp_get_attachment_image_url($image_id, 'full'); 
              $attachment_url = esc_url(get_attachment_link($image_id)); 
              ?>
              <span>
                <a href="<?php echo $full_url; ?>" data-attachment-url="<?php echo $attachment_url; ?>">
                  <img src="<?php echo esc_url(wp_get_attachment_image_url($image_id, 'baum-256-thumb')); ?>" 
                    class="baum-feat-thumb-square"
                    data-large="<?php echo $img_url; ?>" 
                    data-full="<?php echo $full_url; ?>" 
                    data-attachment-url="<?php echo $attachment_url; ?>" 
                    alt="">
                </a>
                <?php if ($caption) { ?>
                  <figcaption class="baum-img-caption wp-element-caption" style="display:none;">
                    <?php echo $caption; ?>
                  </figcaption>
                <?php } ?>
                <img src="<?php echo $img_url; ?>" 
                  class="baum-featured-sized" 
                  style="display:none;" />
              </span>
            <?php } ?>
          <?php // } ?>
        </div>
      </div>
      <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Select the main image and its wrapping link
        const mainImageContainer = document.querySelector('.baum-feat-main-image');
        const mainImageLink = mainImageContainer.querySelector('a');
        const mainImage = mainImageLink.querySelector('img');

        // Select all thumbnails
        const thumbnails = document.querySelectorAll('.baum-featured-images-gallery img');

        // Add click event listeners to all thumbnails
        thumbnails.forEach(thumb => {
          thumb.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent default link behavior

            // Get data attributes from the clicked thumbnail
            const newLargeSrc = this.getAttribute('data-large'); // The large version of the image
            const newFullSrc = this.getAttribute('data-full'); // The full-size version of the image
            const newAttachmentUrl = this.getAttribute('data-attachment-url'); // The attachment URL

            // Update the main image's attributes
            mainImage.src = newLargeSrc; // Set the large image URL
            mainImage.srcset = ''; // Clear srcset to avoid conflicts
            mainImage.setAttribute('data-attachment-url', newAttachmentUrl); // Update data-attachment-url

            // Update the main image link's href
            mainImageLink.href = newFullSrc; // Update the link to point to the full-size image
          });
        });
      });
      </script>
      <?php 
      break;

    case '3': // Format 3: Grid
      ?>
      <div class="baum-featured-images format-3 <?php echo esc_attr($aspect_ratio_class); ?> columns-<?php echo esc_attr($atts['column']); ?>">
        <?php foreach ($image_ids as $image_id): ?>
          <div class="baum-feat-grid-item">
            <?php 
            $caption = wp_get_attachment_caption($image_id); 
            if ($caption) { 
              echo "<figcaption class='baum-modal-caption' data-title-max-lines='1' style='width:100%;bottom:0;padding:5px 10px;'>";
              echo $caption; 
              echo "</figcaption>"; 
            } 
            

          
                echo wp_get_attachment_link($image_id, $thumb_size, false, false, false, ['class' => 'baum-feat-grid-thumb']);

            


            // $caption = wp_get_attachment_caption($image_id); 
            // if ($caption) { 
            //   echo "<figcaption data-title-max-lines='1' class='baum-img-caption wp-element-caption'>";
            //   echo $caption; 
            //   echo "</figcaption>"; 
            // } 

            // wp_get_attachment_image($image_id, 'baum-1:1-thumb', false, ['class' => 'grid-thumb']); 
            ?>
          </div>
        <?php endforeach; ?>
      </div>
      <?php
      break;
        
      case '6': 
        ?>
        <div class="baum-featured-images format-6 <?php echo esc_attr($aspect_ratio_class); ?> columns-<?php echo esc_attr($atts['column']); ?>">
          <?php foreach ($image_ids as $image_id): ?>
            <div class="baum-feat-grid-item">
              <?php 
              $caption = wp_get_attachment_caption($image_id); 
              if ($caption) { 
                echo "<figcaption class='baum-modal-caption' data-title-max-lines='1' style='width:100%;bottom:0;padding:5px 10px;'>";
                echo $caption; 
                echo "</figcaption>"; 
              } 
              
              echo wp_get_attachment_link($image_id, $thumb_size, false, false, false, ['class' => 'baum-feat-grid-thumb']);

              // $caption = wp_get_attachment_caption($image_id); 
              // if ($caption) { 
              //   echo "<figcaption data-title-max-lines='1' class='baum-img-caption wp-element-caption'>";
              //   echo $caption; 
              //   echo "</figcaption>"; 
              // } 

              // wp_get_attachment_image($image_id, 'baum-1:1-thumb', false, ['class' => 'grid-thumb']); 
              ?>
            </div>
          <?php endforeach; ?>
        </div>
        <?php
        break;
        
      default:
        echo '<p>Invalid format selected: <code>'.$atts['format'].'</code></p>';
  }

  return ob_get_clean();
}

add_shortcode('baum_featured_images', 'baum_featured_images_shortcode');

// Explanation of [baum_person_card] 
// Attributes:
// id: WordPress user ID.
// email: User's email address (alternative to ID).
// float: Aligns the card (left, right, center).
// format: Determines the style (1, 2, or 3).
// User Retrieval:
// Fetches the user object using get_userdata() (ID) or get_user_by() (email).
// Conditional Formatting:
// Outputs different HTML structures based on the format value.
// Dynamic Styles:
// Floats the card to the left, right, or centers it.
// Error Handling:
// Displays meaningful error messages if id or email is missing or invalid.
// Interactive Elements:
// Format 3 includes a "Follow" button and a "Close" button.

/**
 * Shortcode: [baum_person_card]
 * 
 * Displays user information in various formats.
 * 
 * Attributes:
 * - `id` (int|string|array): WordPress user ID(s). Accepts a single ID, comma-separated IDs, or an array of IDs.
 * - `email` (string|array): User's email(s). Accepts a single email, comma-separated emails, or an array of emails.
 * - `float` (string): Alignment of the card. Options: `left`, `right`, or `center` (default: `center`).
 * - `format` (int): Determines the display style. Options:
 *   - `1`: Basic user card with avatar and name.
 *   - `2`: Mini card styled with the theme.
 *   - `3`: Standalone profile card with follow button and close functionality.
 *   - `4`: Single avatar with tooltip and link to profile.
 *   - `5`: Stacked avatars for multiple users with tooltip listing all users.
 *   - `6`: List of user names linking to their profiles.
 * - `size` (int): Size of the avatar in pixels (used for `format` 4; default: 64).
 * 
 * Example Usage:
 * `[baum_person_card id="1,2,3" format="5" float="left"]`
 * 
 * @param array $atts Shortcode attributes.
 * @return string HTML output of the user card.
 */

 function baum_person_card_shortcode ($atts) {
  $atts = shortcode_atts([
    'id'      => null,
    'email'   => null,
    'float'   => 'center',
    'title'   => '',
    'format'  => 1,
    'size'    => 64,
    'columns' => 2,          // Default number of columns or inline
    'width'   => '100%',     // Default container width
  ], $atts, 'baum_person_card');

  if (empty($atts['id']) && empty($atts['email'])) {
    return '<p>Error: You must provide either a user ID or email.</p>';
  }

  $float = strtolower($atts['float']);
  $alignment = '';

  if ($float === 'left') 
    $alignment = "float:left;margin-right:10px";
  elseif ($float === 'right') 
    $alignment = "float:right;margin-left:10px";
  else 
    $alignment = "margin:auto";

  // Retrieve user(s)
  $users = [];
  if (!empty($atts['id'])) {
    $ids = is_array($atts['id']) ? $atts['id'] : explode(',', $atts['id']);
    foreach ($ids as $id) {
      $user = get_userdata(trim($id));
      if ($user) $users[] = $user;
    }
  } elseif (!empty($atts['email'])) {
    $emails = is_array($atts['email']) ? $atts['email'] : explode(',', $atts['email']);
    foreach ($emails as $email) {
      $user = get_user_by('email', trim($email));
        if ($user) $users[] = $user;
    }
  }

  if (empty($users)) {
    return '<!-- Error: User(s) not found.</p> -->';
  }

  if ($atts['columns'] == "inline") {
    $display = "display:inline"; 
  } else {
    $columns = max(1, intval($atts['columns'])); // Ensure at least 1 column
    $column_width = 100 / $columns . '%';        // Calculate width for each column

    // Ensure columns are between 1 and 12
    $columns = max(1, min(10, intval($atts['columns'])));

    $display = "display:grid;grid-template-columns:repeat($columns, 1fr);gap:0px";
  }

  ob_start();

  ?>
  <div class="baum-person-cards baum-person-card-grid format-<?php echo $atts['format']; ?>" style="width:<?php echo esc_attr($atts['width']); ?>;<?php echo $alignment; ?>;<?php echo $display; ?>;">
          <?php
          // Execute the existing formats without altering them
          ob_start();
          switch ((int) $atts['format']) {
            case 0:
            case 1:
                ?>
                <?php foreach ($users as $user): ?>
                  <div class="baum-person-card-item" style="">
                    <div class="baum-widget-person" style="">
                      <div style="border-radius:50%;height:64px;min-width:64px;max-width:64px;margin:10px;float:left;">
                        <?php echo get_avatar($user->ID, 64); ?>
                      </div>
                      <span class="baum-author-box-name" data-title-max-lines='1'>
                        <a href="<?php echo esc_url(get_author_posts_url($user->ID)); ?>" style="color:var(--color-black);">
                            <?php echo esc_html($user->display_name); ?>
                        </a>
                      </span>
                    </div>
                  </div>
                <?php endforeach; ?>
                <?php
                break;

              case 2:
                  ?>
                  <?php foreach ($users as $user): ?>
                    <div style="margin:5px;" class="">
                        <div class='baum-card-container-mini <?php // echo $card_text_color; ?>'>
                            <div class='baum-card baum-card-mini <?php // echo $card_bg_color; ?>'>
                                <div class='baum-card-body'>
                                    <div class='baum-card-img'>
                                        <?php echo get_avatar($user->ID, 64); ?>
                                    </div>
                                    <div class='baum-card-headline' data-title-max-lines='3'>
                                        <a class='baum-title post-title' href='<?php echo get_author_posts_url($user->ID); ?>'>
                                            <?php echo esc_html($user->display_name); ?>
                                        </a>
                                        <div class='post-excerpt'>
                                            @<?php echo esc_html($user->user_nicename); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> 
                  <?php endforeach; ?>
                  <?php
                  break;

              case 3:
                  ?>
                  <?php foreach ($users as $user): ?> 
                    <div class="baum-person-card-substack" style="margin:5px;border:var(--card-border);outline:var(--card-outline);border-radius:var(--border-radius);padding:10px;text-align:center;position:relative;background:var(--card-bg);">
                        <button title='Close' class="baum-person-card-close" style="position:absolute;top:0px;right:10px;background:transparent;border:none;font-size:16px;cursor:pointer;margin:0;width:16px;padding:0;color:var(--color-secondary);font-weight:400;" onclick="this.closest('.baum-person-card-substack').remove()">✖</button>
                        <div style="border-radius:50%;height:96px;width:96px;margin:10px auto;">
                            <?php echo get_avatar($user->ID, 96); ?>
                        </div>
                        <a href='<?php echo get_author_posts_url($user->ID); ?>' data-title-max-lines='1'>
                            <h4 style="font-weight:normal;font-size:16px;color:var(--color-secondary);"><?php echo esc_html($user->display_name); ?></h4>
                        </a>
                        <div style="color:var(--color-quinary);font-size:14px;">
                          @<?php echo esc_html($user->user_nicename); ?>
                        </div>
                        <button class='button secondary-bg' style="margin-top:10px;margin-bottom:10px;padding:0px;border:none;cursor:pointer;width:128px;height:28px;">
                          Follow
                        </button>
                    </div> 
                  <?php endforeach; ?>
                  <?php
                  break;

                case 4:
                    $size = intval($atts['size']) ?: 64;
                    // $user = $users[0];
                    foreach ($users as $user): ?>
                    <div class="baum-person-card-item" style="">
                        <?php 
                        // Single Avatar with Tooltip
                        ?>
                        <a href="<?php echo esc_url(get_author_posts_url($user->ID)); ?>"  title="<?php echo esc_html($user->display_name); ?>" data-tooltipster='{"position":"bottom"}'>
                            <?php echo get_avatar($user->ID, $size); ?>
                        </a>
                      </div>
                    <?php endforeach; ?>
                    <?php
                    break;
            
                case 5:
                    // Stacked Avatars with Tooltip
                    $count = count($users);
                    $visible = min(5, $count);
                    ?>
                    <?php // foreach ($users as $user): ?>
                      <div class="baum-person-card-item" style="">
                        <div class="baum-avatar-stack" style="position:relative;background:rgba(128,128,128,0.1);border-radius:var(--border-radius);overflow:hidden;border:1px solid rgba(128,128,128,0.25);/*background:var(--color-octonary);font-weight:700;outline:var(--card-outline);*/height:42px;line-height:14px;"> 

                            <?php if ($atts['title']) { ?> 
                              
                              <div style='display:grid;grid-template-columns:auto 1fr;'> 

                                <div> 
                                  <h6 style='line-height:35px;background:var(--color-secondary);'> 
                                    <?php echo $atts['title']; ?> 
                                  </h6> 
                                </div> 

                                <div style='position:relative;'> 
                                  <?php for ($i = 0; $i < $visible; $i++): ?> 
                                    <a href="<?php echo esc_url(get_author_posts_url($users[$i]->ID)); ?>" style="margin:5px auto;position:absolute;left:<?php echo ($i * 25) + 5; ?>px;box-shadow:0px 0px 1px 1px rgba(0,0,0,0.1);border-radius:16px;" title="<?php echo esc_html($users[$i]->display_name); ?>" data-tooltipster='{"position":"bottom"}'>
                                        <?php echo get_avatar($users[$i]->ID, 32); ?>
                                    </a>
                                  <?php endfor; ?>
                                  <?php if ($count > $visible): ?>
                                    <span style="line-height:42px;position:absolute;left:<?php echo $visible * 25; ?>px;">+<?php echo $count - $visible; ?> More</span>
                                  <?php endif; ?>
                                </div>

                              </div> 

                            <?php } else { ?> 

                              <div style='position:relative;'> 
                                <?php for ($i = 0; $i < $visible; $i++): ?> 
                                  <a href="<?php echo esc_url(get_author_posts_url($users[$i]->ID)); ?>" style="margin:5px auto;position:absolute;left:<?php echo ($i * 25) + 5; ?>px;box-shadow:0px 0px 1px 1px rgba(0,0,0,0.1);border-radius:16px;" title="<?php echo esc_html($users[$i]->display_name); ?>" data-tooltipster='{"position":"bottom"}'>
                                      <?php echo get_avatar($users[$i]->ID, 32); ?>
                                  </a>
                                <?php endfor; ?>
                                <?php if ($count > $visible): ?>
                                  <span style="line-height:42px;position:absolute;left:<?php echo $visible * 25; ?>px;">+<?php echo $count - $visible; ?> More</span>
                                <?php endif; ?>
                              </div>

                            <?php } ?>
                        </div>
                        <div style="width:64px;height:32px;display:inline-block;"></div>
                      </div>
                    <?php // endforeach; ?>
                    <?php
                    break;
            
                case 6:
                    // foreach ($users as $user): ?>
                        <?php // Names List with Links
                        $user_links = array_map(function ($user) {
                            return '<a href="' . esc_url(get_author_posts_url($user->ID)) . '">' . esc_html($user->display_name) . '</a>';
                        }, $users);
                    
                        if (count($user_links) > 1) {
                            // Combine all names with commas, and the last two with " and "
                            $last_user = array_pop($user_links);
                            echo implode(', ', $user_links) . ' and ' . $last_user;
                        } else {
                            // Single user, no conjunction needed
                            echo $user_links[0];
                        }
                        ?>
                    <?php // endforeach;
                    break;
            
                  default:
                      return '<!-- <p>Error: Invalid format selected.</p> -->';
          }
          echo ob_get_clean();
          ?>
  </div>
  <?php

  return ob_get_clean();
}

add_shortcode('baum_person_card', 'baum_person_card_shortcode');

/** 
 * [contact] — Contact us
 * 
 */ 

add_shortcode('baum_contact', function () { 
  $retval = '<h6>Contact:</h6>';
  $retval .= '<p>';
  $retval .= get_theme_mod('baum_name') . '<br>';
  $retval .= get_theme_mod('baum_position') . '<br>';
  $retval .= get_bloginfo('admin_email') . '</p>';
  return $retval; 
}); 

/** 
 * [baum_about] — About us (one paragraph) 
 * 
 */ 

add_shortcode('baum_about', function () { 
  $retval = '<h6>About Us:</h6>'; 
  $retval .= '<p>' . get_theme_mod('baum_about') . '</p>'; 
  return $retval; 
}); 

/** 
 * [site_url] — Site URL shortcode 
 * 
 */ 

add_shortcode('site_url', function () { 
  return home_url(); 
}); 

/** 
 * [baum_site_url] — Baum Site URL shortcode 
 * 
 */ 

add_shortcode('baum_site_url', function () { 
  return home_url(); 
}); 

/** 
 * [sitename] — Site name shortcode 
 * 
 */ 

add_shortcode('sitename', function () { 
  return get_bloginfo(); 
}); 

/** 
 * [baum_sitename] — Site name shortcode 
 * 
 */ 

add_shortcode('baum_sitename', function () { 
  return get_bloginfo(); 
}); 

/** 
 * [baum_title] — Content title shortcode 
 * 
 */ 

add_shortcode('baum_title', function () { 
  if (is_archive()) return single_term_title();
  else return get_the_title(); 
});

/** 
 * [baum_name] — Admin name shortcode 
 * 
 */ 

add_shortcode('baum_name', function () { 
  return get_theme_mod('baum_name');
});

/** 
 * [baum_email] — Admin email shortcode 
 * 
 */ 

add_shortcode('baum_email', function () { 
  return get_theme_mod('baum_email');
});

/** 
 * [baum_position] — Admin position shortcode 
 * 
 */ 

add_shortcode('baum_position', function () { 
  return get_theme_mod('baum_position');
});

/** 
 * [baum_your_address] — Admin address shortcode 
 * 
 */ 

add_shortcode('baum_address', function () { 
  return get_theme_mod('baum_address');
});


/** 
 * [baum_user_roles] — Display the current user's roles
 * 
 */ 

add_shortcode('baum_user_roles', function () {
  $current_user = wp_get_current_user(); 
  if ($current_user->ID == 0) return; // Check if a user is signed in 
  $roles = $current_user->roles; 
  if (!empty($roles)) return implode(', ', $roles); 
  else return 'You have no roles assigned.'; 
}); 

///////////////////////////////////// 
// Return the User's Dashboard URL 
///////////////////////////////////// 

function dashboard_url () { 
  $current_user = wp_get_current_user(); 
  if ($current_user->ID == 0) return '/'; 
  return '/dashboard'; 
} 

///////////////////////////////////// 
// Return the User's Profile URL 
///////////////////////////////////// 

function get_profile_url ($id) { 
  return get_the_author_meta('url', $id); 
} 


// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

// function baum_icon_embed($url, $title = '') {
//   $parsed_url = parse_url($url, PHP_URL_HOST);
//   $title = $title ? $title : $parsed_url;
//   $favicon = 'https://www.google.com/s2/favicons?sz=128&domain=' . $parsed_url;

//   // Tooltip setup to load Iframely content on hover or click
//   $html = '<span class="baum-icon-embed" data-url="' . esc_url($url) . '">'
//       . '<img src="' . esc_url($favicon) . '" alt="favicon" />'
//       . '<a href="' . esc_url($url) . '" target="_blank" rel="noopener noreferrer">'
//       . esc_html($title)
//       . '</a>'
//       . '</span>';
//   return $html;
// }

// function baum_shortcode_embed($atts) {
//   $atts = shortcode_atts([
//       'url' => '',
//       'title' => ''
//   ], $atts, 'baum_embed');
//   return baum_icon_embed($atts['url'], $atts['title']);
// }

// add_shortcode('baum_embed', 'baum_shortcode_embed');

// function baum_icon_embed ($url, $title = '') { 
//   $parsed_url = parse_url($url, PHP_URL_HOST); 
  
//   // Fetch and cache the title if it's not provided 
//   if (empty($title)) { 
//       $transient_key = 'baum_embed_title_' . md5($url); 
//       $cached_title = get_transient($transient_key); 

//       if ($cached_title !== false) { 
//           $title = $cached_title; 
//       } else { 
//           // Fetch the external page HTML 
//           $response = wp_remote_get($url); 

//           error_log( print_r($response) ); 

//           if (is_wp_error($response)) {
//               $title = $parsed_url; // Fallback to domain if the request fails
//               error_log('Fallback to domain if the request fails: ' .  $title); 
//           } else {
//               $html = wp_remote_retrieve_body($response);

//               // error_log('$html ' . $html); 

//               // Use regex to extract the <title> tag content
//               if (preg_match('/<title>(.*?)<\/title>/', $html, $matches)) {
//                   $title = trim($matches[1]);

//                   // Cache the title for 24 hours (86400 seconds)
//                   set_transient($transient_key, $title, 86400);
//               } else {
//                   $title = $parsed_url;
//                   error_log('Fallback to domain if no title is found: ' .  $title); 
//               }
//           }

//           // Trim the title if it's too long
//           if (strlen($title) > 50) {
//               $title = substr($title, 0, 50) . '...';
//           }
//       }
//   }

//   $favicon = 'https://www.google.com/s2/favicons?sz=128&domain=' . $parsed_url;

//   // Tooltip setup to load Iframely content on hover or click
//   $html = '<span class="button button-small baum-icon-embed" data-url="' . esc_url($url) . '">'
//       . '<img src="' . esc_url($favicon) . '" alt="favicon" />'
//       . '<a class="black" href="' . esc_url($url) . '" target="_blank" rel="noopener noreferrer">'
//       . esc_html($title)
//       . '</a>'
//       . '</span>';
//   return $html;
// }

// function baum_shortcode_embed ($atts) {
//   $atts = shortcode_atts([
//       'url' => '',
//       'title' => ''
//   ], $atts, 'baum_embed');
//   return baum_icon_embed($atts['url'], $atts['title']);
// }

// add_shortcode('baum_embed', 'baum_shortcode_embed');


// function baum_icon_embed ($url, $title = '') { 
//   $parsed_url = parse_url($url, PHP_URL_HOST); 
//   $title = $title ? $title : $parsed_url; 
//   $favicon = 'https://www.google.com/s2/favicons?sz=128&domain=' . $parsed_url; 
//   $html = '<span class="baum-icon-embed">'
//     . '<img src="' . esc_url($favicon) . '" alt="favicon" />'
//     . '<a href="' . esc_url($url) . '" target="_blank" rel="noopener noreferrer">' 
//     . esc_html($title) 
//     . '</a>'
//     . '</span>'; 
//   return $html; 
// } 

// function baum_shortcode_embed ($atts) {
//   $atts = shortcode_atts([ 
//     'url' => '', 
//     'title' => '' 
//   ], $atts, 'baum_embed');
//   return baum_icon_embed($atts['url'], $atts['title']);
// }

// add_shortcode('baum_embed', 'baum_shortcode_embed');

/**
 * Shortcode: [if_role role=name] .. [elseif role=name] .. [else] .. [/if_role]
 * 
 * Displays different content based on the user's role. It supports:
 * - [if_role role=name] to check the initial role.
 * - [elseif role=name] for additional role checks (supports multiple).
 * - [else] if none of the roles match or if the user is not logged in.
 * 
 * It matches WordPress roles hierarchically (e.g., "editor" or higher).
 * It replaces {{role}} with the user's role or "Anonymous" for guests.
 * 
 * Usage examples:
 * 
 * 1. Only [if_role] block:
 *    [if_role role=editor] You are an {{role}}! 
 *    [/if_role]
 * 
 * 2. [if_role] block with multiple [elseif] blocks and no [else] block:
 *    [if_role role=editor] You are an editor!
 *    [elseif role=subscriber] You are a subscriber!
 *    [elseif role=author] You are an author! 
 *    [/if_role]
 * 
 * 3. [if_role] block with only [else] block:
 *    [if_role role=editor] You are an editor! 
 *    [else] You are an anonymous user. 
 *    [/if_role]
 * 
 * 4. [if_role] block with multiple [elseif] blocks and an [else] block:
 *    [if_role role=editor] You are an editor!
 *    [elseif role=subscriber] You are a subscriber!
 *    [elseif role=author] You are an author!
 *    [else] You are an anonymous user. 
 *    [/if_role]
 * 
 * Dynamically handles the role checks and displays the appropriate content.
 */

 function role_based_content_shortcode ($atts, $content = null) {
  // Get all roles
  global $wp_roles;
  if (!isset($wp_roles)) {
    $wp_roles = new WP_Roles();
  }
  $all_roles = $wp_roles->roles;

  // Define role hierarchy dynamically
  $role_hierarchy = [];
  $priority = count($all_roles); // Assign highest priority to the first role

  foreach ($all_roles as $role_key => $role_data) {
    $role_hierarchy[$role_key] = $priority;
    $priority--;
  }

  // Define a pattern to match the entire structure with multiple elseif blocks
  $pattern = '/\[if_role role=([a-z_]+)\](.*?)(\[(elseif role=[a-z_]+)\](.*?))*\[else\](.*?)\[\/if_role\]/is';
  $else_content = '';

  if (preg_match($pattern, $content, $matches)) {
    $role_blocks = [];
    $else_content = array_pop($matches); // Extract the [else] block content if present
    $content_length = count($matches);

    // Extract [if_role] and [elseif] blocks
    for ($i = 1; $i < $content_length; $i += 2) {
      if (strpos($matches[$i], 'role=') !== false) {
        $role_blocks[] = [
          'role' => strtolower(str_replace(['role=', '[', ']'], '', $matches[$i])),
          'content' => $matches[$i + 1],
        ];
      }
    }
  } else {
    // Try without elseif and else
    $pattern = '/\[if_role role=([a-z_]+)\](.*?)\[\/if_role\]/is';
    if (preg_match($pattern, $content, $matches)) {
      $role_blocks[] = [
        'role' => strtolower($matches[1]),
        'content' => $matches[2],
      ];
    } else {
      // Try with only if_role and else
      $pattern = '/\[if_role role=([a-z_]+)\](.*?)\[else\](.*?)\[\/if_role\]/is';
      if (preg_match($pattern, $content, $matches)) {
        $role_blocks[] = [
          'role' => strtolower($matches[1]),
          'content' => $matches[2],
        ];
        $else_content = $matches[3];
      }
    }
  }

  if (is_user_logged_in() && $role_blocks) {
    $user = wp_get_current_user();
    $roles = (array) $user->roles;
    $role = array_shift($roles); // Get the highest role
    $role = strtolower($role);

    // Check user roles against the [if_role] and [elseif] blocks
    foreach ($role_blocks as $block) {
      if (isset($role_hierarchy[$block['role']]) && $role_hierarchy[$role] >= $role_hierarchy[$block['role']]) {
        return str_replace('{{role}}', ucfirst($role), do_shortcode($block['content']));
      }
    }
  }

  // Return [else] if no roles match or if the user is not logged in
  return str_replace('{{role}}', 'Anonymous', do_shortcode($else_content));
}

add_shortcode('if_role', 'role_based_content_shortcode');

/** 
 * 
 * [baum_auth_card] — Baum Authentication Card shortcode 
 * 
 */ 

add_shortcode('baum_auth_card', function ($atts, $content, $shortcode_tag) { 
  $atts = $atts ? $atts : []; 
  $title = isset($atts['title']) ? $atts['title'] : 'true'; 
  ob_start(); 
  if (is_user_logged_in()) { ?> 
    <style>
      .baum-author-box-name-wrap .right { display: none; } 
      .baum-author-box-name-wrap .fa-link { display: none; } 
      .baum-card-bottom { display: none; } 
      article.baum-author-box .baum-author-box-wrap .baum-author-box-name a { 
        font-size: 20px; 
        font-weight: 800; 
        line-height: 28px; 
      } 
      article .baum-author-box-wrap { 
        height: auto; 
        padding: 0 10px; 
        padding-top: 10px; 
      } 
    </style>
    <?php if ($title === 'true') { ?> 
      <h5 class='secondary'>Currently Signed In</h5> 
    <?php } ?>
    <article class='baum-author-box'> 
      <div class='baum-card-container baum-card'> 
        <?php 
          // $author_id = get_the_author_meta('ID');
          $author_id = get_current_user_id(); 
          get_template_part('parts/baum-author', 'card', [
            'avatar_size' => 64, 
            'content_length' => 0, 
            // 'bgcolor' => 'standard', 
            'id' => $author_id, 
            'role' => do_shortcode('[baum_user_roles]'), 
            'email' => get_the_author_meta('email', $author_id), 
            'content' => get_the_author_meta('description', $author_id), 
            // 'since' => get_the_author_meta('user_registered', $author_id), 
            // 'twitter' => get_the_author_meta('twitter', $author_id), 
            // 'facebook' => get_the_author_meta('facebook', $author_id), 
            // 'linkedin' => get_the_author_meta('linkedin', $author_id), 
            'name' => get_the_author_meta('display_name', $author_id), 
            'user_login' => get_the_author_meta('user_login', $author_id), 
            // 'website' => get_the_author_meta('website', $author_id), 
          ]); 
        ?>
      </div>
    </article>

    <div class='baum-grid-2' style='grid-gap: 10px;'>
      <a class='button tertiary-bg' href='<?php echo home_url(); ?>' id='baum-home-link'>Home</a> 
      <a class='button secondary-bg' href='<?php echo wp_logout_url(get_permalink()); ?>' id='baum-logout-link'>Sign Out</a> 
    </div>
  <?php }
  return ob_get_clean();
});

/**
* Shortcode for displaying the site tagline.
*
* @return string The site tagline.
*/

function baum_site_tagline_shortcode () { 
  return get_bloginfo('description'); 
} 

add_shortcode('baum_site_tagline', 'baum_site_tagline_shortcode'); 

/**
 * Shortcode displays site icon with size, background color, border radius.
 *
 * @param array $atts - Optional. An array of shortcode attributes.
 * @type string $size - The size of the icon in pixels. Default '32px'.
 * @type string $background_color - Default 'var(--color-quinary)'.
 * @type string $border_radius - Default 'var(--border-radius)'.
 * }
 * @return string HTML output of the site icon.
 */

function baum_site_icon_shortcode ($atts) {
  $atts = shortcode_atts([
    'size' => '32px',
    'background_color' => 'var(--color-quinary)',
    'border_radius' => 'var(--border-radius)',
  ], $atts, 'baum_site_icon');

  $size = esc_attr($atts['size']);
  $background_color = esc_attr($atts['background_color']);
  $border_radius = esc_attr($atts['border_radius']);

  $site_icon_url = get_site_icon_url();
  if (!$site_icon_url) {
    return '';
  }

  return "<img src='{$site_icon_url}' style='width:{$size}; height:{$size}; background-color:{$background_color}; border-radius:{$border_radius};' />";
}

add_shortcode('baum_site_icon', 'baum_site_icon_shortcode');

/**
* Shortcode displays today's date with an optional time component.
*
* @param array $atts - Optional. An array of shortcode attributes.
* @type bool $time - Display time along with date. Default false.
* 
* @return string Today's date, optionally including the time.
*/

function baum_today_date_shortcode ($atts) {
  $atts = shortcode_atts([ 'time' => false ], $atts, 'baum_today_date');
  $format = 'F j, Y';
  if ($atts['time']) $format .= ' g:ia';
  return date($format);
}

add_shortcode('baum_today_date', 'baum_today_date_shortcode');

/**
 * Shortcode displays date and time shortcode was first loaded for $post.
 *
 * @return string - Date and time shortcode first loaded, format 'F j, Y g:ia'.
 */
function baum_first_loaded_shortcode () {
  global $post;
  // Define a meta key to store the first loaded time in the post meta
  $meta_key = '_baum_first_loaded_time';
  // Retrieve the stored first loaded time from the post meta
  $first_loaded_time = get_post_meta($post->ID, $meta_key, true);
  // If the first loaded time is not set, set it to the current time and save it in the post meta
  if (!$first_loaded_time) {
    $first_loaded_time = current_time('mysql');
    update_post_meta($post->ID, $meta_key, $first_loaded_time);
  }
  // Format the stored time for display
  $formatted_time = date('F j, Y g:ia', strtotime($first_loaded_time));
  return $formatted_time;
}

add_shortcode('baum_first_loaded', 'baum_first_loaded_shortcode');




/*
Plugin Name: Baum's Gutenberg Frontend
Description: Allows users with the required capabilities to create posts using Gutenberg on the frontend.
Version: 1.0
Author: Your Name
*/


// Register the shortcode.
function baum_gutenberg_frontend_shortcode() {
    if ( ! current_user_can( 'edit_posts' ) ) {
        return '<p>You do not have permission to write a new post.</p>';
    }

    ob_start();
    ?>
    <div id="baum-gutenberg-frontend-app"></div>
    <?php
    return ob_get_clean();
}

// add_shortcode( 'baum_gutenberg_frontend', 'baum_gutenberg_frontend_shortcode' );


function baum_recent_announcements_shortcode() {
  // Get recent posts of type 'announcement'
  $recent_posts = wp_get_recent_posts([ 'post_type' => 'announcement' ]);
  
  // Start output buffering
  ob_start();

  // Display the announcements
  if (!empty($recent_posts)) {
      echo "<ul class='baum-announcements'>";
      foreach ($recent_posts as $recent) {
          // Exclude the 'announcements' page by slug
          echo "<li>
                  <u>
                      <a href='" . esc_url(get_permalink($recent['ID'])) . "'>" . esc_html($recent['post_title']) . "</a>
                  </u> – ";
          get_template_part('parts/baum-byline', 'updated');
          echo "</li>";
      }
      echo "</ul>";
  } else {
      echo "<p>No recent announcements found.</p>";
  }

  // Return the buffered content
  return ob_get_clean();
}

// Register the shortcode
add_shortcode('baum_recent_announcements', 'baum_recent_announcements_shortcode');


function baum_story_card_shortcode ($atts) {
  // Shortcode attributes
  $atts = shortcode_atts([
      'format' => 1, // Default format is 1
      'bg_color' => 'default-bg', // Default background color class
      'text_color' => 'default-text', // Default text color class
      'query' => 'latest', // Default query type
      'quantity' => 1, // Default to 1 card
  ], $atts, 'baum_story_card');

  // Map formats to sizes
  $format_map = [
      1 => '1x2',
      2 => 'xlarge',
      3 => 'large',
      4 => 'medium',
      5 => 'small',
      6 => 'wide',
      7 => 'square',
      8 => 'mini',
      9 => 'book',
      10 => 'full',
  ];

  $format = intval($atts['format']);
  $card_size = $format_map[$format] ?? 'small';
  $posts_per_page = intval($atts['quantity']);
  $card_query = sanitize_text_field($atts['query']);

  // Default query arguments
  $query_args = [
      'post_type' => 'post',
      'posts_per_page' => $posts_per_page,
      'orderby' => 'date',
      'order' => 'DESC',
  ];

  // Query logic based on the provided card_query attribute
  global $post;

  if ($card_query == 'my_people') {
      $baum_follows = $_COOKIE['baum_follows'] ?? '';
      $baum_follows = explode(',', $baum_follows);
      $query_args['post_type'] = 'person';
      $query_args['post__in'] = $baum_follows;
  } elseif ($card_query == 'my_tag') {
      $baum_follows = $_COOKIE['baum_follows'] ?? '';
      $baum_follows = explode(',', $baum_follows);
      $query_args['tag__in'] = $baum_follows;
  } elseif ($card_query == 'my_topic') {
      $baum_follows = $_COOKIE['baum_follows'] ?? '';
      $baum_follows = explode(',', $baum_follows);
      $query_args['tax_query'] = [
          [
              'taxonomy' => 'category',
              'field' => 'term_id',
              'terms' => $baum_follows,
              'operator' => 'IN',
          ],
      ];
  } elseif ($card_query == 'my_author') {
      $baum_follows = $_COOKIE['baum_follows'] ?? '';
      $baum_follows = explode(',', $baum_follows);
      $query_args['tax_query'] = [
          [
              'taxonomy' => 'contributor',
              'field' => 'term_id',
              'terms' => $baum_follows,
              'operator' => 'IN',
          ],
      ];
  } elseif ($card_query == 'my_channel') {
      $baum_follows = $_COOKIE['baum_follows'] ?? '';
      $baum_follows = explode(',', $baum_follows);
      $query_args['tax_query'] = [
          [
              'taxonomy' => 'channel',
              'field' => 'term_id',
              'terms' => $baum_follows,
              'operator' => 'IN',
          ],
      ];
  } elseif ($card_query == 'my_feed') {
      $baum_follows = $_COOKIE['baum_follows'] ?? '';
      $baum_follows = explode(',', $baum_follows);
      $query_args['tax_query'] = [
          'relation' => 'OR',
          [
              'taxonomy' => 'person',
              'field' => 'term_id',
              'terms' => $baum_follows,
              'operator' => 'IN',
          ],
          [
              'taxonomy' => 'contributor',
              'field' => 'term_id',
              'terms' => $baum_follows,
              'operator' => 'IN',
          ],
          [
              'taxonomy' => 'category',
              'field' => 'term_id',
              'terms' => $baum_follows,
              'operator' => 'IN',
          ],
          [
              'taxonomy' => 'channel',
              'field' => 'term_id',
              'terms' => $baum_follows,
              'operator' => 'IN',
          ],
      ];
  } elseif ($card_query == 'related_author' && is_author()) {
      $query_args['author'] = get_the_author_meta('ID');
  } elseif ($card_query == 'related_tag' && is_single()) {
      $tags = wp_get_post_tags($post->ID, ['fields' => 'ids']);
      if ($tags) {
          $query_args['tag__in'] = $tags;
      }
  } elseif ($card_query == 'related_topic') {
      $cat_ids = [];
      if (get_query_var('cat')) {
          $cat_ids[] = get_query_var('cat');
          $query_args['category__in'] = $cat_ids;
      } else {
          $cats = get_the_category();
          if ($cats) {
              foreach ($cats as $cat) {
                  $cat_ids[] = $cat->term_id;
              }
              $query_args['category__in'] = $cat_ids;
          }
      }
  } elseif ($card_query == 'select_tag') {
      $query_args['tag'] = sanitize_text_field($atts['tag'] ?? '');
  } elseif ($card_query == 'select_topic') {
      $topics = get_field('topics', 'option'); // Adjust term source if needed
      $query_args['category__in'] = $topics;
  } elseif ($card_query == 'select_channel') {
      $channel_slug = sanitize_text_field($atts['channel_slug'] ?? '');
      $query_args['tax_query'] = [
          [
              'taxonomy' => 'channel',
              'field' => 'slug',
              'terms' => $channel_slug,
          ],
      ];
  }

  // Run query
  $recent_posts = new WP_Query($query_args);

  if (!$recent_posts->have_posts()) {
      return '<p>No posts found.</p>';
  }

  // Generate output
  ob_start();
  ?>
  <div class="baum-cards-<?php echo esc_attr($card_size); ?> baum-cards-<?php echo esc_attr($atts['bg_color']); ?>">
      <?php while ($recent_posts->have_posts()) : $recent_posts->the_post(); ?>
          <!-- <div class="baum-card baum-card-<?php // echo esc_attr($card_size); ?> baum-card-<?php // echo esc_attr($atts['text_color']); ?>"> -->
              <?php get_template_part('parts/baum', 'card', [
                  'size' => $card_size,
                  'card_bg_color' => $atts['bg_color'],
                  'card_text_color' => $atts['text_color'],
              ]); ?>
          <!-- </div> -->
      <?php endwhile; ?>
  </div>
  <?php
  wp_reset_postdata();
  return ob_get_clean();
}

add_shortcode('baum_story_card', 'baum_story_card_shortcode');



/** 
 * [contact] — Contact us
 * 
 */ 

 add_shortcode('baum_contact', function () { 
  $retval = '<h6>Contact:</h6>';
  $retval .= '<p>';
  $retval .= get_theme_mod('baum_name') . '<br>';
  $retval .= get_theme_mod('baum_position') . '<br>';
  $retval .= get_bloginfo('admin_email') . '</p>';
  return $retval; 
}); 

/** 
 * [baum_about] — About us (one paragraph) 
 * 
 */ 

 add_shortcode('baum_about', function () { 
  $retval = '<h6>About Us:</h6>'; 
  $retval .= '<p>' . get_theme_mod('baum_about') . '</p>'; 
  return $retval; 
}); 

/** 
 * [site_url] — Site URL shortcode 
 * 
 */ 

 add_shortcode('site_url', function () { 
  return home_url(); 
}); 

/** 
 * [baum_site_url] — Baum Site URL shortcode 
 * 
 */ 

 add_shortcode('baum_site_url', function () { 
  return home_url(); 
}); 

/** 
 * [sitename] — Site name shortcode 
 * 
 */ 

add_shortcode('sitename', function () { 
  return get_bloginfo(); 
}); 

/** 
 * [baum_sitename] — Site name shortcode 
 * 
 */ 

 add_shortcode('baum_sitename', function () { 
  return get_bloginfo(); 
}); 

/** 
 * [baum_title] — Content title shortcode 
 * 
 */ 

 add_shortcode('baum_title', function () { 
  if (is_archive()) {
    return single_term_title();
  } else {
    return get_the_title(); 
  }
});

/** 
 * [baum_name] — Admin name shortcode 
 * 
 */ 

 add_shortcode('baum_name', function () { 
  return get_theme_mod('baum_name');
});

/** 
 * [baum_email] — Admin email shortcode 
 * 
 */ 

 add_shortcode('baum_email', function () { 
  return get_theme_mod('baum_email');
});

/** 
 * [baum_position] — Admin position shortcode 
 * 
 */ 

 add_shortcode('baum_position', function () { 
  return get_theme_mod('baum_position');
});

/** 
 * [baum_your_address] — Admin address shortcode 
 * 
 */ 

 add_shortcode('baum_address', function () { 
  return get_theme_mod('baum_address');
});


/** 
 * [baum_user_roles] — Display the current user's roles
 * 
 */ 

add_shortcode('baum_user_roles', function () {
  $current_user = wp_get_current_user(); 

  // Check if a user is signed in 
  if ($current_user->ID == 0) return; 

  $roles = $current_user->roles; 

  if (!empty($roles)) { 
    return implode(', ', $roles); 
  } else { 
    return 'You have no roles assigned.'; 
  } 
}); 

/** 
 * [baum_square_thumbs] — Display latest posts' featured images together 
 * 
 */ 

function baum_square_thumbs_shortcode ($atts) {
  global $do_not_duplicate; 
  // Extract shortcode attributes with defaults
  $atts = shortcode_atts([
      'posts'      => 10, // Number of posts to display
      'omit'       => '', // Comma-separated list of square positions to omit
      'gaps'       => '5', // Gap between squares in pixels
  ], $atts, 'baum_square_thumbs');

  $posts_count = intval($atts['posts']);
  $omit_positions = array_map('intval', explode(',', $atts['omit']));
  $gap = intval($atts['gaps']);

  // Query the latest posts
  $query = new WP_Query([
      'post_type'      => 'post',
      'posts_per_page' => $posts_count,
      'no_found_rows'  => true,
      'post__not_in' => $do_not_duplicate, 
  ]);

  // Start building the grid HTML
  $output = '<div class="baum-square-thumbs-grid" style="--square-gap: ' . esc_attr($gap) . 'px;">';

  if ($query->have_posts()) {
      $index = 0;

      while ($query->have_posts()) {
          $query->the_post();
          $index++;

          $do_not_duplicate[] = get_the_ID(); 

          // Skip omitted positions
          if (in_array($index, $omit_positions)) {
              $output .= '<div class="baum-square-thumb omit"></div>';
              continue;
          }

          // Get the featured image
          if (has_post_thumbnail()) {
              $image_url = get_the_post_thumbnail_url(get_the_ID(), 'baum-square-thumb');

              $output .= '<div class="baum-square-thumb" style="background-image: url(' . esc_url($image_url) . ');"></div>';
          } else {
              // // Placeholder image
              // $image_url = 'https://via.placeholder.com/96'; 
          }

      }
  } else { 
      $output .= '<p>No posts found.</p>'; 
  } 

  $output .= '</div>';

  $output .= '<style>
    .baum-square-thumbs-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, 96px);
        gap: var(--square-gap, 5px);
        width: calc(96px * 6 + var(--square-gap, 5px) * 9); /* Adjust width based on the number of squares */
        margin: 0 auto;
        justify-content: center;
    }
    .baum-square-thumb {
        width: 96px;
        height: 96px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        border-radius: 5px; /* Optional for rounded corners */
    }
    .baum-square-thumb.omit {
        background: transparent;
    }
    </style>';

  // Restore original post data
  wp_reset_postdata();

  return $output;
}

add_shortcode('baum_square_thumbs', 'baum_square_thumbs_shortcode');

// 
// Register Shortcode for Media Playlist 
// 

function baum_media_playlist_shortcode ($atts) { 
  $atts = shortcode_atts([ 
      'id'   => '', 
      'slug' => '' 
  ], $atts); 

  if (empty($atts['id']) && empty($atts['slug'])) { 
      return; 
  } 

  $field = !empty($atts['id']) ? 'term_id' : 'slug';
  $terms = !empty($atts['id']) ? $atts['id'] : $atts['slug'];

  $args = [
    'post_type'      => 'attachment',
    'posts_per_page' => -1,
    'post_status'    => 'inherit', // Important for attachments
    'tax_query'      => [
      [
        'taxonomy' => 'playlist',
        'field'    => $field,
        'terms'    => $terms
      ]
    ]
  ];

  $query = new WP_Query($args);
  if (!$query->have_posts()) {
      return; 
  }

  // Get Playlist Info
  $playlist_term = get_term_by($field, $terms, 'playlist');
  $playlist_name = $playlist_term ? esc_html($playlist_term->name) : 'Unknown Playlist';
  $playlist_author_id = get_term_meta($playlist_term->term_id, 'playlist_author', true);
  $playlist_author = $playlist_author_id ? get_the_author_meta('display_name', $playlist_author_id) : 'Unknown Author';
  $total_videos = $query->post_count;

  // Get Query Parameters
  $loop = isset($_GET['loop']) ? (int) $_GET['loop'] : 0;
  $shuffle = isset($_GET['shuffle']) ? (int) $_GET['shuffle'] : 0;
  $current_index = isset($_GET['track']) ? (int) $_GET['track'] : 1;

  ob_start();
  ?>
  <div class='baum-card-container'>
      <div class='baum-media-playlist-container baum-card quaternary-bg'> 
          <div class='baum-title-width'>
              <h5 class='baum-heading tertiary-bg white' style='height:auto;font-size:18px;'>
                  <?= $playlist_name ?>
                  <p style='color:gray;font-size:12px;font-weight:700;'>
                      <?= $playlist_author ?> - <span id='current-video-index'><?= $current_index ?></span> / <?= $total_videos ?>
                  </p>
                  <a href='?loop=<?= $loop ? 0 : 1 ?>&shuffle=<?= $shuffle ?>' id='loop-button' class='playlist-button' title='Loop'>
                      <i class='fa-fw fa-solid fa-repeat <?= $loop ? "active" : "" ?>'></i>
                  </a>
                  <a href='?loop=<?= $loop ?>&shuffle=<?= $shuffle ? 0 : 1 ?>' id='shuffle-button' class='playlist-button' title='Shuffle'>
                      <i class='fa-fw fa-solid fa-shuffle <?= $shuffle ? "active" : "" ?>'></i>
                  </a>
                  <a href='#' class='playlist-button' title='Like'>
                      <i class='fa-fw fa-solid fa-heart'></i>
                  </a>
              </h5>
          </div>
          <div class='baum-media-playlist'>

  <?php
  $index = 1;
  while ($query->have_posts()) {
      $query->the_post();
      $mime_type = get_post_mime_type();
      $url       = wp_get_attachment_url();
      $title     = get_the_title();
      $author    = get_post_meta(get_the_ID(), '_wp_attachment_metadata', true)['artist'] ?? 'Unknown Artist';
      $attachment_url = get_permalink(get_the_ID());
      $is_active = ($index == $current_index) ? 'active' : '';

      if (strpos($mime_type, 'video') !== false || strpos($mime_type, 'audio') !== false) {
          // Get Featured Image or Default Black Box
          $thumb = get_the_post_thumbnail(get_the_ID(), [100, 0], ['style' => 'width:100px; height:auto;']);
          if (!$thumb) {
              $thumb = "<div style='width:100px;height:56px;background:black;border-radius:var(--border-radius);margin-bottom:5px;margin-top:5px;'></div>";
          }

          // Playlist Item
          echo "<div class='baum-media-item {$is_active}' data-index='{$index}' data-url='{$attachment_url}' style='display:grid;grid-template-columns:auto auto auto;'>
              <div style='font-weight:bold;margin:auto;margin-right:10px;'>{$index}</div>
              <div style='margin-right:10px;'>{$thumb}</div>
              <div style='font-size:13px;'>
                  <strong>{$title}</strong><br>
                  <small>By {$author}</small>
              </div>
          </div>";
          $index++;
      }
  }

  ?>
          </div>
      </div>

<?php 
  echo "<div style='border-top:0;' class='baum-card-bottom tertiary-bg'>"; 
  echo "<div class='baum-card-info text-left'><small class='quinary bold uppercase smaller' style='opacity:0.5;'></small></div>"; 
  echo "</div>";

  ?>

</div>

  <script>
      document.addEventListener("DOMContentLoaded", function () {
          let playlistItems = document.querySelectorAll(".baum-media-item");
          let currentIndex = <?= $current_index ?>;
          let totalTracks = <?= $total_videos ?>;
          let loopEnabled = <?= $loop ?>;
          let shuffleEnabled = <?= $shuffle ?>;

          function getNextTrackIndex() {
              if (shuffleEnabled) {
                  return Math.floor(Math.random() * totalTracks) + 1;
              }
              if (currentIndex < totalTracks) {
                  return currentIndex + 1;
              }
              return loopEnabled ? 1 : null;
          }

          function loadNextTrack() {
              let nextIndex = getNextTrackIndex();
              if (nextIndex) {
                  window.location.href = `?track=${nextIndex}&loop=${loopEnabled}&shuffle=${shuffleEnabled}`;
              }
          }

          playlistItems.forEach(item => {
              item.addEventListener("click", function () {
                  let trackIndex = this.getAttribute("data-index");
                  let trackUrl = this.getAttribute("data-url");
                  window.location.href = trackUrl + `?track=${trackIndex}&loop=${loopEnabled}&shuffle=${shuffleEnabled}`;
              });
          });

          let mediaElement = document.querySelector("video, audio");
          if (mediaElement) {
              mediaElement.addEventListener("ended", function () {
                  loadNextTrack();
              });
          }
      });
  </script>

  <?php
  wp_reset_postdata();
  return ob_get_clean();
}

add_shortcode('baum_media_playlist', 'baum_media_playlist_shortcode');





// 
// Register Shortcode for Media Playlist 
// 
function baum_email_lists_shortcode ($atts) {
  $atts = shortcode_atts([ 
    'user_id'   => '', 
    'size' => '', 
  ], $atts); 

  // Include The Newsletter Plugin's API
  if (!class_exists('Newsletter')) {
    require_once WP_PLUGIN_DIR . '/newsletter/includes/module.php';
  }

  // Fetch email lists
  $newsletter_subscription = new NewsletterSubscription();
  $email_lists = $newsletter_subscription->get_lists(); // Get all lists
  
  // Get the current user's subscriptions
  $user = $user_id ? get_user($user_id) : wp_get_current_user();
  $tnp_user = Newsletter::instance()->get_user($user->user_email); 
  $subscribed_lists = [];
  
  // Check which lists the user is subscribed to
  if ($tnp_user) {
    foreach (range(1, 40) as $list_id) { 
      $list_key = "list_{$list_id}";
      if (!empty($tnp_user->{$list_key}) && $tnp_user->{$list_key} == 1) {
        $subscribed_lists[] = $list_id;
      }
    }
  }
  ?>

    <div class='email-lists-container'>
      <!-- <h1>Email Lists</h1> -->
      <p style='margin:5px;'>
        <span>
          Select the email lists you'd like to subscribe to and click "Update Subscription" to save your preferences.
        </span>
      </p>
    </div>

    <div class='email-lists-grid' style='display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:28px;margin:10px;'>
      <?php foreach ($email_lists as $list) { ?>
        <?php 
          $is_subscribed = in_array($list->id, $subscribed_lists);

          // Fetch custom fields for the current list
          $custom_fields = get_option("tnp_list_{$list->id}_custom_fields", 
          [
            'description' => 'No description available.',
            'price' => 'Free',
            'frequency' => 'N/A',
            'tags' => '',
          ]);
        ?>
        <div class='email-list-box <?php echo $is_subscribed ? 'active' : ''; ?>' data-list-id='<?php echo esc_attr($list->id); ?>' style='border-radius:var(--border-radius);overflow:hidden;outline:2.5px solid var(--color-septenary);padding:15px;cursor:pointer;background-color: <?php echo $is_subscribed ? 'var(--color-septenary)' : 'var(--color-nonary)'; ?>; border:none;'>
          <div style='background:var(--color-secondary);border-bottom-right-radius:0;border-bottom-left-radius:0;margin:-20px;padding:20px;padding-bottom:5px;margin-bottom:15px;'>
          <!-- <label> -->
            <!-- Subscribe -->
            <h5 class='white'>
              <input type="checkbox" <?php echo $is_subscribed ? 'checked' : ''; ?> style="float:right;">
              <?php echo esc_html($list->name); ?>
            </h5>
          <!-- </label> -->
          </div>
          <!-- <p style='width:100%;height:28px;'>&nbsp;</p> -->
          <p style='font-size:14px;'>
            <?php echo esc_html($custom_fields['description']); ?>
          </p>
          <p>
            <strong>Frequency:</strong> <?php echo esc_html($custom_fields['frequency']); ?>
            <br>
            <strong>Price:</strong> <?php echo esc_html($custom_fields['price']); ?>
            <br>
            <!-- <strong>Tags:</strong> <?php // echo esc_html($custom_fields['tags']); ?> -->
          </p>
        </div>
      <?php } ?>
    </div>
    
    <div style='height:20px;width:100%;'>&nbsp;</div>

    <div style='display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:25px;'>
      <div>
        <h3 style='margin-top:28px;' class='right' >Your Email Address: </h3>
      </div> 
      <div class='baum-width-100'>
        <input type='email' value='<?php echo $user->user_email; ?>' class='h6 baum-width-100' style='display:block;font-family:sans-serif;text-transform:lowercase;font-size:16px;margin:25px 0 !important;' /> 
      </div>
      <div>
        <button id='update-subscriptions' class='left' disabled style='margin-top:20px;margin-bottom:20px;padding:10px 20px;background:var(--color-primary);color:#fff;border:none;border-radius:var(--border-radius);cursor:not-allowed;height:49px;line-height:28px; font-size:14px;'>Update Subscription</button>
      </div> 
    </div>

    <div style='height:20px;width:100%;'>&nbsp;</div>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        const boxes = document.querySelectorAll('.email-list-box');
        const updateButton = document.getElementById('update-subscriptions');

        let originalState = {}; // To track the initial subscription state

        // Initialize box state and track the original subscription state
        function initializeBoxState() {
          boxes.forEach(box => {
            const checkbox = box.querySelector('input[type="checkbox"]');
            originalState[box.dataset.listId] = checkbox.checked; // Save initial state
            updateBoxState(box);
          });
          updateButtonState(); // Initialize button state
        }

        // Function to update box styles based on checkbox state
        function updateBoxState(box) {
          const checkbox = box.querySelector('input[type="checkbox"]');
          if (checkbox.checked) {
            box.classList.add('active');
            box.style.backgroundColor = 'var(--color-septenary)';
          } else {
            box.classList.remove('active');
            box.style.backgroundColor = 'var(--color-nonary)';
          }
        }

        // Function to check if the subscription state has changed
        function hasStateChanged() {
          return Array.from(boxes).some(box => {
            const checkbox = box.querySelector('input[type="checkbox"]');
            return checkbox.checked !== originalState[box.dataset.listId];
          });
        }

        // Function to update the submit button's state
        function updateButtonState() {
          const stateChanged = hasStateChanged();
          updateButton.disabled = !stateChanged;
          updateButton.style.cursor = stateChanged ? 'pointer' : 'not-allowed';
        }

        // Add event listeners to boxes
        boxes.forEach(box => {
          const checkbox = box.querySelector('input[type="checkbox"]');

          // Handle box clicks
          box.addEventListener('click', (e) => {
            if (e.target !== checkbox) {
              checkbox.checked = !checkbox.checked; // Toggle checkbox
            }
            updateBoxState(box); // Update box styles
            updateButtonState(); // Update button state
          });

          // Handle checkbox clicks directly
          checkbox.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent box click event
            updateBoxState(box); // Update box styles
            updateButtonState(); // Update button state
          });
        });

        // Handle the Update Subscription button click
        updateButton.addEventListener('click', function () {
          const selectedLists = Array.from(boxes)
            .filter(box => box.querySelector('input[type="checkbox"]').checked)
            .map(box => box.dataset.listId);

          // AJAX call to update subscriptions
          fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
              action: 'update_user_subscriptions',
              user_id: <?php echo esc_js($user->ID); ?>,
              lists: JSON.stringify(selectedLists),
            })
          })
          .then(response => response.json())
          .then(data => {
            // baum_notify('Response: ' + JSON.stringify(data));
            if (data.success) {
              // alert(data.message || "Subscriptions updated successfully!");
              baum_notify('Subscriptions updated successfully!');
              initializeBoxState(); // Reset original state after successful update
            } else {
              // alert(data.error || "Failed to update subscriptions.");
              baum_notify('Failed to update subscriptions. Try again later.');
            }
          })
          .catch(error => {
            baum_notify('Error updating subscriptions. Try again later.');
            console.error("Error updating subscriptions:", error);
          }); 

          // Initialize the box states and button
          initializeBoxState();
        });
      });    
    </script>
  <?php
}

add_shortcode('baum_email_lists', 'baum_email_lists_shortcode');

// 
// 
// 

function baum_attachment_links_shortcode($atts) {
  // Get shortcode attributes: Use current post ID if no attachment_id is provided
  $atts = shortcode_atts([
      'attachment_id' => get_the_ID() // Default to current attachment in loop
  ], $atts);

  $attachment_id = intval($atts['attachment_id']);

  if (!$attachment_id) {
      return "<p>No attachment specified.</p>";
  }

  // Get the attachment URL
  $attachment_url = wp_get_attachment_url($attachment_id);

  if (!$attachment_url) {
      return "<p>Invalid attachment.</p>";
  }

  // Debugging: Log attachment details
  error_log("DEBUG: Attachment ID: " . $attachment_id);
  error_log("DEBUG: Attachment URL: " . $attachment_url);

  // Initialize meta_query array
  $meta_query = [
      'relation' => 'OR',
      [
          'key'     => '_thumbnail_id', // Standard WP featured image reference
          'value'   => $attachment_id,
          'compare' => '='
      ],
      [
          'key'     => 'multiple_featured_images', // Custom meta field (array)
          'value'   => '"' . $attachment_id . '"', // Serialized array search (ACF stores it like this)
          'compare' => 'LIKE'
      ]
  ];

  // **Dynamically add all ACF fields that could contain the attachment**
  global $wpdb;
  $acf_fields = $wpdb->get_col($wpdb->prepare(
      "SELECT DISTINCT meta_key FROM $wpdb->postmeta WHERE meta_value = %s",
      $attachment_id
  ));

  if (!empty($acf_fields)) {
      foreach ($acf_fields as $acf_field) {
          $meta_query[] = [
              'key'     => $acf_field,
              'value'   => $attachment_id,
              'compare' => '='
          ];
      }
      error_log("DEBUG: Found ACF fields using attachment ID: " . implode(", ", $acf_fields));
  } else {
      error_log("DEBUG: No ACF fields found for attachment ID " . $attachment_id);
  }

  // Query for posts where the attachment is used
  $args = [
      'post_type'      => 'any', // Get all post types
      'posts_per_page' => -1,
      'meta_query'     => $meta_query,
  ];

  // **Manually search post content using a separate query**
  $content_query = new WP_Query([
      'post_type'      => 'any',
      'posts_per_page' => -1,
      's'              => $attachment_url, // Search for attachment URL in post_content
      'fields'         => 'ids', // Get only post IDs to avoid large queries
  ]);

  if (!empty($content_query->posts)) {
      $args['post__in'] = $content_query->posts; // Merge with main query
  }

  // Execute the main query
  $attached_posts = new WP_Query($args);

  // Debugging: Log the entire WP_Query object
  error_log("DEBUG: WP_Query object: " . print_r($attached_posts, true));
  error_log("DEBUG: Found " . $attached_posts->found_posts . " posts");

  // Start output buffering
  ob_start();

  ?>

  <div class='baum-title-width' style='margin:15px 0;'> 
      <h5 class='baum-heading'>
          Uploaded To
      </h5> 
  </div>

  <?php

  if ($attached_posts->have_posts()) :
      echo '<div class="baum-card-mini-container" style="margin:-5px;">';
      while ($attached_posts->have_posts()) : $attached_posts->the_post();
          
          // Debugging: Log each found post before displaying
          error_log("DEBUG: Found post ID: " . get_the_ID() . " - Title: " . get_the_title());

          // Check if the template part actually exists before calling it
          if (locate_template('parts/baum-card.php')) {
              get_template_part('parts/baum', 'card', ['size' => 'mini']);
          } else {
              error_log("ERROR: parts/baum-card.php not found!");
          }

      endwhile;
      echo '</div>';
      wp_reset_postdata();
  else :
      echo "<p style='padding:10px;'>No posts or pages use this attachment.</p>";
  endif;

  return ob_get_clean(); // Return the buffered output
}

add_shortcode('baum_attachment_links', 'baum_attachment_links_shortcode');








function baum_attachment_links_shortcode_old ($atts) {
  // Get shortcode attributes: Use current post ID if no attachment_id is provided
  $atts = shortcode_atts([
      'attachment_id' => get_the_ID() // Default to current attachment in loop
  ], $atts);

  $attachment_id = intval($atts['attachment_id']);

  if (!$attachment_id) {
      return "<p>No attachment specified.</p>";
  }

  // Get the attachment URL
  $attachment_url = wp_get_attachment_url($attachment_id);

  if (!$attachment_url) {
      return "<p>Invalid attachment.</p>";
  }

  // Debugging: Log attachment details
  error_log("DEBUG: Attachment ID: " . $attachment_id);
  error_log("DEBUG: Attachment URL: " . $attachment_url);

  // Query for posts where the attachment is:
  // 1. Used in post_content (content editor)
  // 2. Used in ACF fields (post meta)
  // 3. Used as an attachment metadata reference
  $args = [
      'post_type'      => 'any', // Get all post types
      'posts_per_page' => -1,
      'meta_query'     => [
          'relation' => 'OR',
          [
              'key'     => '_wp_attached_file', // Standard WP attachment reference
              'value'   => $attachment_id,
              'compare' => '='
          ],
          [
              'key'     => '_wp_attachment_metadata', // Metadata reference
              'value'   => $attachment_id,
              'compare' => '='
          ],
          // [
          //     'key'     => '', // ACF field check (dynamic)
          //     'value'   => $attachment_id,
          //     'compare' => '='
          // ]
      ],
      // 's' => $attachment_url, // Look for the attachment URL in post_content
  ];

  // **Dynamically add all ACF fields that could contain the attachment**
  global $wpdb;
  $acf_fields = $wpdb->get_col("SELECT DISTINCT meta_key FROM $wpdb->postmeta WHERE meta_value = '{$attachment_id}'");

  if (!empty($acf_fields)) {
      foreach ($acf_fields as $acf_field) {
          $args['meta_query'][] = [
              'key'     => $acf_field,
              'value'   => $attachment_id,
              'compare' => '='
          ];
      }
      error_log("DEBUG: Found ACF fields using attachment ID: " . implode(", ", $acf_fields));
  } else {
      error_log("DEBUG: No ACF fields found for attachment ID " . $attachment_id);
  }

  // Execute the query
  $attached_posts = new WP_Query($args);

  // Debugging: Log the entire WP_Query object
  error_log("DEBUG: WP_Query object: " . print_r($attached_posts, true));
  error_log("DEBUG: Found " . $attached_posts->found_posts . " posts");

  // Start output buffering
  ob_start();

  ?>

  <div class='baum-title-width' style='margin:15px 0;'> 
    <h5 class='baum-heading <?php echo $heading_bg_color; ?> <?php echo $heading_text_color; ?>'>
      Uploaded To
      <?php // the_title(); ?> 
    </h5> 
  </div>

  <?php

  if ($attached_posts->have_posts()) :
      echo '<div class="baum-card-mini-container" style="margin:-5px;">';
      while ($attached_posts->have_posts()) : $attached_posts->the_post();
          
          // Debugging: Log each found post before displaying
          error_log("DEBUG: Found post ID: " . get_the_ID() . " - Title: " . get_the_title());

          // Check if the template part actually exists before calling it
          if (locate_template('parts/baum-card.php')) {
              get_template_part('parts/baum', 'card', ['size' => 'mini']);
          } else {
              error_log("ERROR: parts/baum-card.php not found!");
          }

      endwhile;
      echo '</div>';
      wp_reset_postdata();
  else :
      echo "<p style='padding:10px;'>No posts or pages use this attachment.</p>";
  endif;

  return ob_get_clean(); // Return the buffered output
}

// add_shortcode('baum_attachment_links', 'baum_attachment_links_shortcode');









// <div class='baum-box'>.baum-box</div>

// <div class='baum-grid-4'>

//   <div class='baum-box-container'>
//     <div class='baum-box'>.baum-box-container .baum-box</div>
//   </div>

//   <div class='baum-box-container'>
//     <div class='baum-box'>.baum-box</div>
//     <div class='baum-box-bottom'>.baum-box-bottom</div>
//   </div>

//   <div class='baum-box-container'>
//     <div class='baum-box-heading'>.baum-box-heading</div>
//     <div class='baum-box'>.baum-box</div>
//   </div>

//   <div class='baum-box-container'>
//     <div class='baum-box-heading'>.baum-box-heading</div>
//     <div class='baum-box'>.baum-box</div>
//     <div class='baum-box-bottom'>.baum-box-bottom</div>
//   </div>

//   <div class='baum-box-container'>
//     <div class='baum-box-heading'>.baum-box-heading</div>
//     <div class='baum-box'>
//       .baum-box
//       <div class='baum-box-body'>.baum-box .baum-box-body</div>
//     </div>
//     <div class='baum-box-bottom'>.baum-box-bottom</div>
//   </div>

// </div>

// <div class='baum-box'><p>.baum-box > p</p></div>


// <div class='baum-grid-5'>

//   <div class='baum-box-container'>
//     <div class='baum-box'><p>.baum-box-container .baum-box > p</p></div>
//   </div>

//   <div class='baum-box-container'>
//     <div class='baum-box'><p>.baum-box > p</p></div>
//     <div class='baum-box-bottom'><p>.baum-box-bottom > p</p></div>
//   </div>

//   <div class='baum-box-container'>
//     <div class='baum-box-heading'><p>.baum-box-heading > p</p></div>
//     <div class='baum-box'><p>.baum-box > p</p></div>
//   </div>

//   <div class='baum-box-container'>
//     <div class='baum-box-heading'><p>.baum-box-heading > p</p></div>
//     <div class='baum-box'><p>.baum-box > p</p></div>
//     <div class='baum-box-bottom'><p>.baum-box-bottom > p</p></div>
//   </div>

//   <div class='baum-box-container'>
//     <div class='baum-box-heading'><p>.baum-box-heading > p</p></div>
//     <div class='baum-box'>
//       <p>.baum-box > p</p>
//       <div class='baum-box-body'><p>.baum-box .baum-box-body > p</p></div>
//     </div>
//     <div class='baum-box-bottom'><p>.baum-box-bottom > p</p></div>
//   </div>

// </div>


// 
// 
// 

function baum_darkmode_toggle_shortcode () {
  ob_start();
  ?>
  <style>

    .baum-dark-toggle {
      display: inline-flex;
      align-items: center;
      background: var(--color-tertiary);
      border-radius: var(--border-radius);
      padding: 0px;
      gap: 5px;
      font-size: 11px;
      user-select: none;
      position: relative; 
      top: -1.5px; 
      margin-left:5px;
      margin-right:5px;
    }
    .baum-dark-toggle {
      display: inline-flex;
      align-items: center;
      background: var(--color-quaternary);
      border-radius: var(--border-radius-small);
      padding: 0px 2.5px;
      gap: 5px;
      font-size: 11px;
      user-select: none;
      position: relative;
      top: 0.5px;
      margin-left: 5px;
      margin-right: 5px;
    }


    .baum-dark-toggle .toggle-option {
      /* padding: 6px 12px; */
      padding: 0px 7.5px;
      border-radius: var(--border-radius-small);
      background: transparent;
      color: var(--color-senary);
      transition: background 0.2s, color 0.2s;
      cursor: pointer;
    }

    .baum-dark-toggle .toggle-option.active {
      /* background: var(--color-quinary); */
      color: white;
    }

    .baum-dark-toggle i {
      margin-right: 0px;
    }

  </style>

  <div class='baum-dark-toggle' role="group" aria-label="Theme toggle">
    <div class="toggle-option" data-mode="light" title='Light'><i class="fa-solid fa-sun"></i></div>
    <div class="toggle-option" data-mode="auto" title='Auto'><i class="fa-solid fa-circle-half-stroke"></i></div>
    <div class="toggle-option" data-mode="dark" title='Dark'><i class="fa-solid fa-moon"></i></div>
  </div>

  <script>
    (function () {
      const options = document.querySelectorAll('.baum-dark-toggle .toggle-option');

      function applyMode(mode) {
        document.body.classList.remove('dark');
        if (mode === 'dark') document.body.classList.add('dark');
        else if (mode === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches) {
          document.body.classList.add('dark');
        }
        localStorage.setItem('darkMode', mode);
        options.forEach(opt => opt.classList.remove('active'));
        const active = document.querySelector(`.baum-dark-toggle .toggle-option[data-mode="${mode}"]`);
        if (active) active.classList.add('active');
      }

      options.forEach(option => {
        option.addEventListener('click', () => {
          const mode = option.getAttribute('data-mode');
          applyMode(mode);
        });
      });

      const savedMode = localStorage.getItem('darkMode') || 'auto';
      applyMode(savedMode);

      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
        if (localStorage.getItem('darkMode') === 'auto') applyMode('auto');
      });
    })();
  </script>
  <?php
  return ob_get_clean();
}

add_shortcode('baum_darkmode_toggle', 'baum_darkmode_toggle_shortcode');





































// Register shortcode [baum_user_tooltip id=USER_ID]
function baum_user_tooltip_shortcode($atts) {
  $atts = shortcode_atts([
      'id' => get_current_user_id(),
      'size' => 32
  ], $atts, 'baum_user_tooltip');

  $user_id = intval($atts['id']);
  if (!$user_id) return '';

  $user = get_userdata($user_id);
  if (!$user) return '';

  $display_name = esc_html($user->display_name);
  $email = esc_html($user->user_email);
  $roles = implode(', ', $user->roles);
  $since = date('F j, Y', strtotime($user->user_registered));
  $bio = esc_html(get_user_meta($user_id, 'description', true));
  $profile_url = get_author_posts_url($user_id);

  // You must implement these according to your site’s data
  $followers = (int) get_user_meta($user_id, 'followers_count', true);
  $xp = (int) get_user_meta($user_id, 'experience_points', true);
  $trophies = function_exists('gamipress_count_user_earned_achievements') 
      ? gamipress_count_user_earned_achievements($user_id) : 0;

  $article_count = count_user_posts($user_id, 'post');
  $online = get_user_meta($user_id, 'is_online', true) ? '🟢' : '⚪';

  ob_start();
  ?>
  <span class="baum-user-tooltip-trigger" data-tooltip-content="#baum-user-tooltip-<?php echo $user_id; ?>">
      <?php echo get_avatar($user_id, $atts['size']); ?>
  </span>
  <div class="tooltip_templates" style="display:none; text-transform: initial !important; font-weight: 600; color: var(--color-denary);">
      <span id="baum-user-tooltip-<?php echo $user_id; ?>">
          <div class="baum-user-tooltip-card" style="max-width: 280px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; padding: 12px;">
              <div style="display: flex; align-items: center; margin-bottom: 10px;">
                  <div style="margin-right: 12px;">
                      <?php echo get_avatar($user_id, 64, '', '', ['style' => 'border-radius: 50%;']); ?>
                  </div>
                  <div style="flex: 1;">
                      <div style="font-size: 16px; font-weight: 600; line-height: 1.2; text-transform: initial !important;">
                          <?php echo $display_name; ?> <small style="font-size:9px;"><?php echo $online; ?></small>
                      </div>
                      <div style="font-size: 11px; color: #888; margin-top: 2px;">
                          <?php echo $roles; ?>
                      </div>
                  </div>
              </div>

              <div style="display:grid;grid-template-columns:1fr 1fr;font-size:11px;line-height:1.6;font-weight:600;">
                  <div><?php echo $article_count; ?> Posts</div>
                  <div><?php echo $trophies; ?> Trophies</div>
                  <div><?php echo $xp; ?> XP</div>
                  <div><?php echo $followers; ?> Followers</div>
              </div>

              <?php if ($bio): ?>
                  <div style="margin-top: 10px; font-size: 11px; font-style: italic; text-transform: initial !important; font-weight: 600; color: var(--color-denary);">
                      “<?php echo wp_trim_words($bio, 20); ?>”
                  </div>
              <?php endif; ?>

              <div style="margin-top: 10px; display: flex; justify-content: space-between; align-items: center;">
                  <div>
                      <a style="font-size: 9px; color:var(--color-primary); text-decoration:none; font-weight:800;" href="mailto:<?php echo $email; ?>"><?php echo $email; ?></a>
                  </div>
                  <div style="display: flex; gap: 6px;">
                      <a href="<?php echo esc_url($profile_url); ?>" class="button baum-button baum-button-primary" style="font-size: 11px; padding: 4px 8px;">
                          View
                      </a>
                      <a href="#" class="button baum-button baum-button-primary" style="font-size: 11px; padding: 4px 8px;">
                          Follow
                      </a>
                  </div>
              </div>
          </div>
          <div style='text-align:center;color:#777;background:#444;width:100%;border-radius:var(--border-radius-small);margin-bottom:10px;'>Since <?php echo $since; ?></div>
      </span>
  </div>

  <?php
  return ob_get_clean();
}
add_shortcode('baum_user_tooltip', 'baum_user_tooltip_shortcode');





