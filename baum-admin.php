<?php
/* 
Plugin Name: <PERSON><PERSON>'s Admin 
Description: Comprehensive admin customization plugin. 
Version: 1.0 
Author: <PERSON> 
*/ 

require_once plugin_dir_path(__FILE__) . 'edit-profile.php'; 
require_once plugin_dir_path(__FILE__) . 'settings.php'; 

class BaumAdmin { 

  public function __construct () { 
    add_action('admin_init', [$this, 'admin_customizations']); 
    add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_styles']); 
  } 

  public function register_admin_color_scheme () { 
    wp_admin_css_color( 
        'baum_scheme', 
        __('Baum Color Scheme', 'baum'), 
        plugin_dir_url(__FILE__) . 'assets/baum-scheme.css', 
        [ 
          '#b4b4b4', 
          '#bc0c0c', 
          '#ffffff', 
          '#4b4b4b' 
        ] 
    ); 
  } 

  public function admin_customizations () { 
    // Remove unwanted dashboard widgets, menus, etc. 
    // Example: 
    remove_meta_box('dashboard_quick_press', 'dashboard', 'side'); 
  } 

  public function enqueue_admin_styles () { 
    wp_enqueue_style( 
      'baum-admin-scheme', 
      plugin_dir_url(__FILE__) . 'assets/baum-scheme.css' 
    ); 
    wp_enqueue_style( 
      'baum-admin-style', 
      plugin_dir_url(__FILE__) . 'assets/admin-style.css' 
    ); 
    wp_enqueue_script( 
      'baum-admin-script', 
      plugin_dir_url(__FILE__) . 'assets/admin-script.js', 
      ['jquery'], 
      null, 
      true 
    ); 
  } 

  public function set_default_admin_color ($user_id) { 
    $args = ['ID' => $user_id, 'admin_color' => 'baum_scheme']; 
    wp_update_user($args); 
  } 

  public function force_admin_color_scheme () { 
    $user_id = get_current_user_id(); 
    $args = ['ID' => $user_id, 'admin_color' => 'baum_scheme']; 
    wp_update_user($args); 
  } 

} 

new BaumAdmin(); 







///////////////////////////////////// 
// Add the User Avatar to the Admin Menu 
///////////////////////////////////// 

function baum_admin_style () { 
  $current_user = wp_get_current_user();
  $user_avatar = get_avatar($current_user->ID, 42); // Increase avatar size
  // $user_name = trim($current_user->user_firstname . ' ' . $current_user->user_lastname); 
  // $user_initials = ''; 

  // if (empty($user_name)) { 
    // $user_name = $current_user->user_login; 
    // $user_initials = strtoupper($user_name[0]); 
  // } else { 
    // $user_initials = strtoupper($current_user->user_firstname[0] . $current_user->user_lastname[0]); 
  // } 

  // $avatar_html = $user_avatar; 
  // if (!$user_avatar) { 
    // $avatar_html = '<div class="user-initials">' . $user_initials . '</div>'; 
  // } 
  ?> 
  <li id='baum-user-info' style='display:grid;grid-template-columns:48px auto;grid-gap:5px;padding-left:5px;padding-right:5px;'>

    <div class='baum-user-avatar'>
      <?php echo $user_avatar; ?>
    </div>

    <div class='baum-user-fullname'>
      <?php echo $current_user->user_firstname . ' ' . $current_user->user_lastname; ?>
      <small class='gray smaller'>
        <?php echo $current_user->user_email; ?>
      </small>
    </div>

    <!-- <div class='baum-user-buttons'>
      <a title='Edit Profile' href='<?php // echo admin_url('profile.php'); ?>'>
        <i class="fas fw fa-user"></i>
      </a>
      <a title='Baum Contact Center' href='<?php // echo admin_url('baum-webmail.php'); ?>'>
        <i class="fas fw fa-envelope"></i>
      </a>
      <a title='Sign Out' href='<?php // echo wp_logout_url(); ?>'>
        <i class='fas fw fa-power-off'></i>
      </a>
    </div> -->

  </li>
  <script>
    (function ($) {
      $(document).ready(function () {
        $('#baum-user-info').prependTo('#adminmenu');
        $('#baum-user-info').show(); 
      });
    })(jQuery);
  </script>
  <?php 
}

add_action('admin_footer', 'baum_admin_style');  








































add_action('admin_head', function () {
  ?>
  <style>
      .notice {
          position: relative;
          padding-left: 12.5px !important;
          border-left: 50px solid transparent;
          box-shadow: none !important;
      }

      /* Icons */
      .notice::before {
          font-family: "Font Awesome 6 Free";
          font-weight: 900;
          position: absolute;
          left: -35px;
          top: 7.5px;
          font-size: 1.5em;
      }

      /* Success = Green */
      .notice-success {
          border-left-color: var(--color-green);
      }
      .notice-success::before {
          content: "\f058"; /* fas fa-check-circle */
          color: var(--color-white);
      }

      /* Warning = Orange */
      .notice-warning,
      .notice.notice-alt {
          border-left-color: var(--color-orange);
      }
      .notice-warning::before,
      .notice.notice-alt::before {
          content: "\f071"; /* fas fa-exclamation-triangle */
          color: var(--color-white);
      }

      /* Error = Red (fallback if no var set) */
      .notice-error {
          border-left-color: var(--color-red);
      }
      .notice-error::before {
          content: "\f057"; /* fas fa-times-circle */
          color: var(--color-white);
      }

      /* Info = Blue or fallback */
      .notice-info {
          border-left-color: var(--color-blue);
      }
      .notice-info::before {
          content: "\f05a"; /* fas fa-info-circle */
          color: var(--color-white);
      }
  </style>
  <?php
});
























// === ADMIN HEAD: Add styles, view toggle, and suppress table ===
add_action('admin_head-edit.php', function () {
  global $typenow;
  if ($typenow !== 'post') return;

  $view = $_GET['view'] ?? 'list';
  $grid_url = add_query_arg('view', 'card');
  $list_url = remove_query_arg('view');

  echo '<style>
  select[name="cat"], 
  select[name="post_format"], 
  #filter-by-date, 
  #post-query-submit {
      display: none !important;
  }
 </style>';

  echo '<div class="baum-view-toggle">';
    echo '<a href="' . esc_url($list_url) . '" class="' . ($view === 'card' ? '' : 'active') . '">List ☰</a>';
    echo '<a href="' . esc_url($grid_url) . '" class="' . ($view === 'card' ? 'active' : '') . '">Card ▦</a>';
  echo '</div>';

});









add_action('restrict_manage_posts', 'baum_stories_custom_topbar');
function baum_stories_custom_topbar($post_type) {
  if ($post_type !== 'post' || ($_GET['view'] ?? '') !== 'card') return;

  $tabs = [
      'all' => 'All',
      'published' => 'Published',
      'draft' => 'Drafts',
      'private' => 'Private'
  ];
  $active_tab = $_GET['tab'] ?? 'all';

  // $selected_topic = $_GET['story_topic'] ?? '';
  // $selected_tag = $_GET['story_tag'] ?? '';
  $selected_date = $_GET['m'] ?? '';
  $search_term = $_GET['s'] ?? '';

  echo '<div class="wp-filter">';

  // === Tabs ===
  echo '<ul class="filter-links">';
  foreach ($tabs as $slug => $label) {
      $url = add_query_arg(['tab' => $slug, 'view' => 'card']);
      $class = ($active_tab === $slug) ? 'current' : '';
      echo '<li class="baum-tab-' . esc_attr($slug) . '">';
      echo '<a href="' . esc_url($url) . '" class="' . $class . '" aria-current="' . ($class ? 'page' : 'false') . '">' . esc_html($label) . '</a>';
      echo '</li>';
  }
  echo '</ul>';

  // === Filters & Search ===
  echo '<form class="search-form baum-filter-form" method="get">';
  echo '<input type="hidden" name="post_type" value="post">';
  echo '<input type="hidden" name="view" value="card">';
  echo '<input type="hidden" name="tab" value="' . esc_attr($active_tab) . '">';

  // Search
  echo '<label for="baum-search" class="screen-reader-text">Search Stories</label>';
  echo '<input type="search" name="s" id="baum-search" class="wp-filter-search" placeholder="Search Stories…" value="' . esc_attr($search_term) . '" />';


  // echo '<input type="submit" class="button" value="Filter" />';
  echo '</form>';
  echo '</div>';
}



// === RENDER CARD VIEW ===
add_action('restrict_manage_posts', function () {
global $typenow;
if ($typenow !== 'post' || ($_GET['view'] ?? '') !== 'card') return;

$query_args = [
    'post_type' => 'post',
    'posts_per_page' => 50,
    'post_status' => 'any',
];

if (!empty($_GET['s'])) {
    $query_args['s'] = sanitize_text_field($_GET['s']);
}

// Support topic (category), tag, and date filtering
if (!empty($_GET['story_topic'])) {
    $query_args['cat'] = intval($_GET['story_topic']);
}

if (!empty($_GET['story_tag'])) {
    $query_args['tag'] = sanitize_text_field($_GET['story_tag']);
}

if (!empty($_GET['m'])) { 
    $query_args['m'] = preg_replace('/[^0-9]/', '', $_GET['m']); 
} 

?>

<style> 

.search-box, 
.subsubsub, 
.bulkactions, 
.tablenav.bottom, 
.tablenav-pages, 
.table-view-list { 
  display: none !important; 
}

</style>





<?php 

$recent = new WP_Query($query_args);

echo '<div class="baum-cards baum-cards-small">'; 


while ($recent->have_posts()) {
  $recent->the_post(); 
  $do_not_duplicate[] = $post->ID; 
// foreach ($posts as $post) {
    // setup_postdata($post);



    get_template_part('parts/baum', 'card', array(
      'size' => 'small', 
      'fav_btn' => false, 
      // 'backend' => true, 
      // 'post' => $post, 
    )); 

    // $thumb = get_the_post_thumbnail_url($post, 'medium') ?: 'https://via.placeholder.com/400x300?text=No+Image';

// echo '<div class="baum-card-container">';
//     echo '<div class="baum-card">';
//     echo '<a href="' . get_edit_post_link($post->ID) . '">';
//     echo '<img src="' . esc_url($thumb) . '" alt="' . esc_attr(get_the_title($post)) . '">';
//     echo '<h2>' . esc_html(get_the_title($post)) . '</h2>';
//     echo '<p class="meta">By ' . esc_html(get_the_author_meta('display_name', $post->post_author)) . '</p>';
//     echo '</a>';
//     echo '<div class="quick-actions" style="display: flex; justify-content: space-around; padding: 8px 0;">';
//     echo '<a href="' . get_edit_post_link($post->ID) . '" class="button">Edit</a>';
//     echo '<a href="' . get_permalink($post->ID) . '" class="button" target="_blank">View</a>';
//     echo '<a href="' . get_delete_post_link($post->ID) . '" class="button delete-post">Delete</a>';
//     echo '</div>';
//     echo '</div>';
//     echo '</div>';
}
echo '</div>';
// wp_reset_postdata();
});




// add_filter('months_dropdown_results', '__return_empty_array'); // Remove All Dates dropdown
// add_filter('disable_categories_dropdown', '__return_true'); // Custom filter we'll define
// add_filter('disable_post_formats_dropdown', '__return_true'); // Custom filter we'll define






























// // === APPLY FILTER QUERY VARS ===
// add_action('pre_get_posts', 'baum_filter_stories_query');
// function baum_filter_stories_query($query) {
//     if (!is_admin() || !$query->is_main_query()) return;

//     if ($query->get('post_type') === 'post') {
//         if (!empty($_GET['story_topic'])) {
//             $query->set('cat', intval($_GET['story_topic']));
//         }

//         if (!empty($_GET['story_tag'])) {
//             $query->set('tag', sanitize_text_field($_GET['story_tag']));
//         }

//         if (!empty($_GET['m'])) {
//             $query->set('m', preg_replace('/[^0-9]/', '', $_GET['m']));
//         }
//     }
// }













// === SAVE FILTER SETTINGS TO localStorage ===
add_action('admin_footer-edit.php', function () {
global $typenow;
if ($typenow !== 'post') return;
?>
<script>
(function(){
    const form = document.getElementById('posts-filter');
    const STORAGE_KEY = 'baum_stories_filters';

    // Save on change
    form.addEventListener('change', () => {
        const data = {};
        ['story_topic', 'story_tag', 'm', 's'].forEach(id => {
            const el = form.querySelector(`[name="${id}"]`);
            if (el) data[id] = el.value;
        });
        localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    });

    // Restore on load
    document.addEventListener('DOMContentLoaded', () => {
        const saved = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');
        Object.entries(saved).forEach(([key, value]) => {
            const el = form.querySelector(`[name="${key}"]`);
            if (el) el.value = value;
        });
    });
})();
</script>
<?php
});







